package com.app.wordifynumbers.accessibility

import androidx.compose.foundation.clickable
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.AnnotatedString
import android.content.Context
import android.view.accessibility.AccessibilityManager as AndroidAccessibilityManager

/**
 * Accessibility manager for ensuring the app is fully accessible
 * and compliant with Android accessibility guidelines and WCAG standards.
 */
object AccessibilityManager {
    
    /**
     * Check if accessibility services are enabled
     */
    fun isAccessibilityEnabled(context: Context): Bo<PERSON>an {
        val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AndroidAccessibilityManager
        return accessibilityManager.isEnabled
    }
    
    /**
     * Check if TalkBack is enabled
     */
    fun isTalkBackEnabled(context: Context): Boolean {
        val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AndroidAccessibilityManager
        return accessibilityManager.isTouchExplorationEnabled
    }
    
    /**
     * Get recommended minimum touch target size
     */
    fun getMinimumTouchTargetSize(): Int = 48 // dp - Android accessibility guidelines
    
    /**
     * Content descriptions for common UI elements
     */
    object ContentDescriptions {
        const val CALCULATOR_BUTTON = "Calculator button"
        const val NUMBER_INPUT = "Number input field"
        const val RESULT_DISPLAY = "Calculation result"
        const val NAVIGATION_TAB = "Navigation tab"
        const val BACK_BUTTON = "Navigate back"
        const val INFO_BUTTON = "Show information"
        const val SETTINGS_BUTTON = "Open settings"
        const val CLEAR_BUTTON = "Clear input"
        const val COPY_BUTTON = "Copy to clipboard"
        const val SHARE_BUTTON = "Share result"
        const val LANGUAGE_SELECTOR = "Select language"
        const val CURRENCY_SELECTOR = "Select currency"
        const val DATE_PICKER = "Select date"
        const val DROPDOWN_MENU = "Dropdown menu"
        const val CLOSE_DIALOG = "Close dialog"
        const val CONFIRM_ACTION = "Confirm action"
        const val CANCEL_ACTION = "Cancel action"
    }
    
    /**
     * Semantic roles for different UI elements
     */
    object SemanticRoles {
        val CALCULATOR_BUTTON = Role.Button
        val INPUT_FIELD = Role.Button // Use Button role for input fields in Compose
        val RESULT_TEXT = Role.Button // Use Button role for text elements
        val NAVIGATION_TAB = Role.Tab
        val DROPDOWN = Role.Button // Use Button role for dropdowns
        val DIALOG = Role.Button // Use Button role for dialogs
        val CHECKBOX = Role.Checkbox
        val RADIO_BUTTON = Role.RadioButton
        val SWITCH = Role.Switch
    }
    
    /**
     * State descriptions for dynamic content
     */
    object StateDescriptions {
        const val LOADING = "Loading"
        const val ERROR = "Error occurred"
        const val SUCCESS = "Operation successful"
        const val SELECTED = "Selected"
        const val UNSELECTED = "Not selected"
        const val EXPANDED = "Expanded"
        const val COLLAPSED = "Collapsed"
        const val ENABLED = "Enabled"
        const val DISABLED = "Disabled"
    }
}

/**
 * Accessibility modifier extensions for common patterns
 */

/**
 * Add accessibility semantics for calculator buttons
 */
@Composable
fun Modifier.calculatorButtonSemantics(
    text: String,
    onClick: () -> Unit,
    enabled: Boolean = true
): Modifier {
    return this.semantics {
        contentDescription = "${AccessibilityManager.ContentDescriptions.CALCULATOR_BUTTON}: $text"
        role = AccessibilityManager.SemanticRoles.CALCULATOR_BUTTON
        stateDescription = if (enabled) {
            AccessibilityManager.StateDescriptions.ENABLED
        } else {
            AccessibilityManager.StateDescriptions.DISABLED
        }
        if (enabled) {
            onClick { onClick(); true }
        }
    }
}

/**
 * Add accessibility semantics for input fields
 */
@Composable
fun Modifier.inputFieldSemantics(
    label: String,
    value: String,
    error: String? = null
): Modifier {
    return this.semantics {
        contentDescription = "${AccessibilityManager.ContentDescriptions.NUMBER_INPUT}: $label"
        role = AccessibilityManager.SemanticRoles.INPUT_FIELD
        text = AnnotatedString(value)
        error?.let { 
            stateDescription = "${AccessibilityManager.StateDescriptions.ERROR}: $it"
        }
    }
}

/**
 * Add accessibility semantics for result displays
 */
@Composable
fun Modifier.resultDisplaySemantics(
    result: String,
    isLoading: Boolean = false
): Modifier {
    return this.semantics {
        contentDescription = "${AccessibilityManager.ContentDescriptions.RESULT_DISPLAY}: $result"
        role = AccessibilityManager.SemanticRoles.RESULT_TEXT
        text = AnnotatedString(result)
        stateDescription = if (isLoading) {
            AccessibilityManager.StateDescriptions.LOADING
        } else {
            AccessibilityManager.StateDescriptions.SUCCESS
        }
    }
}

/**
 * Add accessibility semantics for navigation tabs
 */
@Composable
fun Modifier.navigationTabSemantics(
    tabName: String,
    isSelected: Boolean,
    onClick: () -> Unit
): Modifier {
    return this.semantics {
        contentDescription = "${AccessibilityManager.ContentDescriptions.NAVIGATION_TAB}: $tabName"
        role = AccessibilityManager.SemanticRoles.NAVIGATION_TAB
        stateDescription = if (isSelected) {
            AccessibilityManager.StateDescriptions.SELECTED
        } else {
            AccessibilityManager.StateDescriptions.UNSELECTED
        }
        onClick { onClick(); true }
    }
}

/**
 * Add accessibility semantics for dropdown menus
 */
@Composable
fun Modifier.dropdownSemantics(
    label: String,
    selectedValue: String,
    isExpanded: Boolean,
    onClick: () -> Unit
): Modifier {
    return this.semantics {
        contentDescription = "${AccessibilityManager.ContentDescriptions.DROPDOWN_MENU}: $label, selected: $selectedValue"
        role = AccessibilityManager.SemanticRoles.DROPDOWN
        stateDescription = if (isExpanded) {
            AccessibilityManager.StateDescriptions.EXPANDED
        } else {
            AccessibilityManager.StateDescriptions.COLLAPSED
        }
        onClick { onClick(); true }
    }
}

/**
 * Add accessibility semantics for dialog content
 */
@Composable
fun Modifier.dialogSemantics(
    title: String,
    description: String? = null
): Modifier {
    return this.semantics {
        contentDescription = "Dialog: $title${description?.let { ", $it" } ?: ""}"
        role = AccessibilityManager.SemanticRoles.DIALOG
        heading()
    }
}

/**
 * Add accessibility semantics for action buttons
 */
@Composable
fun Modifier.actionButtonSemantics(
    action: String,
    onClick: () -> Unit,
    enabled: Boolean = true
): Modifier {
    return this.semantics {
        contentDescription = "$action button"
        role = Role.Button
        stateDescription = if (enabled) {
            AccessibilityManager.StateDescriptions.ENABLED
        } else {
            AccessibilityManager.StateDescriptions.DISABLED
        }
        if (enabled) {
            onClick { onClick(); true }
        }
    }
}

/**
 * Add live region semantics for dynamic content updates
 */
@Composable
fun Modifier.liveRegionSemantics(
    content: String,
    politeness: LiveRegionMode = LiveRegionMode.Polite
): Modifier {
    return this.semantics {
        liveRegion = politeness
        contentDescription = content
    }
}

/**
 * Accessibility utilities for common patterns
 */
object AccessibilityUtils {
    
    /**
     * Format numbers for screen readers
     */
    fun formatNumberForScreenReader(number: String): String {
        return number.replace(",", " comma ")
            .replace(".", " point ")
            .replace("-", " minus ")
            .replace("+", " plus ")
    }
    
    /**
     * Format currency for screen readers
     */
    fun formatCurrencyForScreenReader(amount: String, currency: String): String {
        return "$amount $currency"
    }
    
    /**
     * Format percentage for screen readers
     */
    fun formatPercentageForScreenReader(percentage: String): String {
        return "$percentage percent"
    }
    
    /**
     * Create accessible error messages
     */
    fun createErrorMessage(field: String, error: String): String {
        return "Error in $field: $error"
    }
    
    /**
     * Create accessible success messages
     */
    fun createSuccessMessage(action: String): String {
        return "$action completed successfully"
    }
}
