package com.app.wordifynumbers.ui.components

/**
 * Enum representing different types of calculator buttons
 * Used for styling and behavior differentiation
 */
enum class ButtonType {
    NUMBER,     // Numeric buttons (0-9, decimal)
    OPERATOR,   // Mathematical operators (+, -, ×, ÷)
    FUNCTION,   // Function buttons (clear, delete, etc.)
    EQUALS,     // Equals button (=)
    SCIENTIFIC  // Scientific calculator functions
}