package com.app.wordifynumbers.ui.screens

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.Complex
import com.app.wordifynumbers.util.ComplexOperation
import com.app.wordifynumbers.util.FeedbackUtil
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.*

/**
 * A modern complex number calculator screen
 */
@Composable
fun ModernComplexCalculatorScreen() {
    val context = LocalContext.current

    // Theme colors
    val primaryColor = NeonCyan
    val secondaryColor = NeonPink
    val accentColor = NeonGreen

    // State for complex number inputs
    var real1 by remember { mutableStateOf("") }
    var imag1 by remember { mutableStateOf("") }
    var real2 by remember { mutableStateOf("") }
    var imag2 by remember { mutableStateOf("") }

    // State for polar form inputs
    var magnitude by remember { mutableStateOf("") }
    var angle by remember { mutableStateOf("") }

    // Operation and result state
    var operation by remember { mutableStateOf(ComplexOperation.ADD) }
    var result by remember { mutableStateOf<Complex?>(null) }
    var isError by remember { mutableStateOf(false) }

    // Locale for number formatting
    var selectedLocale by remember { mutableStateOf(Locale.getDefault()) }
    var showLocaleDialog by remember { mutableStateOf(false) }

    // Number formatter
    val numberFormat = remember(selectedLocale) {
        NumberFormat.getNumberInstance(selectedLocale).apply {
            maximumFractionDigits = 6
            minimumFractionDigits = 0
        }
    }

    // Input mode state
    var inputMode by remember { mutableStateOf("rectangular") }

    // Function to parse complex number from inputs
    fun parseComplex1(): Complex? {
        return try {
            val r = real1.toDoubleOrNull() ?: 0.0
            val i = imag1.toDoubleOrNull() ?: 0.0
            Complex(r, i)
        } catch (e: Exception) {
            null
        }
    }

    fun parseComplex2(): Complex? {
        return try {
            val r = real2.toDoubleOrNull() ?: 0.0
            val i = imag2.toDoubleOrNull() ?: 0.0
            Complex(r, i)
        } catch (e: Exception) {
            null
        }
    }

    fun parsePolarComplex(): Complex? {
        return try {
            val mag = magnitude.toDoubleOrNull() ?: 0.0
            val ang = angle.toDoubleOrNull() ?: 0.0
            Complex.fromPolarDegrees(mag, ang)
        } catch (e: Exception) {
            null
        }
    }

    // Function to perform calculation
    fun calculate() {
        try {
            isError = false

            when (operation) {
                // Binary operations
                ComplexOperation.ADD,
                ComplexOperation.SUBTRACT,
                ComplexOperation.MULTIPLY,
                ComplexOperation.DIVIDE,
                ComplexOperation.POWER -> {
                    val z1 = parseComplex1() ?: throw Exception("Invalid first complex number")
                    val z2 = parseComplex2() ?: throw Exception("Invalid second complex number")

                    result = when (operation) {
                        ComplexOperation.ADD -> z1 + z2
                        ComplexOperation.SUBTRACT -> z1 - z2
                        ComplexOperation.MULTIPLY -> z1 * z2
                        ComplexOperation.DIVIDE -> z1 / z2
                        ComplexOperation.POWER -> z1.pow(z2.real)
                        else -> throw Exception("Unsupported operation")
                    }
                }

                // Unary operations
                else -> {
                    val z = if (inputMode == "rectangular") {
                        parseComplex1() ?: throw Exception("Invalid complex number")
                    } else {
                        parsePolarComplex() ?: throw Exception("Invalid polar form")
                    }

                    result = when (operation) {
                        ComplexOperation.CONJUGATE -> z.conjugate()
                        ComplexOperation.NEGATE -> -z
                        ComplexOperation.SQUARE -> z * z
                        ComplexOperation.SQRT -> z.sqrt()
                        ComplexOperation.EXP -> z.exp()
                        ComplexOperation.LN -> z.ln()
                        ComplexOperation.SIN -> z.sin()
                        ComplexOperation.COS -> z.cos()
                        ComplexOperation.TAN -> z.tan()
                        else -> throw Exception("Unsupported operation")
                    }
                }
            }
        } catch (e: Exception) {
            isError = true
            result = null
        }
    }

    // Information dialog
    var showInfoDialog by remember { mutableStateOf(false) }

    if (showInfoDialog) {
        AlertDialog(
            onDismissRequest = { showInfoDialog = false },
            title = { Text("Complex Number Calculator") },
            text = {
                Column {
                    Text("This calculator helps you perform operations with complex numbers.")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("• Enter complex numbers in the form a + bi")
                    Text("• Perform basic operations: +, -, ×, ÷")
                    Text("• Calculate powers, roots, and other functions")
                    Text("• Convert between rectangular and polar forms")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("All calculations follow mathematical standards for complex arithmetic.")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("You can change the number format by selecting a different locale.")
                }
            },
            confirmButton = {
                TextButton(onClick = { showInfoDialog = false }) {
                    Text("Got it")
                }
            },
            containerColor = NeonCard,
            titleContentColor = primaryColor,
            textContentColor = NeonText
        )
    }

    // Locale selection dialog
    if (showLocaleDialog) {
        val availableLocales = remember {
            listOf(
                Locale.US,
                Locale.UK,
                Locale.GERMANY,
                Locale.FRANCE,
                Locale.ITALY,
                Locale.JAPAN,
                Locale.CHINA,
                Locale.KOREA,
                Locale("ar"),  // Arabic
                Locale("hi"),  // Hindi
                Locale("ru"),  // Russian
                Locale("es"),  // Spanish
                Locale("pt"),  // Portuguese
                Locale("ur")   // Urdu
            )
        }

        AlertDialog(
            onDismissRequest = { showLocaleDialog = false },
            title = { Text("Select Number Format") },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    availableLocales.forEach { locale ->
                        val isSelected = locale.language == selectedLocale.language
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .clickable {
                                    selectedLocale = locale
                                    showLocaleDialog = false
                                    FeedbackUtil.buttonPress(context)
                                },
                            color = if (isSelected) primaryColor.copy(alpha = 0.2f) else Color.Transparent
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = locale.displayName,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (isSelected) primaryColor else NeonText
                                )

                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = primaryColor
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showLocaleDialog = false }) {
                    Text("Cancel")
                }
            },
            containerColor = NeonCard,
            titleContentColor = primaryColor,
            textContentColor = NeonText
        )
    }

    // Main screen content
    StandardCalculatorLayout(
        title = "Complex Numbers",
        icon = Icons.Default.Calculate,
        accentColor = primaryColor,
        showInfoButton = true,
        onInfoClick = {
            showInfoDialog = true
            FeedbackUtil.buttonPress(context)
        },

        // Input Section
        inputSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Title with description
                Text(
                    text = "Complex Number Calculator",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = primaryColor
                )

                Text(
                    text = "Perform operations with complex numbers in rectangular or polar form",
                    style = MaterialTheme.typography.bodyMedium,
                    color = NeonText.copy(alpha = 0.9f)
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Input mode selector with improved styling
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = primaryColor.copy(alpha = 0.2f),
                            ambientColor = primaryColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(1.dp, SolidColor(primaryColor.copy(alpha = 0.3f)))
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "Input Format",
                            style = MaterialTheme.typography.labelLarge,
                            color = primaryColor
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            Button(
                                onClick = {
                                    inputMode = "rectangular"
                                    FeedbackUtil.buttonPress(context)
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = if (inputMode == "rectangular")
                                        primaryColor else NeonCard.copy(alpha = 0.7f),
                                    contentColor = if (inputMode == "rectangular")
                                        Color.Black else primaryColor
                                ),
                                border = BorderStroke(1.dp, SolidColor(primaryColor)),
                                shape = RoundedCornerShape(8.dp),
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    "Rectangular (a+bi)",
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = if (inputMode == "rectangular")
                                            FontWeight.Bold else FontWeight.Normal
                                    )
                                )
                            }

                            Spacer(modifier = Modifier.width(8.dp))

                            Button(
                                onClick = {
                                    inputMode = "polar"
                                    FeedbackUtil.buttonPress(context)
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = if (inputMode == "polar")
                                        primaryColor else NeonCard.copy(alpha = 0.7f),
                                    contentColor = if (inputMode == "polar")
                                        Color.Black else primaryColor
                                ),
                                border = BorderStroke(1.dp, SolidColor(primaryColor)),
                                shape = RoundedCornerShape(8.dp),
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    "Polar (r∠θ)",
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = if (inputMode == "polar")
                                            FontWeight.Bold else FontWeight.Normal
                                    )
                                )
                            }
                        }
                    }
                }

                // Input fields based on mode with improved styling
                if (inputMode == "rectangular") {
                    // First complex number
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .shadow(
                                elevation = 8.dp,
                                spotColor = primaryColor.copy(alpha = 0.2f),
                                ambientColor = primaryColor.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(12.dp)
                            ),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = NeonCard.copy(alpha = 0.9f)
                        ),
                        border = BorderStroke(1.dp, SolidColor(primaryColor.copy(alpha = 0.3f)))
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Filter1,
                                    contentDescription = null,
                                    tint = primaryColor
                                )

                                Text(
                                    text = "First Complex Number",
                                    style = MaterialTheme.typography.titleMedium.copy(
                                        fontWeight = FontWeight.Bold
                                    ),
                                    color = primaryColor
                                )
                            }

                            Divider(color = primaryColor.copy(alpha = 0.2f))

                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                // Real part with improved styling
                                OutlinedTextField(
                                    label = { Text("Real Part (a)") },
                                    value = real1,
                                    onValueChange = {
                                        real1 = it
                                        if (operation !in listOf(
                                            ComplexOperation.ADD,
                                            ComplexOperation.SUBTRACT,
                                            ComplexOperation.MULTIPLY,
                                            ComplexOperation.DIVIDE,
                                            ComplexOperation.POWER
                                        )) {
                                            calculate()
                                        }
                                    },
                                    modifier = Modifier.fillMaxWidth(),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = primaryColor,
                                        unfocusedBorderColor = primaryColor.copy(alpha = 0.5f),
                                        focusedLabelColor = primaryColor,
                                        unfocusedLabelColor = primaryColor.copy(alpha = 0.7f),
                                        focusedTextColor = NeonText,
                                        unfocusedTextColor = NeonText,
                                        cursorColor = primaryColor,
                                        focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                        unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                    ),
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Default.Functions,
                                            contentDescription = null,
                                            tint = primaryColor
                                        )
                                    }
                                )

                                // Imaginary part with improved styling
                                OutlinedTextField(
                                    label = { Text("Imaginary Part (b)") },
                                    value = imag1,
                                    onValueChange = {
                                        imag1 = it
                                        if (operation !in listOf(
                                            ComplexOperation.ADD,
                                            ComplexOperation.SUBTRACT,
                                            ComplexOperation.MULTIPLY,
                                            ComplexOperation.DIVIDE,
                                            ComplexOperation.POWER
                                        )) {
                                            calculate()
                                        }
                                    },
                                    modifier = Modifier.fillMaxWidth(),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = primaryColor,
                                        unfocusedBorderColor = primaryColor.copy(alpha = 0.5f),
                                        focusedLabelColor = primaryColor,
                                        unfocusedLabelColor = primaryColor.copy(alpha = 0.7f),
                                        focusedTextColor = NeonText,
                                        unfocusedTextColor = NeonText,
                                        cursorColor = primaryColor,
                                        focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                        unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                    ),
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Default.Functions,
                                            contentDescription = null,
                                            tint = primaryColor
                                        )
                                    },
                                    trailingIcon = {
                                        Text(
                                            text = "i",
                                            style = MaterialTheme.typography.titleMedium.copy(
                                                fontWeight = FontWeight.Bold,
                                                fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                            ),
                                            color = primaryColor
                                        )
                                    }
                                )

                                // Display the complex number in standard form
                                if (real1.isNotEmpty() || imag1.isNotEmpty()) {
                                    val r = real1.toDoubleOrNull() ?: 0.0
                                    val i = imag1.toDoubleOrNull() ?: 0.0
                                    val sign = if (i >= 0) "+" else ""

                                    Text(
                                        text = "z₁ = $r $sign ${i}i",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = primaryColor
                                    )
                                }
                            }
                        }
                    }

                    // Second complex number (only for binary operations)
                    if (operation in listOf(
                        ComplexOperation.ADD,
                        ComplexOperation.SUBTRACT,
                        ComplexOperation.MULTIPLY,
                        ComplexOperation.DIVIDE,
                        ComplexOperation.POWER
                    )) {
                        Spacer(modifier = Modifier.height(8.dp))

                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .shadow(
                                    elevation = 8.dp,
                                    spotColor = secondaryColor.copy(alpha = 0.2f),
                                    ambientColor = secondaryColor.copy(alpha = 0.1f),
                                    shape = RoundedCornerShape(12.dp)
                                ),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = NeonCard.copy(alpha = 0.9f)
                            ),
                            border = BorderStroke(1.dp, SolidColor(secondaryColor.copy(alpha = 0.3f)))
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Filter2,
                                        contentDescription = null,
                                        tint = secondaryColor
                                    )

                                    Text(
                                        text = "Second Complex Number",
                                        style = MaterialTheme.typography.titleMedium.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = secondaryColor
                                    )
                                }

                                Divider(color = secondaryColor.copy(alpha = 0.2f))

                                Column(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalArrangement = Arrangement.spacedBy(12.dp)
                                ) {
                                    // Real part with improved styling
                                    OutlinedTextField(
                                        label = { Text("Real Part (a)") },
                                        value = real2,
                                        onValueChange = { real2 = it },
                                        modifier = Modifier.fillMaxWidth(),
                                        singleLine = true,
                                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                        colors = OutlinedTextFieldDefaults.colors(
                                            focusedBorderColor = secondaryColor,
                                            unfocusedBorderColor = secondaryColor.copy(alpha = 0.5f),
                                            focusedLabelColor = secondaryColor,
                                            unfocusedLabelColor = secondaryColor.copy(alpha = 0.7f),
                                            focusedTextColor = NeonText,
                                            unfocusedTextColor = NeonText,
                                            cursorColor = secondaryColor,
                                            focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                            unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                        ),
                                        leadingIcon = {
                                            Icon(
                                                imageVector = Icons.Default.Functions,
                                                contentDescription = null,
                                                tint = secondaryColor
                                            )
                                        }
                                    )

                                    // Imaginary part with improved styling
                                    OutlinedTextField(
                                        label = { Text("Imaginary Part (b)") },
                                        value = imag2,
                                        onValueChange = { imag2 = it },
                                        modifier = Modifier.fillMaxWidth(),
                                        singleLine = true,
                                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                        colors = OutlinedTextFieldDefaults.colors(
                                            focusedBorderColor = secondaryColor,
                                            unfocusedBorderColor = secondaryColor.copy(alpha = 0.5f),
                                            focusedLabelColor = secondaryColor,
                                            unfocusedLabelColor = secondaryColor.copy(alpha = 0.7f),
                                            focusedTextColor = NeonText,
                                            unfocusedTextColor = NeonText,
                                            cursorColor = secondaryColor,
                                            focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                            unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                        ),
                                        leadingIcon = {
                                            Icon(
                                                imageVector = Icons.Default.Functions,
                                                contentDescription = null,
                                                tint = secondaryColor
                                            )
                                        },
                                        trailingIcon = {
                                            Text(
                                                text = "i",
                                                style = MaterialTheme.typography.titleMedium.copy(
                                                    fontWeight = FontWeight.Bold,
                                                    fontStyle = androidx.compose.ui.text.font.FontStyle.Italic
                                                ),
                                                color = secondaryColor
                                            )
                                        }
                                    )

                                    // Display the complex number in standard form
                                    if (real2.isNotEmpty() || imag2.isNotEmpty()) {
                                        val r = real2.toDoubleOrNull() ?: 0.0
                                        val i = imag2.toDoubleOrNull() ?: 0.0
                                        val sign = if (i >= 0) "+" else ""

                                        Text(
                                            text = "z₂ = $r $sign ${i}i",
                                            style = MaterialTheme.typography.bodyLarge.copy(
                                                fontWeight = FontWeight.Bold
                                            ),
                                            color = secondaryColor
                                        )
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // Polar form input with improved styling
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .shadow(
                                elevation = 8.dp,
                                spotColor = primaryColor.copy(alpha = 0.2f),
                                ambientColor = primaryColor.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(12.dp)
                            ),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = NeonCard.copy(alpha = 0.9f)
                        ),
                        border = BorderStroke(1.dp, SolidColor(primaryColor.copy(alpha = 0.3f)))
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.ChangeCircle,
                                    contentDescription = null,
                                    tint = primaryColor
                                )

                                Text(
                                    text = "Polar Form Input",
                                    style = MaterialTheme.typography.titleMedium.copy(
                                        fontWeight = FontWeight.Bold
                                    ),
                                    color = primaryColor
                                )
                            }

                            Divider(color = primaryColor.copy(alpha = 0.2f))

                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                // Magnitude with improved styling
                                OutlinedTextField(
                                    label = { Text("Magnitude (r)") },
                                    value = magnitude,
                                    onValueChange = {
                                        magnitude = it
                                        if (operation !in listOf(
                                            ComplexOperation.ADD,
                                            ComplexOperation.SUBTRACT,
                                            ComplexOperation.MULTIPLY,
                                            ComplexOperation.DIVIDE,
                                            ComplexOperation.POWER
                                        )) {
                                            calculate()
                                        }
                                    },
                                    modifier = Modifier.fillMaxWidth(),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = primaryColor,
                                        unfocusedBorderColor = primaryColor.copy(alpha = 0.5f),
                                        focusedLabelColor = primaryColor,
                                        unfocusedLabelColor = primaryColor.copy(alpha = 0.7f),
                                        focusedTextColor = NeonText,
                                        unfocusedTextColor = NeonText,
                                        cursorColor = primaryColor,
                                        focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                        unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                    ),
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Default.RadioButtonChecked,
                                            contentDescription = null,
                                            tint = primaryColor
                                        )
                                    }
                                )

                                // Angle with improved styling
                                OutlinedTextField(
                                    label = { Text("Angle (θ) in degrees") },
                                    value = angle,
                                    onValueChange = {
                                        angle = it
                                        if (operation !in listOf(
                                            ComplexOperation.ADD,
                                            ComplexOperation.SUBTRACT,
                                            ComplexOperation.MULTIPLY,
                                            ComplexOperation.DIVIDE,
                                            ComplexOperation.POWER
                                        )) {
                                            calculate()
                                        }
                                    },
                                    modifier = Modifier.fillMaxWidth(),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedBorderColor = primaryColor,
                                        unfocusedBorderColor = primaryColor.copy(alpha = 0.5f),
                                        focusedLabelColor = primaryColor,
                                        unfocusedLabelColor = primaryColor.copy(alpha = 0.7f),
                                        focusedTextColor = NeonText,
                                        unfocusedTextColor = NeonText,
                                        cursorColor = primaryColor,
                                        focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                        unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                    ),
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Default.ChangeCircle,
                                            contentDescription = null,
                                            tint = primaryColor
                                        )
                                    },
                                    trailingIcon = {
                                        Text(
                                            text = "°",
                                            style = MaterialTheme.typography.titleMedium.copy(
                                                fontWeight = FontWeight.Bold
                                            ),
                                            color = primaryColor
                                        )
                                    }
                                )

                                // Display the complex number in polar form
                                if (magnitude.isNotEmpty() || angle.isNotEmpty()) {
                                    val r = magnitude.toDoubleOrNull() ?: 0.0
                                    val theta = angle.toDoubleOrNull() ?: 0.0

                                    Text(
                                        text = "z = $r ∠ ${theta}°",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = primaryColor
                                    )
                                }
                            }
                        }
                    }
                }
            }
        },

        // Action Buttons
        actionButtons = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Operation selection
                Text(
                    text = "Select Operation",
                    style = MaterialTheme.typography.titleMedium,
                    color = accentColor,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                // Basic operations
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    OperationButton(
                        operation = ComplexOperation.ADD,
                        isSelected = operation == ComplexOperation.ADD,
                        onClick = {
                            operation = ComplexOperation.ADD
                            FeedbackUtil.buttonPress(context)
                        },
                        primaryColor = accentColor,
                        modifier = Modifier.weight(1f)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    OperationButton(
                        operation = ComplexOperation.SUBTRACT,
                        isSelected = operation == ComplexOperation.SUBTRACT,
                        onClick = {
                            operation = ComplexOperation.SUBTRACT
                            FeedbackUtil.buttonPress(context)
                        },
                        primaryColor = accentColor,
                        modifier = Modifier.weight(1f)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    OperationButton(
                        operation = ComplexOperation.MULTIPLY,
                        isSelected = operation == ComplexOperation.MULTIPLY,
                        onClick = {
                            operation = ComplexOperation.MULTIPLY
                            FeedbackUtil.buttonPress(context)
                        },
                        primaryColor = accentColor,
                        modifier = Modifier.weight(1f)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    OperationButton(
                        operation = ComplexOperation.DIVIDE,
                        isSelected = operation == ComplexOperation.DIVIDE,
                        onClick = {
                            operation = ComplexOperation.DIVIDE
                            FeedbackUtil.buttonPress(context)
                        },
                        primaryColor = accentColor,
                        modifier = Modifier.weight(1f)
                    )
                }

                // Advanced operations
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    OperationButton(
                        operation = ComplexOperation.CONJUGATE,
                        isSelected = operation == ComplexOperation.CONJUGATE,
                        onClick = {
                            operation = ComplexOperation.CONJUGATE
                            calculate()
                            FeedbackUtil.buttonPress(context)
                        },
                        primaryColor = accentColor,
                        modifier = Modifier.weight(1f)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    OperationButton(
                        operation = ComplexOperation.SQUARE,
                        isSelected = operation == ComplexOperation.SQUARE,
                        onClick = {
                            operation = ComplexOperation.SQUARE
                            calculate()
                            FeedbackUtil.buttonPress(context)
                        },
                        primaryColor = accentColor,
                        modifier = Modifier.weight(1f)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    OperationButton(
                        operation = ComplexOperation.SQRT,
                        isSelected = operation == ComplexOperation.SQRT,
                        onClick = {
                            operation = ComplexOperation.SQRT
                            calculate()
                            FeedbackUtil.buttonPress(context)
                        },
                        primaryColor = accentColor,
                        modifier = Modifier.weight(1f)
                    )
                }

                // Calculate button (only for binary operations)
                if (operation in listOf(
                    ComplexOperation.ADD,
                    ComplexOperation.SUBTRACT,
                    ComplexOperation.MULTIPLY,
                    ComplexOperation.DIVIDE,
                    ComplexOperation.POWER
                )) {
                    Button(
                        onClick = {
                            calculate()
                            FeedbackUtil.buttonPress(context)
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = accentColor,
                            contentColor = Color.Black
                        ),
                        shape = RoundedCornerShape(12.dp),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = "Calculate",
                            style = MaterialTheme.typography.titleMedium,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }

                // Clear button
                Button(
                    onClick = {
                        real1 = ""
                        imag1 = ""
                        real2 = ""
                        imag2 = ""
                        magnitude = ""
                        angle = ""
                        result = null
                        FeedbackUtil.buttonPress(context)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = secondaryColor,
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "Clear"
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Clear All")
                }
            }
        },

        // Result Section with improved styling
        resultSection = {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = accentColor.copy(alpha = 0.2f),
                        ambientColor = accentColor.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                ),
                border = BorderStroke(1.dp, SolidColor(accentColor.copy(alpha = 0.3f)))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Result header
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Calculate,
                            contentDescription = null,
                            tint = accentColor
                        )

                        Text(
                            text = "Calculation Result",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = accentColor
                        )
                    }

                    Divider(color = accentColor.copy(alpha = 0.2f))

                    if (result != null) {
                        // Result visualization with improved styling
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(16.dp)
                        ) {
                            // Cartesian form with card styling
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .shadow(
                                        elevation = 4.dp,
                                        spotColor = primaryColor.copy(alpha = 0.2f),
                                        ambientColor = primaryColor.copy(alpha = 0.1f),
                                        shape = RoundedCornerShape(12.dp)
                                    ),
                                shape = RoundedCornerShape(12.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = NeonCard.copy(alpha = 0.7f)
                                ),
                                border = BorderStroke(1.dp, primaryColor.copy(alpha = 0.3f))
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(12.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.LinearScale,
                                            contentDescription = null,
                                            tint = primaryColor
                                        )

                                        Text(
                                            text = "Cartesian Form (a + bi)",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = primaryColor,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }

                                    Text(
                                        text = result.toString(),
                                        style = MaterialTheme.typography.headlineSmall.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = NeonText,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }
                            }

                            // Polar form with card styling
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .shadow(
                                        elevation = 4.dp,
                                        spotColor = secondaryColor.copy(alpha = 0.2f),
                                        ambientColor = secondaryColor.copy(alpha = 0.1f),
                                        shape = RoundedCornerShape(12.dp)
                                    ),
                                shape = RoundedCornerShape(12.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = NeonCard.copy(alpha = 0.7f)
                                ),
                                border = BorderStroke(1.dp, secondaryColor.copy(alpha = 0.3f))
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(12.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.ChangeCircle,
                                            contentDescription = null,
                                            tint = secondaryColor
                                        )

                                        Text(
                                            text = "Polar Form (r∠θ)",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = secondaryColor,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }

                                    Text(
                                        text = result?.toPolarString() ?: "",
                                        style = MaterialTheme.typography.headlineSmall.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = NeonText,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }
                            }

                            // Exponential form with card styling
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .shadow(
                                        elevation = 4.dp,
                                        spotColor = accentColor.copy(alpha = 0.2f),
                                        ambientColor = accentColor.copy(alpha = 0.1f),
                                        shape = RoundedCornerShape(12.dp)
                                    ),
                                shape = RoundedCornerShape(12.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = NeonCard.copy(alpha = 0.7f)
                                ),
                                border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(12.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Functions,
                                            contentDescription = null,
                                            tint = accentColor
                                        )

                                        Text(
                                            text = "Exponential Form (re^iθ)",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = accentColor,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }

                                    Text(
                                        text = result?.toExponentialString() ?: "",
                                        style = MaterialTheme.typography.headlineSmall.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = NeonText,
                                        modifier = Modifier.fillMaxWidth()
                                    )
                                }
                            }

                            // Properties in a row with improved styling
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                // Magnitude card
                                Card(
                                    modifier = Modifier
                                        .weight(1f)
                                        .shadow(
                                            elevation = 4.dp,
                                            spotColor = NeonGreen.copy(alpha = 0.2f),
                                            ambientColor = NeonGreen.copy(alpha = 0.1f),
                                            shape = RoundedCornerShape(12.dp)
                                        ),
                                    shape = RoundedCornerShape(12.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = NeonCard.copy(alpha = 0.7f)
                                    ),
                                    border = BorderStroke(1.dp, NeonGreen.copy(alpha = 0.3f))
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(12.dp),
                                        verticalArrangement = Arrangement.spacedBy(4.dp),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Speed,
                                                contentDescription = null,
                                                tint = NeonGreen,
                                                modifier = Modifier.size(16.dp)
                                            )

                                            Text(
                                                text = "Magnitude",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = NeonGreen
                                            )
                                        }

                                        Text(
                                            text = result?.let { numberFormat.format(it.magnitude()) } ?: "",
                                            style = MaterialTheme.typography.titleLarge.copy(
                                                fontWeight = FontWeight.Bold
                                            ),
                                            color = NeonText
                                        )
                                    }
                                }

                                // Phase card
                                Card(
                                    modifier = Modifier
                                        .weight(1f)
                                        .shadow(
                                            elevation = 4.dp,
                                            spotColor = NeonOrange.copy(alpha = 0.2f),
                                            ambientColor = NeonOrange.copy(alpha = 0.1f),
                                            shape = RoundedCornerShape(12.dp)
                                        ),
                                    shape = RoundedCornerShape(12.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = NeonCard.copy(alpha = 0.7f)
                                    ),
                                    border = BorderStroke(1.dp, NeonOrange.copy(alpha = 0.3f))
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(12.dp),
                                        verticalArrangement = Arrangement.spacedBy(4.dp),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.RotateRight,
                                                contentDescription = null,
                                                tint = NeonOrange,
                                                modifier = Modifier.size(16.dp)
                                            )

                                            Text(
                                                text = "Phase (rad)",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = NeonOrange
                                            )
                                        }

                                        Text(
                                            text = result?.let { numberFormat.format(it.phase()) } ?: "",
                                            style = MaterialTheme.typography.titleLarge.copy(
                                                fontWeight = FontWeight.Bold
                                            ),
                                            color = NeonText
                                        )
                                    }
                                }
                            }

                            // Legend with improved styling
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                shape = RoundedCornerShape(8.dp),
                                colors = CardDefaults.cardColors(
                                    containerColor = NeonBackground.copy(alpha = 0.5f)
                                )
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(8.dp),
                                    horizontalArrangement = Arrangement.SpaceEvenly,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    LegendItem(color = primaryColor, text = "Real + Imaginary")
                                    LegendItem(color = secondaryColor, text = "Magnitude ∠ Angle")
                                    LegendItem(color = accentColor, text = "r e^(iθ)")
                                }
                            }
                        }
                    } else {
                        // Empty state or error with improved styling
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = if (isError)
                                    NeonRed.copy(alpha = 0.1f) else NeonBackground.copy(alpha = 0.3f)
                            ),
                            border = BorderStroke(
                                1.dp,
                                if (isError) NeonRed.copy(alpha = 0.3f) else accentColor.copy(alpha = 0.2f)
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                horizontalArrangement = Arrangement.spacedBy(12.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = if (isError) Icons.Default.Error else Icons.Default.Info,
                                    contentDescription = null,
                                    tint = if (isError) NeonRed else accentColor,
                                    modifier = Modifier.size(24.dp)
                                )

                                Text(
                                    text = if (isError)
                                        "Error in calculation. Please check your inputs."
                                    else
                                        "Enter complex numbers and select an operation to see results",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (isError) NeonRed else NeonText.copy(alpha = 0.9f),
                                    modifier = Modifier.weight(1f)
                                )
                            }
                        }
                    }
                }
            }
        },
        additionalContent = null
    )
}

@Composable
fun OperationButton(
    operation: ComplexOperation,
    isSelected: Boolean,
    onClick: () -> Unit,
    primaryColor: Color,
    modifier: Modifier = Modifier
) {
    val backgroundColor = if (isSelected) primaryColor.copy(alpha = 0.2f) else Color.Transparent
    val borderColor = if (isSelected) primaryColor else primaryColor.copy(alpha = 0.5f)

    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = primaryColor
        ),
        border = BorderStroke(1.dp, borderColor),
        shape = RoundedCornerShape(8.dp),
        modifier = modifier.height(40.dp)
    ) {
        Text(
            text = operation.symbol,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
fun ResultItem(
    label: String,
    value: String,
    color: Color,
    icon: ImageVector? = null,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = color,
                    modifier = Modifier.size(16.dp)
                )
            }

            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = color
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Bold
            ),
            color = NeonText, // Using NeonText instead of Color.White for better visibility
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun LegendItem(
    color: Color,
    text: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Improved color indicator with border for better visibility
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, shape = CircleShape)
                .border(0.5.dp, Color.White.copy(alpha = 0.3f), CircleShape)
        )

        Text(
            text = text,
            style = MaterialTheme.typography.bodySmall.copy(
                fontWeight = FontWeight.Medium
            ),
            color = NeonText.copy(alpha = 0.9f) // Using NeonText instead of Color.White for better visibility
        )
    }
}
