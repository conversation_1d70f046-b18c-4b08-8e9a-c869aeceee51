package com.app.wordifynumbers.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.*

/**
 * ViewModel for the Statistics Calculator screen
 * Handles business logic for statistical calculations
 */
class StatisticsCalculatorViewModel : ViewModel() {
    
    // Data input as string
    private val _dataInput = MutableStateFlow("")
    val dataInput: StateFlow<String> = _dataInput.asStateFlow()
    
    // Parsed data as list of doubles
    private val _parsedData = MutableStateFlow<List<Double>>(emptyList())
    val parsedData: StateFlow<List<Double>> = _parsedData.asStateFlow()
    
    // Selected visualization type
    private val _visualizationType = MutableStateFlow(VisualizationType.HISTOGRAM)
    val visualizationType: StateFlow<VisualizationType> = _visualizationType.asStateFlow()
    
    // Statistics result
    private val _statisticsResult = MutableStateFlow<StatisticsResult?>(null)
    val statisticsResult: StateFlow<StatisticsResult?> = _statisticsResult.asStateFlow()
    
    // Error message
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()
    
    // Selected locale for number formatting
    private val _selectedLocale = MutableStateFlow(Locale.getDefault())
    val selectedLocale: StateFlow<Locale> = _selectedLocale.asStateFlow()
    
    // Available locales for number formatting
    private val _availableLocales = MutableStateFlow(getCommonLocales())
    val availableLocales: StateFlow<List<Locale>> = _availableLocales.asStateFlow()
    
    // Sample data sets
    private val _sampleDataSets = MutableStateFlow(
        mapOf(
            "Normal" to "10,15,20,25,30,35,40",
            "Skewed" to "1,2,2,3,3,3,10,15,20",
            "Uniform" to "5,5,5,5,5,5,5,5,5,5",
            "Bimodal" to "1,2,3,3,4,10,11,12,12,13",
            "Large Range" to "1,5,10,50,100,500,1000"
        )
    )
    val sampleDataSets: StateFlow<Map<String, String>> = _sampleDataSets.asStateFlow()
    
    /**
     * Update the data input and recalculate statistics
     */
    fun updateDataInput(input: String) {
        _dataInput.value = input
        calculateStatistics()
    }
    
    /**
     * Set the visualization type
     */
    fun setVisualizationType(type: VisualizationType) {
        _visualizationType.value = type
    }
    
    /**
     * Set the selected locale for number formatting
     */
    fun setSelectedLocale(locale: Locale) {
        _selectedLocale.value = locale
        // Recalculate statistics to update formatted values
        calculateStatistics()
    }
    
    /**
     * Load a sample data set
     */
    fun loadSampleData(key: String) {
        _sampleDataSets.value[key]?.let { sample ->
            _dataInput.value = sample
            calculateStatistics()
        }
    }
    
    /**
     * Calculate statistics from the current data input
     */
    fun calculateStatistics() {
        viewModelScope.launch {
            try {
                _error.value = null
                
                val numbers = parseDataInput(_dataInput.value)
                if (numbers.isEmpty()) {
                    _parsedData.value = emptyList()
                    _statisticsResult.value = null
                    return@launch
                }
                
                _parsedData.value = numbers
                
                val mean = numbers.average()
                val variance = numbers.map { (it - mean).pow(2) }.average()
                val stdDev = sqrt(variance)
                val sorted = numbers.sorted()
                val min = sorted.first()
                val max = sorted.last()
                
                // Calculate median
                val median = if (sorted.size % 2 == 0) {
                    (sorted[sorted.size/2 - 1] + sorted[sorted.size/2]) / 2
                } else {
                    sorted[sorted.size/2]
                }
                
                // Calculate mode
                val modes = numbers
                    .groupBy { it }
                    .entries
                    .groupBy { it.value.size }
                    .maxByOrNull { it.key }
                    ?.value
                    ?.map { it.key }
                    ?: emptyList()
                
                // Calculate quartiles
                val q1Index = (sorted.size * 0.25).toInt()
                val q3Index = (sorted.size * 0.75).toInt()
                val q1 = sorted[q1Index]
                val q3 = sorted[q3Index]
                val iqr = q3 - q1
                
                // Calculate percentiles
                val percentiles = mapOf(
                    10 to sorted[(sorted.size * 0.1).toInt()],
                    25 to sorted[(sorted.size * 0.25).toInt()],
                    50 to median,
                    75 to sorted[(sorted.size * 0.75).toInt()],
                    90 to sorted[(sorted.size * 0.9).toInt()],
                    95 to sorted[(sorted.size * 0.95).toInt()],
                    99 to sorted[(sorted.size * 0.99).coerceAtMost(sorted.size - 1.0).toInt()]
                )
                
                // Calculate advanced statistics
                val skewness = numbers.map { ((it - mean) / stdDev).pow(3) }.average()
                val kurtosis = numbers.map { ((it - mean) / stdDev).pow(4) }.average() - 3
                val coefficientOfVariation = stdDev / mean * 100
                val meanAbsoluteDeviation = numbers.map { abs(it - mean) }.average()
                
                // Calculate geometric mean (only for positive numbers)
                val geometricMean = if (numbers.all { it > 0 }) {
                    exp(numbers.map { ln(it) }.average())
                } else {
                    Double.NaN
                }
                
                // Calculate harmonic mean (only for non-zero numbers)
                val harmonicMean = if (numbers.all { it != 0.0 }) {
                    numbers.size.toDouble() / numbers.sumOf { 1.0 / it }
                } else {
                    Double.NaN
                }
                
                // Calculate frequency distribution for histogram
                val binCount = min(10, numbers.size)
                val binWidth = (max - min) / binCount
                val bins = (0 until binCount).map { i ->
                    val lowerBound = min + i * binWidth
                    val upperBound = min + (i + 1) * binWidth
                    val count = numbers.count { it >= lowerBound && (i == binCount - 1 || it < upperBound) }
                    HistogramBin(lowerBound, upperBound, count)
                }
                
                // Create statistics result
                _statisticsResult.value = StatisticsResult(
                    data = numbers,
                    mean = mean,
                    median = median,
                    modes = modes,
                    variance = variance,
                    standardDeviation = stdDev,
                    range = max - min,
                    skewness = skewness,
                    kurtosis = kurtosis,
                    sampleSize = numbers.size,
                    min = min,
                    max = max,
                    q1 = q1,
                    q3 = q3,
                    iqr = iqr,
                    coefficientOfVariation = coefficientOfVariation,
                    meanAbsoluteDeviation = meanAbsoluteDeviation,
                    geometricMean = geometricMean,
                    harmonicMean = harmonicMean,
                    percentiles = percentiles,
                    histogramBins = bins
                )
            } catch (e: Exception) {
                _error.value = "Error: ${e.message ?: "Unknown error"}"
                _statisticsResult.value = null
            }
        }
    }
    
    /**
     * Parse data input string into a list of doubles
     */
    private fun parseDataInput(input: String): List<Double> {
        return input
            .split(",", ";", " ", "\n", "\t")
            .map { it.trim() }
            .filter { it.isNotEmpty() }
            .mapNotNull { it.toDoubleOrNull() }
    }
    
    /**
     * Format a number according to the selected locale
     */
    fun formatNumber(value: Double, decimalPlaces: Int = 3): String {
        return try {
            if (value.isNaN()) return "N/A"
            if (value.isInfinite()) return if (value > 0) "∞" else "-∞"
            
            val formatter = NumberFormat.getNumberInstance(_selectedLocale.value)
            formatter.maximumFractionDigits = decimalPlaces
            formatter.minimumFractionDigits = 0 // Don't show unnecessary decimal places
            
            formatter.format(value)
        } catch (e: Exception) {
            // Fallback to simple formatting if locale-specific formatting fails
            String.format("%.${decimalPlaces}f", value)
        }
    }
    
    /**
     * Format a percentage according to the selected locale
     */
    fun formatPercent(value: Double, decimalPlaces: Int = 2): String {
        return try {
            if (value.isNaN()) return "N/A"
            if (value.isInfinite()) return if (value > 0) "∞%" else "-∞%"
            
            val formatter = NumberFormat.getPercentInstance(_selectedLocale.value)
            formatter.maximumFractionDigits = decimalPlaces
            
            formatter.format(value / 100)
        } catch (e: Exception) {
            // Fallback to simple formatting if locale-specific formatting fails
            String.format("%.${decimalPlaces}f%%", value)
        }
    }
    
    /**
     * Generate a summary of the statistics
     */
    fun generateSummary(stats: StatisticsResult): String {
        val distribution = when {
            stats.kurtosis > 1.0 -> "leptokurtic (heavy-tailed)"
            stats.kurtosis < -1.0 -> "platykurtic (light-tailed)"
            else -> "mesokurtic (normal-like)"
        }
        
        val skewDescription = when {
            stats.skewness > 0.5 -> "positively skewed (right-tailed)"
            stats.skewness < -0.5 -> "negatively skewed (left-tailed)"
            else -> "approximately symmetric"
        }
        
        return buildString {
            append("This dataset contains ${stats.sampleSize} observations with values ranging from ")
            append("${formatNumber(stats.min)} to ${formatNumber(stats.max)}.\n\n")
            
            append("The central tendency is characterized by a mean of ${formatNumber(stats.mean)}, ")
            append("a median of ${formatNumber(stats.median)}, ")
            
            if (stats.modes.isNotEmpty()) {
                append("and ${if (stats.modes.size > 1) "modes" else "a mode"} of ")
                append(stats.modes.joinToString(", ") { formatNumber(it) })
            } else {
                append("with no distinct mode")
            }
            append(".\n\n")
            
            append("The data dispersion shows a standard deviation of ${formatNumber(stats.standardDeviation)} ")
            append("and a coefficient of variation of ${formatPercent(stats.coefficientOfVariation)}.\n\n")
            
            append("The distribution is $skewDescription and $distribution.")
        }
    }
    
    /**
     * Get a list of common locales for number formatting
     */
    private fun getCommonLocales(): List<Locale> {
        return listOf(
            Locale.US,                  // English (US)
            Locale.UK,                  // English (UK)
            Locale.GERMANY,             // German
            Locale.FRANCE,              // French
            Locale.JAPAN,               // Japanese
            Locale.CHINA,               // Chinese
            Locale.KOREA,               // Korean
            Locale("es", "ES"),         // Spanish
            Locale("it", "IT"),         // Italian
            Locale("ru", "RU"),         // Russian
            Locale("ar", "SA"),         // Arabic
            Locale("hi", "IN"),         // Hindi
            Locale("pt", "BR"),         // Portuguese (Brazil)
            Locale("tr", "TR"),         // Turkish
            Locale("nl", "NL"),         // Dutch
            Locale("sv", "SE"),         // Swedish
            Locale("pl", "PL"),         // Polish
            Locale("th", "TH"),         // Thai
            Locale("ur", "PK"),         // Urdu
            Locale("vi", "VN")          // Vietnamese
        )
    }
    
    /**
     * Get the display name for a locale
     */
    fun getLocaleDisplayName(locale: Locale): String {
        return "${locale.getDisplayLanguage(Locale.US)} (${locale.country})"
    }
}

/**
 * Enum representing the visualization type
 */
enum class VisualizationType(val displayName: String) {
    HISTOGRAM("Histogram"),
    BOX_PLOT("Box Plot"),
    SCATTER_PLOT("Scatter Plot"),
    NORMAL_CURVE("Normal Curve"),
    PERCENTILE("Percentile Plot")
}

/**
 * Data class representing a histogram bin
 */
data class HistogramBin(
    val lowerBound: Double,
    val upperBound: Double,
    val count: Int
)

/**
 * Data class representing statistics calculation results
 */
data class StatisticsResult(
    val data: List<Double>,
    val mean: Double,
    val median: Double,
    val modes: List<Double>,
    val variance: Double,
    val standardDeviation: Double,
    val range: Double,
    val skewness: Double,
    val kurtosis: Double,
    val sampleSize: Int,
    val min: Double,
    val max: Double,
    val q1: Double,  // First quartile
    val q3: Double,  // Third quartile
    val iqr: Double, // Interquartile range
    val coefficientOfVariation: Double,
    val meanAbsoluteDeviation: Double,
    val geometricMean: Double,
    val harmonicMean: Double,
    val percentiles: Map<Int, Double>, // Key percentiles (10, 25, 50, 75, 90, 95, 99)
    val histogramBins: List<HistogramBin>
)
