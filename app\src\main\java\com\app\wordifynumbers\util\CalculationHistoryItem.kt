package com.app.wordifynumbers.util

import kotlinx.serialization.Serializable
import java.text.SimpleDateFormat
import java.util.*

/**
 * Data class representing a calculation history item
 * Used for storing and displaying calculation history
 */
@Serializable
data class CalculationHistoryItem(
    val type: CalculationType,
    val input: String,
    val result: String,
    val timestamp: Long = System.currentTimeMillis(),
    val additionalData: Map<String, String> = emptyMap()
) {
    /**
     * Get formatted date string for display
     */
    fun getFormattedDate(): String {
        val date = Date(timestamp)
        val formatter = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * Get short formatted date string for compact display
     */
    fun getShortFormattedDate(): String {
        val date = Date(timestamp)
        val formatter = SimpleDateFormat("MMM dd HH:mm", Locale.getDefault())
        return formatter.format(date)
    }
    
    /**
     * Check if this history item is from today
     */
    fun isFromToday(): Boolean {
        val today = Calendar.getInstance()
        val itemDate = Calendar.getInstance().apply { timeInMillis = timestamp }
        
        return today.get(Calendar.YEAR) == itemDate.get(Calendar.YEAR) &&
               today.get(Calendar.DAY_OF_YEAR) == itemDate.get(Calendar.DAY_OF_YEAR)
    }
    
    /**
     * Get relative time string (e.g., "2 hours ago", "Yesterday")
     */
    fun getRelativeTimeString(): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60_000 -> "Just now"
            diff < 3_600_000 -> "${diff / 60_000} min ago"
            diff < 86_400_000 -> "${diff / 3_600_000} hours ago"
            diff < 172_800_000 -> "Yesterday"
            else -> getShortFormattedDate()
        }
    }
}
