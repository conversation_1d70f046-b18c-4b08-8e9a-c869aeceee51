package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.Brush
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.*
import com.app.wordifynumbers.ui.navigation.MultiModalBackHandler
import com.app.wordifynumbers.ui.navigation.NavigationUtils
import com.app.wordifynumbers.util.FeedbackUtil

/**
 * A modern, international-standard Health Calculator screen
 * Supports BMI, WHR, and BMR calculations with health insights
 */
@Composable
fun BMICalculatorScreen(modifier: Modifier = Modifier) {
    // ViewModel for business logic
    val viewModel: HealthCalculatorViewModel = viewModel()

    // Collect state from ViewModel
    val weight by viewModel.weight.collectAsState()
    val height by viewModel.height.collectAsState()
    val age by viewModel.age.collectAsState()
    val waist by viewModel.waist.collectAsState()
    val hip by viewModel.hip.collectAsState()
    val weightUnit by viewModel.weightUnit.collectAsState()
    val heightUnit by viewModel.heightUnit.collectAsState()
    val waistHipUnit by viewModel.waistHipUnit.collectAsState()
    val gender by viewModel.gender.collectAsState()
    val activityLevel by viewModel.activityLevel.collectAsState()
    val calculatorMode by viewModel.calculatorMode.collectAsState()
    val bmiResult by viewModel.bmiResult.collectAsState()
    val whrResult by viewModel.whrResult.collectAsState()
    val bmrResult by viewModel.bmrResult.collectAsState()
    val error by viewModel.error.collectAsState()
    val availableLocales by viewModel.availableLocales.collectAsState()
    val selectedLocale by viewModel.selectedLocale.collectAsState()

    // Local state
    var showLocaleDialog by remember { mutableStateOf(false) }
    var showInfoDialog by remember { mutableStateOf(false) }
    var showWeightUnitMenu by remember { mutableStateOf(false) }
    var showHeightUnitMenu by remember { mutableStateOf(false) }
    var showWaistHipUnitMenu by remember { mutableStateOf(false) }
    var showGenderMenu by remember { mutableStateOf(false) }
    var showActivityLevelMenu by remember { mutableStateOf(false) }

    val context = LocalContext.current
    val scrollState = rememberScrollState()

    // Handle back button press using standardized multi-modal handler
    MultiModalBackHandler(
        modalStates = NavigationUtils.createModalStates(
            showInfoDialog to { showInfoDialog = false },
            showLocaleDialog to { showLocaleDialog = false },
            showWeightUnitMenu to { showWeightUnitMenu = false },
            showHeightUnitMenu to { showHeightUnitMenu = false },
            showWaistHipUnitMenu to { showWaistHipUnitMenu = false },
            showGenderMenu to { showGenderMenu = false },
            showActivityLevelMenu to { showActivityLevelMenu = false }
        )
    )

    // Info Dialog
    if (showInfoDialog) {
        AlertDialog(
            onDismissRequest = { showInfoDialog = false },
            title = {
                Text(
                    text = "Health Calculator Help",
                    style = MaterialTheme.typography.titleLarge,
                    color = NeonPink
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        text = "This calculator helps you assess various health metrics with international standards.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "Available Calculators:",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonPink
                    )

                    Text(
                        text = "• BMI (Body Mass Index): Measures body fat based on height and weight\n" +
                              "• WHR (Waist-Hip Ratio): Measures body fat distribution\n" +
                              "• BMR (Basal Metabolic Rate): Estimates daily calorie needs",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "How to use:",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonPink
                    )

                    Text(
                        text = "1. Select the calculator type\n" +
                              "2. Enter your measurements\n" +
                              "3. Choose appropriate units\n" +
                              "4. Click Calculate to see results\n" +
                              "5. View personalized health tips",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "Note: These calculations provide estimates and should not replace professional medical advice.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showInfoDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = NeonPink
                    )
                ) {
                    Text("Close")
                }
            },
            containerColor = NeonCard,
            titleContentColor = NeonPink,
            textContentColor = NeonText
        )
    }

    // Locale Selection Dialog
    if (showLocaleDialog) {
        AlertDialog(
            onDismissRequest = { showLocaleDialog = false },
            title = {
                Text(
                    text = "Select Locale",
                    style = MaterialTheme.typography.titleLarge,
                    color = NeonPink
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    availableLocales.forEach { locale ->
                        val isSelected = locale == selectedLocale
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .clickable {
                                    viewModel.setSelectedLocale(locale)
                                    showLocaleDialog = false
                                    FeedbackUtil.buttonPress(context)
                                },
                            color = if (isSelected) NeonPink.copy(alpha = 0.2f) else Color.Transparent
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = viewModel.getLocaleDisplayName(locale),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (isSelected) NeonPink else NeonText
                                )

                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = NeonPink
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showLocaleDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = NeonPink
                    )
                ) {
                    Text("Cancel")
                }
            },
            containerColor = NeonCard,
            titleContentColor = NeonPink,
            textContentColor = NeonText
        )
    }

    // Main Screen Content
    StandardCalculatorLayout(
        title = "Health Calculator",
        icon = Icons.Default.MonitorHeart,
        accentColor = NeonPink,
        showInfoButton = true,
        onInfoClick = {
            showInfoDialog = true
            FeedbackUtil.buttonPress(context)
        },

        // Input Section
        inputSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Calculator Mode Selection
                Text(
                    text = "Calculator Type",
                    style = MaterialTheme.typography.titleSmall,
                    color = NeonPink
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    HealthCalculatorMode.values().forEach { mode ->
                        NeonButton(
                            onClick = {
                                viewModel.setCalculatorMode(mode)
                                FeedbackUtil.buttonPress(context)
                            },
                            modifier = Modifier.weight(1f),
                            accentColor = if (calculatorMode == mode) NeonPink else NeonPink.copy(alpha = 0.5f)
                        ) {
                            Text(mode.displayName)
                        }
                    }
                }

                // Locale Selection
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Locale: ${viewModel.getLocaleDisplayName(selectedLocale)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    IconButton(
                        onClick = {
                            showLocaleDialog = true
                            FeedbackUtil.buttonPress(context)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Language,
                            contentDescription = "Change Locale",
                            tint = NeonPink
                        )
                    }
                }

                // Common Inputs for BMI and BMR
                if (calculatorMode == HealthCalculatorMode.BMI || calculatorMode == HealthCalculatorMode.BMR) {
                    // Weight Input
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        NeonTextField(
                            value = weight,
                            onValueChange = { viewModel.updateWeight(it) },
                            label = { Text("Weight") },
                            modifier = Modifier.weight(1f),
                            accentColor = NeonPink,
                            isError = error != null,
                            supportingText = { Text("Enter your weight") }
                        )

                        Box {
                            NeonButton(
                                onClick = {
                                    showWeightUnitMenu = true
                                    FeedbackUtil.buttonPress(context)
                                },
                                accentColor = NeonPink
                            ) {
                                Text(weightUnit.symbol)
                            }

                            DropdownMenu(
                                expanded = showWeightUnitMenu,
                                onDismissRequest = { showWeightUnitMenu = false }
                            ) {
                                WeightUnit.values().forEach { unit ->
                                    DropdownMenuItem(
                                        text = { Text(unit.displayName) },
                                        onClick = {
                                            viewModel.setWeightUnit(unit)
                                            showWeightUnitMenu = false
                                            FeedbackUtil.buttonPress(context)
                                        }
                                    )
                                }
                            }
                        }
                    }

                    // Height Input
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        NeonTextField(
                            value = height,
                            onValueChange = { viewModel.updateHeight(it) },
                            label = { Text("Height") },
                            modifier = Modifier.weight(1f),
                            accentColor = NeonPink,
                            isError = error != null,
                            supportingText = { Text("Enter your height") }
                        )

                        Box {
                            NeonButton(
                                onClick = {
                                    showHeightUnitMenu = true
                                    FeedbackUtil.buttonPress(context)
                                },
                                accentColor = NeonPink
                            ) {
                                Text(heightUnit.symbol)
                            }

                            DropdownMenu(
                                expanded = showHeightUnitMenu,
                                onDismissRequest = { showHeightUnitMenu = false }
                            ) {
                                HeightUnit.values().forEach { unit ->
                                    DropdownMenuItem(
                                        text = { Text(unit.displayName) },
                                        onClick = {
                                            viewModel.setHeightUnit(unit)
                                            showHeightUnitMenu = false
                                            FeedbackUtil.buttonPress(context)
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                // BMR-specific inputs
                if (calculatorMode == HealthCalculatorMode.BMR) {
                    // Age Input
                    NeonTextField(
                        value = age,
                        onValueChange = { viewModel.updateAge(it) },
                        label = { Text("Age") },
                        modifier = Modifier.fillMaxWidth(),
                        accentColor = NeonPink,
                        isError = error != null,
                        supportingText = { Text("Enter your age in years") }
                    )

                    // Gender Selection
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Gender:",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText,
                            modifier = Modifier.weight(1f)
                        )

                        Box {
                            NeonButton(
                                onClick = {
                                    showGenderMenu = true
                                    FeedbackUtil.buttonPress(context)
                                },
                                accentColor = NeonPink
                            ) {
                                Text(gender.displayName)
                            }

                            DropdownMenu(
                                expanded = showGenderMenu,
                                onDismissRequest = { showGenderMenu = false }
                            ) {
                                Gender.values().forEach { genderOption ->
                                    DropdownMenuItem(
                                        text = { Text(genderOption.displayName) },
                                        onClick = {
                                            viewModel.setGender(genderOption)
                                            showGenderMenu = false
                                            FeedbackUtil.buttonPress(context)
                                        }
                                    )
                                }
                            }
                        }
                    }

                    // Activity Level Selection
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Activity Level:",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText,
                            modifier = Modifier.weight(1f)
                        )

                        Box {
                            NeonButton(
                                onClick = {
                                    showActivityLevelMenu = true
                                    FeedbackUtil.buttonPress(context)
                                },
                                accentColor = NeonPink
                            ) {
                                Text(activityLevel.displayName)
                            }

                            DropdownMenu(
                                expanded = showActivityLevelMenu,
                                onDismissRequest = { showActivityLevelMenu = false },
                                modifier = Modifier.width(250.dp)
                            ) {
                                ActivityLevel.values().forEach { level ->
                                    DropdownMenuItem(
                                        text = {
                                            Column {
                                                Text(level.displayName)
                                                Text(
                                                    text = level.description,
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = NeonText.copy(alpha = 0.7f)
                                                )
                                            }
                                        },
                                        onClick = {
                                            viewModel.setActivityLevel(level)
                                            showActivityLevelMenu = false
                                            FeedbackUtil.buttonPress(context)
                                        }
                                    )
                                }
                            }
                        }
                    }
                }

                // WHR-specific inputs
                if (calculatorMode == HealthCalculatorMode.WHR) {
                    // Gender Selection for WHR
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Gender:",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText,
                            modifier = Modifier.weight(1f)
                        )

                        Box {
                            NeonButton(
                                onClick = {
                                    showGenderMenu = true
                                    FeedbackUtil.buttonPress(context)
                                },
                                accentColor = NeonPink
                            ) {
                                Text(gender.displayName)
                            }

                            DropdownMenu(
                                expanded = showGenderMenu,
                                onDismissRequest = { showGenderMenu = false }
                            ) {
                                Gender.values().forEach { genderOption ->
                                    DropdownMenuItem(
                                        text = { Text(genderOption.displayName) },
                                        onClick = {
                                            viewModel.setGender(genderOption)
                                            showGenderMenu = false
                                            FeedbackUtil.buttonPress(context)
                                        }
                                    )
                                }
                            }
                        }
                    }

                    // Waist Input
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        NeonTextField(
                            value = waist,
                            onValueChange = { viewModel.updateWaist(it) },
                            label = { Text("Waist Circumference") },
                            modifier = Modifier.weight(1f),
                            accentColor = NeonPink,
                            isError = error != null,
                            supportingText = { Text("Measure at navel level") }
                        )

                        Box {
                            NeonButton(
                                onClick = {
                                    showWaistHipUnitMenu = true
                                    FeedbackUtil.buttonPress(context)
                                },
                                accentColor = NeonPink
                            ) {
                                Text(waistHipUnit.symbol)
                            }

                            DropdownMenu(
                                expanded = showWaistHipUnitMenu,
                                onDismissRequest = { showWaistHipUnitMenu = false }
                            ) {
                                HeightUnit.values().forEach { unit ->
                                    DropdownMenuItem(
                                        text = { Text(unit.displayName) },
                                        onClick = {
                                            viewModel.setWaistHipUnit(unit)
                                            showWaistHipUnitMenu = false
                                            FeedbackUtil.buttonPress(context)
                                        }
                                    )
                                }
                            }
                        }
                    }

                    // Hip Input
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        NeonTextField(
                            value = hip,
                            onValueChange = { viewModel.updateHip(it) },
                            label = { Text("Hip Circumference") },
                            modifier = Modifier.weight(1f),
                            accentColor = NeonPink,
                            isError = error != null,
                            supportingText = { Text("Measure at widest part") }
                        )

                        NeonButton(
                            onClick = { /* Unit is shared with waist */ },
                            accentColor = NeonPink,
                            enabled = false
                        ) {
                            Text(waistHipUnit.symbol)
                        }
                    }
                }
            }
        },

        // Action Buttons
        actionButtons = {
            CalculatorActionButton(
                text = when (calculatorMode) {
                    HealthCalculatorMode.BMI -> "Calculate BMI"
                    HealthCalculatorMode.WHR -> "Calculate WHR"
                    HealthCalculatorMode.BMR -> "Calculate BMR"
                },
                onClick = {
                    when (calculatorMode) {
                        HealthCalculatorMode.BMI -> viewModel.calculateBMI()
                        HealthCalculatorMode.WHR -> viewModel.calculateWHR()
                        HealthCalculatorMode.BMR -> viewModel.calculateBMR()
                    }
                    FeedbackUtil.buttonPress(context)
                },
                accentColor = NeonPink,
                modifier = Modifier.fillMaxWidth(),
                icon = Icons.Default.Calculate
            )
        },

        // Result Section
        resultSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Error message
                if (error != null) {
                    Text(
                        text = error ?: "",
                        color = NeonRed,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                // BMI Result
                if (calculatorMode == HealthCalculatorMode.BMI && bmiResult != null) {
                    bmiResult?.let { result ->
                        // Main Result Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "BMI Result",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = result.category.color
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    Text(
                                        text = "BMI: ${viewModel.formatNumber(result.bmi)}",
                                        style = MaterialTheme.typography.headlineMedium,
                                        color = result.category.color
                                    )
                                }

                                Divider(
                                    color = NeonPink.copy(alpha = 0.3f),
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Category",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = result.category.displayName,
                                        color = result.category.color,
                                        fontWeight = FontWeight.Bold
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Status",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = result.category.description,
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Weight",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = "${viewModel.formatNumber(result.weightKg)} kg",
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Height",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = "${viewModel.formatNumber(result.heightM * 100)} cm",
                                        color = NeonText
                                    )
                                }
                            }
                        }

                        // Health Tips Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "Health Tips for ${result.category.displayName}",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = result.category.color
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                result.healthTips.forEach { tip ->
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = null,
                                            tint = result.category.color,
                                            modifier = Modifier.size(20.dp)
                                        )
                                        Text(
                                            text = tip,
                                            color = NeonText,
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    }
                                }
                            }
                        }

                        // BMI Scale Visualization
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "BMI Scale",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonPink
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                // BMI Scale visualization
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(24.dp)
                                        .clip(RoundedCornerShape(12.dp))
                                        .background(
                                            brush = Brush.horizontalGradient(
                                                colors = listOf(
                                                    BMICategory.UNDERWEIGHT.color,
                                                    BMICategory.NORMAL.color,
                                                    BMICategory.OVERWEIGHT.color,
                                                    BMICategory.OBESE.color
                                                )
                                            )
                                        )
                                )

                                // BMI Scale labels
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "< 18.5",
                                        color = BMICategory.UNDERWEIGHT.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "18.5-24.9",
                                        color = BMICategory.NORMAL.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "25-29.9",
                                        color = BMICategory.OVERWEIGHT.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "≥ 30",
                                        color = BMICategory.OBESE.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }

                                // BMI Scale categories
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Underweight",
                                        color = BMICategory.UNDERWEIGHT.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "Normal",
                                        color = BMICategory.NORMAL.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "Overweight",
                                        color = BMICategory.OVERWEIGHT.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                    Text(
                                        text = "Obese",
                                        color = BMICategory.OBESE.color,
                                        style = MaterialTheme.typography.bodySmall
                                    )
                                }
                            }
                        }
                    }
                }

                // WHR Result
                if (calculatorMode == HealthCalculatorMode.WHR && whrResult != null) {
                    whrResult?.let { result ->
                        // Main Result Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "Waist-Hip Ratio Result",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = result.category.color
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    Text(
                                        text = "WHR: ${viewModel.formatNumber(result.whr, 2)}",
                                        style = MaterialTheme.typography.headlineMedium,
                                        color = result.category.color
                                    )
                                }

                                Divider(
                                    color = NeonPink.copy(alpha = 0.3f),
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Category",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = result.category.displayName,
                                        color = result.category.color,
                                        fontWeight = FontWeight.Bold
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Status",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = result.category.description,
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Waist",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = "${viewModel.formatNumber(result.waistCm)} cm",
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Hip",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = "${viewModel.formatNumber(result.hipCm)} cm",
                                        color = NeonText
                                    )
                                }
                            }
                        }

                        // Health Tips Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "Health Tips for ${result.category.displayName}",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = result.category.color
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                result.healthTips.forEach { tip ->
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = null,
                                            tint = result.category.color,
                                            modifier = Modifier.size(20.dp)
                                        )
                                        Text(
                                            text = tip,
                                            color = NeonText,
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    }
                                }
                            }
                        }

                        // WHR Information Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "WHR Health Risk",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonPink
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                Text(
                                    text = "Waist-to-hip ratio (WHR) is a measure of body fat distribution. Higher WHR indicates more fat stored around the waist, which is associated with increased health risks.",
                                    color = NeonText,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Text(
                                    text = "Risk Categories:",
                                    color = NeonPink,
                                    style = MaterialTheme.typography.titleSmall
                                )

                                if (gender == Gender.MALE) {
                                    Text(
                                        text = "• Low Risk: < 0.90\n• Moderate Risk: 0.90 - 0.99\n• High Risk: ≥ 1.0",
                                        color = NeonText,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                } else {
                                    Text(
                                        text = "• Low Risk: < 0.80\n• Moderate Risk: 0.80 - 0.84\n• High Risk: ≥ 0.85",
                                        color = NeonText,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                    }
                }

                // BMR Result
                if (calculatorMode == HealthCalculatorMode.BMR && bmrResult != null) {
                    bmrResult?.let { result ->
                        // Main Result Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "Metabolic Rate Results",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonPink
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Text(
                                            text = "BMR",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = NeonText.copy(alpha = 0.7f)
                                        )
                                        Text(
                                            text = "${viewModel.formatNumber(result.bmr, 0)} calories/day",
                                            style = MaterialTheme.typography.headlineSmall,
                                            color = NeonPink
                                        )
                                    }
                                }

                                Spacer(modifier = Modifier.height(8.dp))

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Text(
                                            text = "Daily Calorie Needs",
                                            style = MaterialTheme.typography.titleSmall,
                                            color = NeonText.copy(alpha = 0.7f)
                                        )
                                        Text(
                                            text = "${viewModel.formatNumber(result.dailyCalories, 0)} calories/day",
                                            style = MaterialTheme.typography.headlineSmall,
                                            color = NeonPink
                                        )
                                    }
                                }

                                Divider(
                                    color = NeonPink.copy(alpha = 0.3f),
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Gender",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = result.gender.displayName,
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Age",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = "${result.age} years",
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Weight",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = "${viewModel.formatNumber(result.weightKg)} kg",
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Height",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = "${viewModel.formatNumber(result.heightCm)} cm",
                                        color = NeonText
                                    )
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Activity Level",
                                        color = NeonText.copy(alpha = 0.7f)
                                    )
                                    Text(
                                        text = result.activityLevel.displayName,
                                        color = NeonText
                                    )
                                }
                            }
                        }

                        // Health Tips Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "Nutrition & Activity Tips",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonPink
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                result.healthTips.forEach { tip ->
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = null,
                                            tint = NeonPink,
                                            modifier = Modifier.size(20.dp)
                                        )
                                        Text(
                                            text = tip,
                                            color = NeonText,
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    }
                                }
                            }
                        }

                        // BMR Information Card
                        NeonCard(
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Text(
                                    text = "About Metabolic Rate",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonPink
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                Text(
                                    text = "Basal Metabolic Rate (BMR) is the number of calories your body needs to maintain basic functions at rest.",
                                    color = NeonText,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Text(
                                    text = "Daily calorie needs are calculated by multiplying your BMR by an activity factor based on your lifestyle.",
                                    color = NeonText,
                                    style = MaterialTheme.typography.bodyMedium
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Text(
                                    text = "For weight management:",
                                    color = NeonPink,
                                    style = MaterialTheme.typography.titleSmall
                                )

                                Text(
                                    text = "• To maintain weight: Consume your daily calorie needs\n• To lose weight: Consume 500-1000 calories less per day\n• To gain weight: Consume 500-1000 calories more per day",
                                    color = NeonText,
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                // No result yet
                if ((calculatorMode == HealthCalculatorMode.BMI && bmiResult == null) ||
                    (calculatorMode == HealthCalculatorMode.WHR && whrResult == null) ||
                    (calculatorMode == HealthCalculatorMode.BMR && bmrResult == null)) {

                    if (error == null) {
                        Text(
                            text = "Enter your measurements and click Calculate to see results",
                            style = MaterialTheme.typography.bodyLarge,
                            color = NeonText.copy(alpha = 0.7f),
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }
        }
    )
}