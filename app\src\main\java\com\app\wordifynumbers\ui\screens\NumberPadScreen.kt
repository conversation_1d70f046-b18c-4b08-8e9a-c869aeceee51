package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.background
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.ExpressionEvaluator
import com.app.wordifynumbers.util.FeedbackUtil

@Composable
fun NumberPadScreen(modifier: Modifier = Modifier) {
    var expression by remember { mutableStateOf("") }
    var result by remember { mutableStateOf("") }
    var lastOperation by remember { mutableStateOf("") }
    var showHistory by remember { mutableStateOf(false) }
    var calculations by remember { mutableStateOf(listOf<String>()) }
    var memory by remember { mutableStateOf(0.0) }
    val context = LocalContext.current

    ScreenWrapper(
        title = "Calculator",
        description = "Scientific calculator with history"
    ) {
        Column(
            modifier = modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Display Section
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // Expression
                    Text(
                        text = expression,
                        style = MaterialTheme.typography.titleLarge,
                        color = NeonText.copy(alpha = 0.7f),
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Result with animation
                    NeonPulse {
                        Text(
                            text = result,
                            style = MaterialTheme.typography.headlineLarge,
                            color = NeonGlow,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            // Number Pad Grid
            NeonCard(
                modifier = Modifier.weight(1f)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    // Function buttons row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        FunctionButton("MC", Icons.Default.Clear) { memory = 0.0 }
                        FunctionButton("MR", Icons.Default.Refresh) { expression = memory.toString() }
                        FunctionButton("M+", Icons.Default.Add) { memory += expression.toDoubleOrNull() ?: 0.0 }
                        FunctionButton("M-", Icons.Default.Remove) { memory -= expression.toDoubleOrNull() ?: 0.0 }
                        FunctionButton("MS", Icons.Default.Save) { memory = expression.toDoubleOrNull() ?: 0.0 }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Main number pad grid
                    val buttons = listOf(
                        "C", "(", ")", "÷",
                        "7", "8", "9", "×",
                        "4", "5", "6", "-",
                        "1", "2", "3", "+",
                        "0", ".", "⌫", "="
                    )

                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        buttons.chunked(4).forEach { row ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                row.forEach { button ->
                                    NumberPadButton(
                                        text = button,
                                        isOperator = button in listOf("÷", "×", "-", "+", "="),
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        when (button) {
                                            "=" -> {
                                                try {
                                                    val evaluatedValue = ExpressionEvaluator.evaluate(expression)

                                                    // Format the result for better readability
                                                    val formattedResult = when {
                                                        evaluatedValue.isInfinite() -> if (evaluatedValue > 0) "∞" else "-∞"
                                                        evaluatedValue.isNaN() -> "Error"
                                                        evaluatedValue == evaluatedValue.toLong().toDouble() -> evaluatedValue.toLong().toString()
                                                        else -> "%.8f".format(evaluatedValue).trimEnd('0').trimEnd('.')
                                                    }

                                                    result = formattedResult
                                                    calculations = calculations + "$expression = $formattedResult"
                                                    lastOperation = expression
                                                    FeedbackUtil.buttonPress(context)
                                                } catch (e: Exception) {
                                                    result = "Error: ${e.message?.take(30) ?: "Invalid expression"}"
                                                    FeedbackUtil.errorFeedback(context)
                                                }
                                            }
                                            "C" -> {
                                                expression = ""
                                                result = ""
                                                FeedbackUtil.buttonPress(context)
                                            }
                                            "⌫" -> {
                                                if (expression.isNotEmpty()) {
                                                    expression = expression.dropLast(1)
                                                    FeedbackUtil.buttonPress(context)
                                                }
                                            }
                                            else -> {
                                                expression += button
                                                try {
                                                    val evaluatedValue = ExpressionEvaluator.evaluateSafe(expression)
                                                    if (evaluatedValue != null) {
                                                        // Format the result for better readability
                                                        result = when {
                                                            evaluatedValue.isInfinite() -> if (evaluatedValue > 0) "∞" else "-∞"
                                                            evaluatedValue.isNaN() -> "Error"
                                                            evaluatedValue == evaluatedValue.toLong().toDouble() -> evaluatedValue.toLong().toString()
                                                            else -> "%.8f".format(evaluatedValue).trimEnd('0').trimEnd('.')
                                                        }
                                                    }
                                                } catch (e: Exception) {
                                                    // Ignore evaluation errors while typing
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // History Section
            NeonSlide(visible = showHistory) {
                NeonCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "Calculation History",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonGlow
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            calculations.takeLast(5).forEach { calc ->
                                NeonShimmer {
                                    Text(
                                        text = calc,
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = NeonText
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun FunctionButton(
    text: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    val context = LocalContext.current
    var pressed by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .clip(CircleShape)
            .clickable {
                pressed = true
                FeedbackUtil.buttonPress(context)
                onClick()
            }
            .padding(8.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = text,
                tint = NeonText,
                modifier = Modifier.size(20.dp)
            )
            Text(
                text = text,
                style = MaterialTheme.typography.bodySmall,
                color = NeonText
            )
        }
    }
}

@Composable
private fun NumberPadButton(
    text: String,
    isOperator: Boolean,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    val context = LocalContext.current
    var pressed by remember { mutableStateOf(false) }

    LaunchedEffect(pressed) {
        if (pressed) {
            kotlinx.coroutines.delay(100)
            pressed = false
        }
    }

    Box(
        modifier = modifier
            .clip(CircleShape)
            .clickable {
                pressed = true
                FeedbackUtil.buttonPress(context)
                onClick()
            }
            .background(
                if (isOperator) NeonGlow.copy(alpha = 0.1f)
                else NeonCard.copy(alpha = 0.5f)
            )
            .padding(16.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.headlineSmall,
            color = if (isOperator) NeonGlow else NeonText,
            modifier = Modifier.graphicsLayer {
                scaleX = if (pressed) 0.9f else 1f
                scaleY = if (pressed) 0.9f else 1f
            }
        )
    }
}

private fun evaluateExpression(expr: String): String {
    return try {
        val result = com.app.wordifynumbers.util.ExpressionEvaluator.evaluate(expr)
        result.toString()
    } catch (e: Exception) {
        "Error: ${e.message}"
    }
}
