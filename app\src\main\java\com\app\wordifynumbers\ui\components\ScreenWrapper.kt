package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween

/**
 * A wrapper for all app screens to apply consistent theme and header with neon effects.
 * @param title Screen title.
 * @param description Brief introduction of the screen.
 * @param actions Optional actions to be displayed in the header.
 * @param content The main content composable for the screen.
 */
@Composable
fun ScreenWrapper(
    title: String,
    description: String,
    actions: @Composable RowScope.() -> Unit = {},
    content: @Composable () -> Unit
) {
    // Multiple animated angles for complex background effects
    var gradientAngle by remember { mutableStateOf(0f) }
    val pulseScaleAnim = remember { Animatable(1f) }
    val glowOpacityAnim = remember { Animatable(0.3f) }
    val pulseScale: Float = pulseScaleAnim.value
    val glowOpacity: Float = glowOpacityAnim.value
    
    // Animate multiple properties simultaneously
    LaunchedEffect(Unit) {
        launch {
            while (true) {
                delay(50)
                gradientAngle = (gradientAngle + 1) % 360
            }
        }
        launch {
            while (true) {
                // Pulse animation
                pulseScaleAnim.animateTo(1.05f, animationSpec = tween(durationMillis = 600))
                pulseScaleAnim.animateTo(1f, animationSpec = tween(durationMillis = 600))
            }
        }
        launch {
            while (true) {
                // Glow animation
                glowOpacityAnim.animateTo(0.5f, animationSpec = tween(durationMillis = 1000))
                glowOpacityAnim.animateTo(0.3f, animationSpec = tween(durationMillis = 1000))
            }
        }
    }

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .drawBehind {
                    // Create animated neon glow gradients in the background
                    val primaryGradient = listOf(
                        NeonGlow.copy(alpha = glowOpacity * 0.3f),
                        Color.Transparent,
                        NeonPurple.copy(alpha = glowOpacity * 0.15f),
                        Color.Transparent
                    )
                    
                    val secondaryGradient = listOf(
                        NeonBlue.copy(alpha = glowOpacity * 0.2f),
                        Color.Transparent,
                        NeonPink.copy(alpha = glowOpacity * 0.1f),
                        Color.Transparent
                    )
                    
                    // Primary gradient
                    val angleRad = Math.toRadians(gradientAngle.toDouble()).toFloat()
                    val length = size.width.coerceAtLeast(size.height)
                    val endX = (length * kotlin.math.cos(angleRad))
                    val endY = (length * kotlin.math.sin(angleRad))
                    
                    drawLine(
                        brush = Brush.linearGradient(primaryGradient),
                        start = Offset(0f, 0f),
                        end = Offset(endX, endY),
                        strokeWidth = 4.dp.toPx()
                    )
                    
                    // Secondary crossing gradient
                    val angle2Rad = Math.toRadians((gradientAngle + 120).toDouble()).toFloat()
                    val end2X = (length * kotlin.math.cos(angle2Rad))
                    val end2Y = (length * kotlin.math.sin(angle2Rad))
                    
                    drawLine(
                        brush = Brush.linearGradient(secondaryGradient),
                        start = Offset(size.width, size.height),
                        end = Offset(end2X, end2Y),
                        strokeWidth = 4.dp.toPx()
                    )
                }
                .padding(16.dp)
        ) {
            // Header section with enhanced neon effects
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp))
                    .graphicsLayer {
                        scaleX = pulseScale
                        scaleY = pulseScale
                    }
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                NeonCard.copy(alpha = 0.3f),
                                NeonCard.copy(alpha = 0.1f)
                            )
                        )
                    )
                    .drawWithContent {
                        drawContent()
                        // Draw animated neon border glow
                        drawRect(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    NeonGlow.copy(alpha = glowOpacity),
                                    NeonGlow.copy(alpha = 0.0f)
                                )
                            ),
                            size = size.copy(height = 2.dp.toPx())
                        )
                    }
                    .padding(16.dp)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = NeonGlow,
                    modifier = Modifier
                        .graphicsLayer {
                            alpha = 0.9f + (kotlin.math.sin(gradientAngle * Math.PI / 180) * 0.1f).toFloat()
                        }
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = NeonText.copy(alpha = 0.8f)
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    actions()
                }
            }

            Spacer(modifier = Modifier.height(24.dp))
            
            // Enhanced content wrapper with dynamic effects
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(24.dp))
                    .background(
                        Brush.verticalGradient(
                            colors = listOf(
                                MaterialTheme.colorScheme.surface.copy(alpha = 0.95f),
                                MaterialTheme.colorScheme.surface.copy(alpha = 0.85f)
                            )
                        )
                    )
                    .drawWithContent {
                        drawContent()
                        // Draw animated neon edge glow
                        drawRect(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    NeonGlow.copy(alpha = glowOpacity * 0.6f),
                                    Color.Transparent
                                )
                            ),
                            size = size.copy(height = 3.dp.toPx())
                        )
                        // Draw side glow
                        drawRect(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    NeonGlow.copy(alpha = glowOpacity * 0.3f),
                                    Color.Transparent,
                                    Color.Transparent,
                                    NeonGlow.copy(alpha = glowOpacity * 0.3f)
                                )
                            ),
                            size = size
                        )
                    }
                    .padding(16.dp)
            ) {
                content()
            }
        }
    }
}
