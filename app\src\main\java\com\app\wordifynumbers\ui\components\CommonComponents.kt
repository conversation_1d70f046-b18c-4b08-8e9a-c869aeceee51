@file:OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
package com.app.wordifynumbers.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.NavigationBarItemColors
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import androidx.compose.animation.core.*

@Composable
fun NeonCard(
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    tonalElevation: Dp = 0.dp,
    content: @Composable () -> Unit
) {
    val infiniteTransition = rememberInfiniteTransition(label = "cardGlow")
    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Surface(
        modifier = modifier.shadow(
            elevation = 8.dp,
            spotColor = accentColor.copy(alpha = glowOpacity * 0.5f),
            ambientColor = accentColor.copy(alpha = glowOpacity * 0.25f),
            shape = RoundedCornerShape(20.dp)
        ),
        shape = RoundedCornerShape(20.dp),
        color = NeonCard,
        tonalElevation = tonalElevation,
        content = {
            Box(
                modifier = Modifier
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                NeonCard.copy(alpha = 0.95f),
                                accentColor.copy(alpha = 0.05f)
                            )
                        )
                    )
            ) {
                content()
            }
        }
    )
}

@Composable
fun NeonButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    accentColor: Color = NeonBlue,
    colors: ButtonColors = ButtonDefaults.buttonColors(
        containerColor = accentColor,
        contentColor = NeonText,
        disabledContainerColor = NeonCard,
        disabledContentColor = NeonHint
    ),
    content: @Composable RowScope.() -> Unit
) {
    val infiniteTransition = rememberInfiniteTransition(label = "buttonGlow")
    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Button(
        onClick = onClick,
        modifier = modifier.shadow(
            elevation = if (enabled) 8.dp else 2.dp,
            spotColor = accentColor.copy(alpha = if (enabled) glowOpacity * 0.6f else 0.1f),
            ambientColor = accentColor.copy(alpha = if (enabled) glowOpacity * 0.3f else 0.05f),
            shape = RoundedCornerShape(12.dp)
        ),
        enabled = enabled,
        colors = colors,
        shape = RoundedCornerShape(12.dp),
        content = content
    )
}

@Composable
fun NeonTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    label: @Composable (() -> Unit)? = null,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    isError: Boolean = false,
    singleLine: Boolean = true,
    accentColor: Color = NeonGlow,
    trailingIcon: (@Composable (() -> Unit))? = null,
    supportingText: (@Composable (() -> Unit))? = null
) {
    val infiniteTransition = rememberInfiniteTransition(label = "textFieldGlow")
    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    TextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier.shadow(
            elevation = 6.dp,
            spotColor = accentColor.copy(alpha = if (enabled) glowOpacity * 0.4f else 0.1f),
            ambientColor = accentColor.copy(alpha = if (enabled) glowOpacity * 0.2f else 0.05f),
            shape = RoundedCornerShape(16.dp)
        ),
        enabled = enabled,
        readOnly = readOnly,
        label = label,
        isError = isError,
        singleLine = singleLine,
        trailingIcon = trailingIcon,
        supportingText = supportingText,
        colors = TextFieldDefaults.colors(
            focusedContainerColor = NeonCard,
            unfocusedContainerColor = NeonCard,
            disabledContainerColor = NeonCard,
            focusedTextColor = NeonText,
            unfocusedTextColor = NeonText,
            focusedLabelColor = accentColor,
            unfocusedLabelColor = NeonHint,
            cursorColor = accentColor,
            focusedIndicatorColor = accentColor,
            unfocusedIndicatorColor = accentColor.copy(alpha = 0.5f),
            errorIndicatorColor = NeonRed,
            errorLabelColor = NeonRed,
            errorSupportingTextColor = NeonRed,
            errorCursorColor = NeonRed
        ),
        shape = RoundedCornerShape(16.dp)
    )
}

@Composable
fun NeonDivider(
    modifier: Modifier = Modifier,
    thickness: Dp = 1.dp,
    color: Color = NeonGlow.copy(alpha = 0.2f)
) {
    Divider(
        modifier = modifier,
        thickness = thickness,
        color = color
    )
}

@Composable
fun NeonDropdownMenu(
    expanded: Boolean,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    DropdownMenu(
        expanded = expanded,
        onDismissRequest = onDismissRequest,
        modifier = modifier.background(NeonCard),
        content = content
    )
}

@Composable
fun NeonDropdownMenuItem(
    text: @Composable () -> Unit,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    DropdownMenuItem(
        text = text,
        onClick = onClick,
        modifier = modifier,
        colors = MenuDefaults.itemColors(
            textColor = NeonText,
            leadingIconColor = NeonGlow,
            trailingIconColor = NeonGlow
        )
    )
}

@Composable
fun NeonTabRow(
    selectedTabIndex: Int,
    modifier: Modifier = Modifier,
    tabs: @Composable () -> Unit
) {
    TabRow(
        selectedTabIndex = selectedTabIndex,
        modifier = modifier,
        containerColor = NeonCard,
        contentColor = NeonGlow,
        indicator = { tabPositions ->
            TabRowDefaults.Indicator(
                modifier = Modifier.tabIndicatorOffset(tabPositions[selectedTabIndex]),
                color = NeonGlow
            )
        },
        tabs = tabs
    )
}

@Composable
fun NeonTab(
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    Tab(
        selected = selected,
        onClick = onClick,
        modifier = modifier,
        selectedContentColor = NeonGlow,
        unselectedContentColor = NeonText.copy(alpha = 0.6f),
        content = content
    )
}

@Composable
fun SimpleNeonBottomNavigation(
    modifier: Modifier = Modifier,
    content: @Composable RowScope.() -> Unit
) {
    NavigationBar(
        modifier = modifier,
        containerColor = NeonNav,
        content = content
    )
}

@Composable
fun RowScope.NeonNavigationBarItem(
    selected: Boolean,
    onClick: () -> Unit,
    icon: @Composable () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    label: @Composable (() -> Unit)? = null,
    colors: NavigationBarItemColors = NavigationBarItemDefaults.colors(
        selectedIconColor = NeonGlow,
        unselectedIconColor = NeonText,
        selectedTextColor = NeonGlow,
        unselectedTextColor = NeonText,
        indicatorColor = NeonCard
    )
) {
    NavigationBarItem(
        selected = selected,
        onClick = onClick,
        icon = icon,
        modifier = modifier,
        enabled = enabled,
        label = label ?: {},
        colors = colors
    )
}

@Composable
fun NeonTooltipBox(
    tooltip: @Composable () -> Unit,
    content: @Composable () -> Unit
) {
    // Simple stub for tooltips
    content()
}
