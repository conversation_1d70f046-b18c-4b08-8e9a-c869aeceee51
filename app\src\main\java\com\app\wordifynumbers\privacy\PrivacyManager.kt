package com.app.wordifynumbers.privacy

import android.content.Context
import android.content.SharedPreferences
import androidx.compose.runtime.*
import com.app.wordifynumbers.security.SecurityManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.*

/**
 * Privacy manager for handling GDPR compliance, data protection,
 * and user privacy preferences in accordance with Google Play policies.
 */
object PrivacyManager {
    
    private const val PRIVACY_PREFS = "privacy_preferences"
    private const val CONSENT_VERSION = "consent_version"
    private const val CURRENT_CONSENT_VERSION = 1
    
    private val _privacyState = MutableStateFlow(PrivacyState())
    val privacyState: StateFlow<PrivacyState> = _privacyState.asStateFlow()
    
    /**
     * Initialize privacy manager
     */
    fun initialize(context: Context) {
        loadPrivacyPreferences(context)
    }
    
    /**
     * Check if user has given consent for the current version
     */
    fun hasValidConsent(context: Context): Boolean {
        val prefs = getPrivacyPreferences(context)
        val consentVersion = prefs.getInt(CONSENT_VERSION, 0)
        return consentVersion >= CURRENT_CONSENT_VERSION && 
               _privacyState.value.hasGivenConsent
    }
    
    /**
     * Record user consent
     */
    fun recordConsent(context: Context, consent: UserConsent) {
        val prefs = getPrivacyPreferences(context)
        prefs.edit().apply {
            putBoolean("analytics_consent", consent.analyticsConsent)
            putBoolean("crash_reporting_consent", consent.crashReportingConsent)
            putBoolean("performance_monitoring_consent", consent.performanceMonitoringConsent)
            putBoolean("personalization_consent", consent.personalizationConsent)
            putInt(CONSENT_VERSION, CURRENT_CONSENT_VERSION)
            putLong("consent_timestamp", System.currentTimeMillis())
            putString("consent_ip", "not_collected") // We don't collect IP addresses
            apply()
        }
        
        updatePrivacyState(context)
    }
    
    /**
     * Withdraw consent
     */
    fun withdrawConsent(context: Context) {
        val prefs = getPrivacyPreferences(context)
        prefs.edit().clear().apply()
        
        // Clear all user data
        clearAllUserData(context)
        
        updatePrivacyState(context)
    }
    
    /**
     * Get current user consent
     */
    fun getCurrentConsent(context: Context): UserConsent {
        val prefs = getPrivacyPreferences(context)
        return UserConsent(
            analyticsConsent = prefs.getBoolean("analytics_consent", false),
            crashReportingConsent = prefs.getBoolean("crash_reporting_consent", false),
            performanceMonitoringConsent = prefs.getBoolean("performance_monitoring_consent", false),
            personalizationConsent = prefs.getBoolean("personalization_consent", false)
        )
    }
    
    /**
     * Export user data (GDPR Article 20 - Right to data portability)
     */
    fun exportUserData(context: Context): UserDataExport {
        val prefs = getPrivacyPreferences(context)
        val regularPrefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        
        return UserDataExport(
            consentData = getCurrentConsent(context),
            consentTimestamp = prefs.getLong("consent_timestamp", 0),
            appPreferences = extractAppPreferences(regularPrefs),
            calculatorHistory = extractCalculatorHistory(context),
            financeData = extractFinanceData(context),
            exportTimestamp = System.currentTimeMillis(),
            dataVersion = "1.0"
        )
    }
    
    /**
     * Clear all user data (GDPR Article 17 - Right to erasure)
     */
    fun clearAllUserData(context: Context) {
        // Clear privacy preferences
        getPrivacyPreferences(context).edit().clear().apply()
        
        // Clear app preferences
        context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
            .edit().clear().apply()
        
        // Clear secure storage
        SecurityManager.SecureStorage.clearAllSecureData(context)
        
        // Clear any cached data
        clearCachedData(context)
        
        updatePrivacyState(context)
    }
    
    /**
     * Get data retention policy
     */
    fun getDataRetentionPolicy(): DataRetentionPolicy {
        return DataRetentionPolicy(
            calculatorHistory = "30 days",
            financeData = "Until user deletion",
            crashReports = "90 days",
            analyticsData = "26 months",
            performanceData = "90 days",
            userPreferences = "Until app uninstall"
        )
    }
    
    /**
     * Check if data collection is allowed for specific purpose
     */
    fun isDataCollectionAllowed(context: Context, purpose: DataCollectionPurpose): Boolean {
        if (!hasValidConsent(context)) return false
        
        val consent = getCurrentConsent(context)
        return when (purpose) {
            DataCollectionPurpose.ANALYTICS -> consent.analyticsConsent
            DataCollectionPurpose.CRASH_REPORTING -> consent.crashReportingConsent
            DataCollectionPurpose.PERFORMANCE_MONITORING -> consent.performanceMonitoringConsent
            DataCollectionPurpose.PERSONALIZATION -> consent.personalizationConsent
            DataCollectionPurpose.ESSENTIAL -> true // Always allowed for app functionality
        }
    }
    
    /**
     * Get privacy preferences
     */
    private fun getPrivacyPreferences(context: Context): SharedPreferences {
        return SecurityManager.getSecureSharedPreferences(context)
    }
    
    /**
     * Load privacy preferences
     */
    private fun loadPrivacyPreferences(context: Context) {
        updatePrivacyState(context)
    }
    
    /**
     * Update privacy state
     */
    private fun updatePrivacyState(context: Context) {
        val hasConsent = hasValidConsent(context)
        val consent = if (hasConsent) getCurrentConsent(context) else null
        
        _privacyState.value = PrivacyState(
            hasGivenConsent = hasConsent,
            userConsent = consent,
            lastUpdated = System.currentTimeMillis()
        )
    }
    
    /**
     * Extract app preferences for export
     */
    private fun extractAppPreferences(prefs: SharedPreferences): Map<String, Any> {
        return prefs.all.filterValues { it != null }.mapValues { it.value!! }
    }
    
    /**
     * Extract calculator history for export
     */
    private fun extractCalculatorHistory(context: Context): List<String> {
        val prefs = context.getSharedPreferences("app_preferences", Context.MODE_PRIVATE)
        return prefs.getStringSet("calculator_history", emptySet())?.toList() ?: emptyList()
    }
    
    /**
     * Extract finance data for export
     */
    private fun extractFinanceData(context: Context): Map<String, Any> {
        // This would extract finance data from Room database or preferences
        // For now, return empty map as finance data is stored locally
        return emptyMap()
    }
    
    /**
     * Clear cached data
     */
    private fun clearCachedData(context: Context) {
        // Clear any cached files or temporary data
        context.cacheDir.deleteRecursively()
    }
    
    /**
     * Data classes for privacy management
     */
    data class PrivacyState(
        val hasGivenConsent: Boolean = false,
        val userConsent: UserConsent? = null,
        val lastUpdated: Long = 0
    )
    
    data class UserConsent(
        val analyticsConsent: Boolean = false,
        val crashReportingConsent: Boolean = false,
        val performanceMonitoringConsent: Boolean = false,
        val personalizationConsent: Boolean = false
    )
    
    data class UserDataExport(
        val consentData: UserConsent,
        val consentTimestamp: Long,
        val appPreferences: Map<String, Any>,
        val calculatorHistory: List<String>,
        val financeData: Map<String, Any>,
        val exportTimestamp: Long,
        val dataVersion: String
    )
    
    data class DataRetentionPolicy(
        val calculatorHistory: String,
        val financeData: String,
        val crashReports: String,
        val analyticsData: String,
        val performanceData: String,
        val userPreferences: String
    )
    
    enum class DataCollectionPurpose {
        ANALYTICS,
        CRASH_REPORTING,
        PERFORMANCE_MONITORING,
        PERSONALIZATION,
        ESSENTIAL
    }
}

/**
 * Privacy policy content
 */
object PrivacyPolicyContent {
    
    const val PRIVACY_POLICY_VERSION = "1.0"
    const val LAST_UPDATED = "2024-01-01"
    
    const val PRIVACY_POLICY_TEXT = """
        PRIVACY POLICY FOR WORDIFY NUMBERS
        
        Last Updated: $LAST_UPDATED
        Version: $PRIVACY_POLICY_VERSION
        
        1. INFORMATION WE COLLECT
        We collect minimal information to provide our services:
        - App usage data for improving functionality
        - Crash reports to fix bugs (optional)
        - Performance data to optimize the app (optional)
        
        2. HOW WE USE INFORMATION
        - To provide and improve our services
        - To fix bugs and technical issues
        - To analyze app performance
        
        3. DATA STORAGE
        - All data is stored locally on your device
        - No personal information is transmitted to external servers
        - You can delete all data at any time
        
        4. YOUR RIGHTS
        - Right to access your data
        - Right to delete your data
        - Right to withdraw consent
        - Right to data portability
        
        5. CONTACT US
        For privacy concerns, contact us at: <EMAIL>
    """
    
    fun getPrivacyPolicyUrl(): String {
        return "https://wordifynumbers.com/privacy-policy"
    }
    
    fun getTermsOfServiceUrl(): String {
        return "https://wordifynumbers.com/terms-of-service"
    }
}
