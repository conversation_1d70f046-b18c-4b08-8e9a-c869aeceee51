package com.app.wordifynumbers.ui.screens

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.NumberConverterViewModel
import com.app.wordifynumbers.NumberConverterViewModelFactory
import com.app.wordifynumbers.ui.components.WordifyHeaderAction
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.FeedbackUtil
import com.app.wordifynumbers.utils.NumberToWords

/**
 * Screen that shows detailed digit to word translations with digits in parentheses
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DigitToWordDetailsScreen(
    selectedLanguage: String,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val viewModel: NumberConverterViewModel = viewModel(factory = NumberConverterViewModelFactory(context))
    val digitTranslations by viewModel.digitTranslations.collectAsState()
    val scrollState = rememberScrollState()

    // Ensure we have the correct language selected
    LaunchedEffect(selectedLanguage) {
        viewModel.onLanguageSelected(selectedLanguage)
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        NeonBackground,
                        NeonBackground.copy(alpha = 0.9f)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            // Custom header with app name on top (matching Finance Notepad)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp, vertical = 6.dp)
                    .shadow(
                        elevation = 10.dp,
                        spotColor = NeonGold.copy(alpha = 0.3f),
                        ambientColor = NeonGold.copy(alpha = 0.2f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.95f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp, horizontal = 12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // App name at the top
                    Text(
                        text = "Wordify Numbers",
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 0.5.sp
                        ),
                        color = NeonGold,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Divider with glow effect
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(0.95f)
                            .height(1.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        NeonGold.copy(alpha = 0.3f),
                                        NeonGold.copy(alpha = 0.5f),
                                        NeonGold.copy(alpha = 0.3f),
                                        Color.Transparent
                                    )
                                )
                            )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Screen title and actions in a row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Back button and screen title
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            IconButton(onClick = onBackClick) {
                                Icon(
                                    imageVector = Icons.Default.ArrowBack,
                                    contentDescription = "Back",
                                    tint = NeonGold
                                )
                            }

                            Text(
                                text = "$selectedLanguage Digit Details",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 18.sp
                                ),
                                color = NeonGold
                            )
                        }

                        // Action buttons
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            WordifyHeaderAction(
                                icon = Icons.Default.Info,
                                contentDescription = "Language Info",
                                accentColor = NeonGold,
                                onClick = {
                                    // Show language info
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Main content
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Title
                Text(
                    text = "DETAILED DIGIT TRANSLATIONS",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        letterSpacing = 1.sp
                    ),
                    color = NeonGold,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    textAlign = TextAlign.Center
                )

                // Subtitle
                Text(
                    text = "Learn how to say digits in $selectedLanguage",
                    style = MaterialTheme.typography.bodyLarge,
                    color = NeonText,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )

                // Digit translations with detailed format
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = NeonGold.copy(alpha = 0.2f),
                            ambientColor = NeonGold.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(
                        1.dp,
                        SolidColor(if (selectedLanguage == "Urdu") NeonGlow.copy(alpha = 0.5f) else NeonGold.copy(alpha = 0.3f))
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // Header
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Digit",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = NeonGold
                            )

                            Text(
                                text = "Translation",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = NeonGold
                            )
                        }

                        // Divider
                        Divider(
                            color = NeonGold.copy(alpha = 0.3f),
                            thickness = 1.dp,
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Digit translations
                        digitTranslations.forEach { (digit, translation) ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Digit with background
                                Box(
                                    modifier = Modifier
                                        .size(48.dp)
                                        .background(
                                            color = if (selectedLanguage == "Urdu") NeonGlow.copy(alpha = 0.2f) else NeonGold.copy(alpha = 0.2f),
                                            shape = RoundedCornerShape(8.dp)
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = digit,
                                        style = MaterialTheme.typography.headlineMedium.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = if (selectedLanguage == "Urdu") NeonGlow else NeonGold
                                    )
                                }

                                // Translation
                                Text(
                                    text = translation,
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = NeonText,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.End
                                )
                            }

                            // Divider between items (except after the last one)
                            if (digit != "9") {
                                Divider(
                                    color = NeonGold.copy(alpha = 0.1f),
                                    thickness = 0.5.dp,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }
                        }
                    }
                }

                // Example numbers section
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = NeonGold.copy(alpha = 0.2f),
                            ambientColor = NeonGold.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(
                        1.dp,
                        SolidColor(if (selectedLanguage == "Urdu") NeonGlow.copy(alpha = 0.5f) else NeonGold.copy(alpha = 0.3f))
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // Header
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Lightbulb,
                                contentDescription = null,
                                tint = if (selectedLanguage == "Urdu") NeonGlow else NeonGold
                            )

                            Text(
                                text = "Example Numbers",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = if (selectedLanguage == "Urdu") NeonGlow else NeonGold
                            )
                        }

                        // Divider
                        Divider(
                            color = NeonGold.copy(alpha = 0.3f),
                            thickness = 1.dp,
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Example numbers with annotations
                        // These are hardcoded examples with the format you requested
                        val examples = listOf(
                            "10" to "10 (ten)",
                            "21" to "21 (twenty-one)",
                            "100" to "100 (one hundred)",
                            "1,000" to "1,000 (one thousand)",
                            "1,000,000" to "1,000,000 (one million)"
                        )

                        examples.forEach { (number, translation) ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Number
                                Text(
                                    text = number,
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.Bold
                                    ),
                                    color = if (selectedLanguage == "Urdu") NeonGlow else NeonGold
                                )

                                // Translation
                                Text(
                                    text = translation,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonText,
                                    modifier = Modifier.weight(1f),
                                    textAlign = TextAlign.End
                                )
                            }

                            // Divider between items (except after the last one)
                            if (number != "1,000,000") {
                                Divider(
                                    color = NeonGold.copy(alpha = 0.1f),
                                    thickness = 0.5.dp,
                                    modifier = Modifier.fillMaxWidth()
                                )
                            }
                        }
                    }
                }

                // Note about language support
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                        .shadow(
                            elevation = 4.dp,
                            spotColor = NeonGold.copy(alpha = 0.1f),
                            ambientColor = NeonGold.copy(alpha = 0.05f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.7f)
                    )
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = NeonGold,
                            modifier = Modifier.size(24.dp)
                        )

                        Text(
                            text = "This screen shows how digits are written with their numeric values. Use the main screen for simple translations.",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}
