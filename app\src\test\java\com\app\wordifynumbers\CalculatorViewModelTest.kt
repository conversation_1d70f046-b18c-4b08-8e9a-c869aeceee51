package com.app.wordifynumbers

import android.app.Application
import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*
import kotlin.math.PI

@RunWith(AndroidJUnit4::class)
@OptIn(ExperimentalCoroutinesApi::class)
class CalculatorViewModelTest {
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private lateinit var viewModel: CalculatorViewModel
    private lateinit var appContext: Application

    @Before
    fun setup() {
        appContext = ApplicationProvider.getApplicationContext()
        viewModel = CalculatorViewModel(appContext)
    }

    @Test
    fun `test basic arithmetic operations`() = runTest {
        // Addition
        viewModel.onInput("2+2")
        viewModel.onEquals()
        assertEquals("4.00", viewModel.state.value.result)

        // Subtraction
        viewModel.onInput("5-3")
        viewModel.onEquals()
        assertEquals("2.00", viewModel.state.value.result)

        // Multiplication
        viewModel.onInput("4*3")
        viewModel.onEquals()
        assertEquals("12.00", viewModel.state.value.result)

        // Division
        viewModel.onInput("10/2")
        viewModel.onEquals()
        assertEquals("5.00", viewModel.state.value.result)
    }

    @Test
    fun `test expression validation`() = runTest {
        // Invalid expression - missing closing parenthesis
        viewModel.onInput("(2+3")
        viewModel.onEquals()
        assertTrue(viewModel.state.value.isError)

        // Invalid expression - double operators
        viewModel.onInput("2++3")
        viewModel.onEquals()
        assertTrue(viewModel.state.value.isError)

        // Invalid expression - ends with operator
        viewModel.onInput("2+")
        viewModel.onEquals()
        assertTrue(viewModel.state.value.isError)

        // Valid complex expression
        viewModel.onInput("(2+3)*4")
        viewModel.onEquals()
        assertEquals("20.00", viewModel.state.value.result)
    }

    @Test
    fun `test error handling`() = runTest {
        // Division by zero
        viewModel.onInput("1/0")
        viewModel.onEquals()
        assertTrue(viewModel.state.value.isError)

        // Invalid number format
        viewModel.onInput("2.5.6")
        viewModel.onEquals()
        assertTrue(viewModel.state.value.isError)

        // Nested parentheses
        viewModel.onInput("((2+3)*4)")
        viewModel.onEquals()
        assertEquals("20.00", viewModel.state.value.result)
    }
}
