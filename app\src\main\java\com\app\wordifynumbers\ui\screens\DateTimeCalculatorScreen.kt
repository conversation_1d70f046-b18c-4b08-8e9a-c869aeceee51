package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.activity.compose.BackHandler
import com.app.wordifynumbers.ui.navigation.MultiModalBackHandler
import com.app.wordifynumbers.ui.navigation.NavigationUtils
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.*
import com.app.wordifynumbers.util.FeedbackUtil
import java.time.LocalDate
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.util.Locale

/**
 * A modern, international-standard Date and Time Calculator screen
 * Supports date difference calculation, date addition/subtraction,
 * time difference calculation, and time addition/subtraction
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateTimeCalculatorScreen(modifier: Modifier = Modifier) {
    // ViewModel for business logic
    val viewModel: DateTimeCalculatorViewModel = viewModel()

    // Collect state from ViewModel
    val calculatorMode by viewModel.calculatorMode.collectAsState()
    val dateMode by viewModel.dateMode.collectAsState()
    val timeMode by viewModel.timeMode.collectAsState()
    val date1 by viewModel.date1.collectAsState()
    val date2 by viewModel.date2.collectAsState()
    val time1 by viewModel.time1.collectAsState()
    val time2 by viewModel.time2.collectAsState()
    val daysToAdd by viewModel.daysToAdd.collectAsState()
    val hoursToAdd by viewModel.hoursToAdd.collectAsState()
    val minutesToAdd by viewModel.minutesToAdd.collectAsState()
    val secondsToAdd by viewModel.secondsToAdd.collectAsState()
    val dateResult by viewModel.dateResult.collectAsState()
    val timeResult by viewModel.timeResult.collectAsState()
    val error by viewModel.error.collectAsState()
    val availableLocales by viewModel.availableLocales.collectAsState()
    val selectedLocale by viewModel.selectedLocale.collectAsState()

    // Local state
    var showLocaleDialog by remember { mutableStateOf(false) }
    var showInfoDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val scrollState = rememberScrollState()

    // Accent color based on calculator mode
    val accentColor = if (calculatorMode == CalculatorMode.DATE) NeonGlow else NeonPurple

    // Handle back button press using standardized multi-modal handler
    MultiModalBackHandler(
        modalStates = NavigationUtils.createModalStates(
            showInfoDialog to { showInfoDialog = false },
            showLocaleDialog to { showLocaleDialog = false }
        )
    )

    // Info Dialog
    if (showInfoDialog) {
        AlertDialog(
            onDismissRequest = { showInfoDialog = false },
            title = {
                Text(
                    text = if (calculatorMode == CalculatorMode.DATE) "Date Calculator Help" else "Time Calculator Help",
                    style = MaterialTheme.typography.titleLarge,
                    color = accentColor
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        text = if (calculatorMode == CalculatorMode.DATE)
                            "This calculator helps you work with dates in two modes:"
                        else
                            "This calculator helps you work with times in two modes:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = if (calculatorMode == CalculatorMode.DATE)
                            "1. Date Difference: Calculate the time between two dates\n" +
                            "2. Add/Subtract Days: Add or subtract days from a date"
                        else
                            "1. Time Difference: Calculate the time between two times\n" +
                            "2. Add/Subtract Time: Add or subtract hours, minutes, and seconds from a time",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "You can also change the locale to see dates and times formatted according to different regional standards.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showInfoDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = accentColor
                    )
                ) {
                    Text("Close")
                }
            },
            containerColor = NeonCard,
            titleContentColor = accentColor,
            textContentColor = NeonText
        )
    }

    // Locale Selection Dialog
    if (showLocaleDialog) {
        AlertDialog(
            onDismissRequest = { showLocaleDialog = false },
            title = {
                Text(
                    text = "Select Locale",
                    style = MaterialTheme.typography.titleLarge,
                    color = accentColor
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    availableLocales.forEach { locale ->
                        val isSelected = locale == selectedLocale
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .clickable {
                                    viewModel.setSelectedLocale(locale)
                                    showLocaleDialog = false
                                    FeedbackUtil.buttonPress(context)
                                },
                            color = if (isSelected) accentColor.copy(alpha = 0.2f) else Color.Transparent
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = viewModel.getLocaleDisplayName(locale),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (isSelected) accentColor else NeonText
                                )

                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = accentColor
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showLocaleDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = accentColor
                    )
                ) {
                    Text("Cancel")
                }
            },
            containerColor = NeonCard,
            titleContentColor = accentColor,
            textContentColor = NeonText
        )
    }

    // Main Screen Content
    StandardCalculatorLayout(
        title = calculatorMode.displayName,
        icon = if (calculatorMode == CalculatorMode.DATE) Icons.Default.CalendarMonth else Icons.Default.Timer,
        accentColor = accentColor,
        showInfoButton = true,
        onInfoClick = {
            showInfoDialog = true
            FeedbackUtil.buttonPress(context)
        },

        // Input Section
        inputSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Mode Selection Card with improved styling
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = accentColor.copy(alpha = 0.2f),
                            ambientColor = accentColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(1.dp, SolidColor(accentColor.copy(alpha = 0.3f)))
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // Header
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Settings,
                                contentDescription = null,
                                tint = accentColor
                            )

                            Text(
                                text = "Calculator Mode",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = accentColor
                            )
                        }

                        Divider(color = accentColor.copy(alpha = 0.2f))

                        // Mode Tabs with improved styling
                        TabRow(
                            selectedTabIndex = if (calculatorMode == CalculatorMode.DATE) 0 else 1,
                            containerColor = NeonCard.copy(alpha = 0.7f),
                            contentColor = accentColor,
                            indicator = { tabPositions ->
                                TabRowDefaults.Indicator(
                                    modifier = Modifier.tabIndicatorOffset(
                                        tabPositions[if (calculatorMode == CalculatorMode.DATE) 0 else 1]
                                    ),
                                    color = accentColor,
                                    height = 3.dp
                                )
                            },
                            divider = { Divider(color = accentColor.copy(alpha = 0.1f)) }
                        ) {
                            Tab(
                                selected = calculatorMode == CalculatorMode.DATE,
                                onClick = {
                                    viewModel.setCalculatorMode(CalculatorMode.DATE)
                                    FeedbackUtil.buttonPress(context)
                                },
                                text = {
                                    Text(
                                        text = "Date",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            fontWeight = if (calculatorMode == CalculatorMode.DATE)
                                                FontWeight.Bold else FontWeight.Normal
                                        )
                                    )
                                },
                                icon = {
                                    Icon(
                                        imageVector = Icons.Default.CalendarMonth,
                                        contentDescription = null,
                                        modifier = Modifier.size(24.dp)
                                    )
                                },
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                            Tab(
                                selected = calculatorMode == CalculatorMode.TIME,
                                onClick = {
                                    viewModel.setCalculatorMode(CalculatorMode.TIME)
                                    FeedbackUtil.buttonPress(context)
                                },
                                text = {
                                    Text(
                                        text = "Time",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            fontWeight = if (calculatorMode == CalculatorMode.TIME)
                                                FontWeight.Bold else FontWeight.Normal
                                        )
                                    )
                                },
                                icon = {
                                    Icon(
                                        imageVector = Icons.Default.Timer,
                                        contentDescription = null,
                                        modifier = Modifier.size(24.dp)
                                    )
                                },
                                modifier = Modifier.padding(vertical = 8.dp)
                            )
                        }

                        // Sub-mode Selection with improved styling
                        Text(
                            text = "Calculation Type",
                            style = MaterialTheme.typography.labelLarge,
                            color = accentColor
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            if (calculatorMode == CalculatorMode.DATE) {
                                DateMode.values().forEach { mode ->
                                    Button(
                                        onClick = {
                                            viewModel.setDateMode(mode)
                                            FeedbackUtil.buttonPress(context)
                                        },
                                        modifier = Modifier.weight(1f),
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = if (dateMode == mode)
                                                accentColor else accentColor.copy(alpha = 0.2f),
                                            contentColor = if (dateMode == mode)
                                                Color.Black else accentColor
                                        ),
                                        shape = RoundedCornerShape(8.dp),
                                        border = BorderStroke(
                                            width = 1.dp,
                                            brush = SolidColor(if (dateMode == mode)
                                                accentColor else accentColor.copy(alpha = 0.3f))
                                        )
                                    ) {
                                        Text(
                                            text = mode.displayName,
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                fontWeight = if (dateMode == mode)
                                                    FontWeight.Bold else FontWeight.Normal
                                            )
                                        )
                                    }
                                }
                            } else {
                                TimeMode.values().forEach { mode ->
                                    Button(
                                        onClick = {
                                            viewModel.setTimeMode(mode)
                                            FeedbackUtil.buttonPress(context)
                                        },
                                        modifier = Modifier.weight(1f),
                                        colors = ButtonDefaults.buttonColors(
                                            containerColor = if (timeMode == mode)
                                                accentColor else accentColor.copy(alpha = 0.2f),
                                            contentColor = if (timeMode == mode)
                                                Color.Black else accentColor
                                        ),
                                        shape = RoundedCornerShape(8.dp),
                                        border = BorderStroke(
                                            width = 1.dp,
                                            brush = SolidColor(if (timeMode == mode)
                                                accentColor else accentColor.copy(alpha = 0.3f))
                                        )
                                    ) {
                                        Text(
                                            text = mode.displayName,
                                            style = MaterialTheme.typography.bodyMedium.copy(
                                                fontWeight = if (timeMode == mode)
                                                    FontWeight.Bold else FontWeight.Normal
                                            )
                                        )
                                    }
                                }
                            }
                        }

                        // Locale Selection with improved styling
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .background(accentColor.copy(alpha = 0.1f))
                                .padding(12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Language,
                                    contentDescription = null,
                                    tint = accentColor,
                                    modifier = Modifier.size(20.dp)
                                )

                                Text(
                                    text = "Locale: ${viewModel.getLocaleDisplayName(selectedLocale)}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonText
                                )
                            }

                            Button(
                                onClick = {
                                    showLocaleDialog = true
                                    FeedbackUtil.buttonPress(context)
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = accentColor.copy(alpha = 0.2f),
                                    contentColor = accentColor
                                ),
                                border = BorderStroke(1.dp, SolidColor(accentColor.copy(alpha = 0.3f))),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                Text(
                                    text = "Change",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                            }
                        }
                    }
                }

                // Input Fields with improved styling
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = accentColor.copy(alpha = 0.2f),
                            ambientColor = accentColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(1.dp, SolidColor(accentColor.copy(alpha = 0.3f)))
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // Header
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = if (calculatorMode == CalculatorMode.DATE)
                                    Icons.Default.DateRange else Icons.Default.AccessTime,
                                contentDescription = null,
                                tint = accentColor
                            )

                            Text(
                                text = if (calculatorMode == CalculatorMode.DATE)
                                    "Date Input" else "Time Input",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = accentColor
                            )
                        }

                        Divider(color = accentColor.copy(alpha = 0.2f))

                        // Input fields based on mode
                        when (calculatorMode) {
                            CalculatorMode.DATE -> {
                                when (dateMode) {
                                    DateMode.DIFFERENCE -> {
                                        // First date with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "First Date",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            DatePickerField(
                                                label = "Select First Date",
                                                date = date1,
                                                onDateChange = { viewModel.setDate1(it) },
                                                accentColor = accentColor,
                                                modifier = Modifier.fillMaxWidth()
                                            )
                                        }

                                        // Second date with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "Second Date",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            DatePickerField(
                                                label = "Select Second Date",
                                                date = date2,
                                                onDateChange = { viewModel.setDate2(it) },
                                                accentColor = accentColor,
                                                modifier = Modifier.fillMaxWidth()
                                            )
                                        }
                                    }
                                    DateMode.ADD_SUBTRACT -> {
                                        // Start date with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "Start Date",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            DatePickerField(
                                                label = "Select Start Date",
                                                date = date1,
                                                onDateChange = { viewModel.setDate1(it) },
                                                accentColor = accentColor,
                                                modifier = Modifier.fillMaxWidth()
                                            )
                                        }

                                        // Days to add/subtract with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "Days to Add/Subtract",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            OutlinedTextField(
                                                label = { Text("Enter Number of Days") },
                                                value = daysToAdd,
                                                onValueChange = {
                                                    if (it.isEmpty() || it.matches(Regex("^-?\\d*$"))) {
                                                        viewModel.setDaysToAdd(it)
                                                    }
                                                },
                                                modifier = Modifier.fillMaxWidth(),
                                                singleLine = true,
                                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                colors = OutlinedTextFieldDefaults.colors(
                                                    focusedBorderColor = accentColor,
                                                    unfocusedBorderColor = accentColor.copy(alpha = 0.5f),
                                                    focusedLabelColor = accentColor,
                                                    unfocusedLabelColor = accentColor.copy(alpha = 0.7f),
                                                    focusedTextColor = NeonText,
                                                    unfocusedTextColor = NeonText,
                                                    cursorColor = accentColor,
                                                    focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                                    unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                                ),
                                                leadingIcon = {
                                                    Icon(
                                                        imageVector = Icons.Default.Edit,
                                                        contentDescription = null,
                                                        tint = accentColor
                                                    )
                                                },
                                                supportingText = {
                                                    Text(
                                                        text = "Use negative values to subtract days",
                                                        color = NeonText.copy(alpha = 0.7f)
                                                    )
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                            CalculatorMode.TIME -> {
                                when (timeMode) {
                                    TimeMode.DIFFERENCE -> {
                                        // First time with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "First Time",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            TimePickerField(
                                                label = "Select First Time",
                                                time = time1,
                                                onTimeChange = { viewModel.setTime1(it) },
                                                accentColor = accentColor,
                                                modifier = Modifier.fillMaxWidth()
                                            )
                                        }

                                        // Second time with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "Second Time",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            TimePickerField(
                                                label = "Select Second Time",
                                                time = time2,
                                                onTimeChange = { viewModel.setTime2(it) },
                                                accentColor = accentColor,
                                                modifier = Modifier.fillMaxWidth()
                                            )
                                        }
                                    }
                                    TimeMode.ADD_SUBTRACT -> {
                                        // Start time with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "Start Time",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            TimePickerField(
                                                label = "Select Start Time",
                                                time = time1,
                                                onTimeChange = { viewModel.setTime1(it) },
                                                accentColor = accentColor,
                                                modifier = Modifier.fillMaxWidth()
                                            )
                                        }

                                        // Time to add/subtract with improved styling
                                        Column(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalArrangement = Arrangement.spacedBy(8.dp)
                                        ) {
                                            Text(
                                                text = "Time to Add/Subtract",
                                                style = MaterialTheme.typography.labelLarge,
                                                color = accentColor
                                            )

                                            Card(
                                                modifier = Modifier.fillMaxWidth(),
                                                colors = CardDefaults.cardColors(
                                                    containerColor = accentColor.copy(alpha = 0.1f)
                                                ),
                                                shape = RoundedCornerShape(8.dp),
                                                border = BorderStroke(1.dp, SolidColor(accentColor.copy(alpha = 0.2f)))
                                            ) {
                                                Column(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        .padding(12.dp),
                                                    verticalArrangement = Arrangement.spacedBy(12.dp)
                                                ) {
                                                    Row(
                                                        modifier = Modifier.fillMaxWidth(),
                                                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                                                    ) {
                                                        // Hours input
                                                        OutlinedTextField(
                                                            label = { Text("Hours") },
                                                            value = hoursToAdd,
                                                            onValueChange = {
                                                                if (it.isEmpty() || it.matches(Regex("^-?\\d*$"))) {
                                                                    viewModel.setHoursToAdd(it)
                                                                }
                                                            },
                                                            modifier = Modifier.weight(1f),
                                                            singleLine = true,
                                                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                            colors = OutlinedTextFieldDefaults.colors(
                                                                focusedBorderColor = accentColor,
                                                                unfocusedBorderColor = accentColor.copy(alpha = 0.5f),
                                                                focusedLabelColor = accentColor,
                                                                unfocusedLabelColor = accentColor.copy(alpha = 0.7f),
                                                                focusedTextColor = NeonText,
                                                                unfocusedTextColor = NeonText,
                                                                cursorColor = accentColor,
                                                                focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                                                unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                                            ),
                                                            leadingIcon = {
                                                                Icon(
                                                                    imageVector = Icons.Default.HourglassTop,
                                                                    contentDescription = null,
                                                                    tint = accentColor,
                                                                    modifier = Modifier.size(20.dp)
                                                                )
                                                            }
                                                        )

                                                        // Minutes input
                                                        OutlinedTextField(
                                                            label = { Text("Minutes") },
                                                            value = minutesToAdd,
                                                            onValueChange = {
                                                                if (it.isEmpty() || it.matches(Regex("^-?\\d*$"))) {
                                                                    viewModel.setMinutesToAdd(it)
                                                                }
                                                            },
                                                            modifier = Modifier.weight(1f),
                                                            singleLine = true,
                                                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                            colors = OutlinedTextFieldDefaults.colors(
                                                                focusedBorderColor = accentColor,
                                                                unfocusedBorderColor = accentColor.copy(alpha = 0.5f),
                                                                focusedLabelColor = accentColor,
                                                                unfocusedLabelColor = accentColor.copy(alpha = 0.7f),
                                                                focusedTextColor = NeonText,
                                                                unfocusedTextColor = NeonText,
                                                                cursorColor = accentColor,
                                                                focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                                                unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                                            ),
                                                            leadingIcon = {
                                                                Icon(
                                                                    imageVector = Icons.Default.Schedule,
                                                                    contentDescription = null,
                                                                    tint = accentColor,
                                                                    modifier = Modifier.size(20.dp)
                                                                )
                                                            }
                                                        )
                                                    }

                                                    // Seconds input
                                                    OutlinedTextField(
                                                        label = { Text("Seconds") },
                                                        value = secondsToAdd,
                                                        onValueChange = {
                                                            if (it.isEmpty() || it.matches(Regex("^-?\\d*$"))) {
                                                                viewModel.setSecondsToAdd(it)
                                                            }
                                                        },
                                                        modifier = Modifier.fillMaxWidth(),
                                                        singleLine = true,
                                                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                                        colors = OutlinedTextFieldDefaults.colors(
                                                            focusedBorderColor = accentColor,
                                                            unfocusedBorderColor = accentColor.copy(alpha = 0.5f),
                                                            focusedLabelColor = accentColor,
                                                            unfocusedLabelColor = accentColor.copy(alpha = 0.7f),
                                                            focusedTextColor = NeonText,
                                                            unfocusedTextColor = NeonText,
                                                            cursorColor = accentColor,
                                                            focusedContainerColor = NeonCard.copy(alpha = 0.7f),
                                                            unfocusedContainerColor = NeonCard.copy(alpha = 0.5f)
                                                        ),
                                                        leadingIcon = {
                                                            Icon(
                                                                imageVector = Icons.Default.Timer,
                                                                contentDescription = null,
                                                                tint = accentColor,
                                                                modifier = Modifier.size(20.dp)
                                                            )
                                                        },
                                                        supportingText = {
                                                            Text(
                                                                text = "Use negative values to subtract time",
                                                                color = NeonText.copy(alpha = 0.7f)
                                                            )
                                                        }
                                                    )
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // Error Message with improved styling
                AnimatedVisibility(
                    visible = error != null,
                    enter = fadeIn(),
                    exit = fadeOut()
                ) {
                    error?.let {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .shadow(
                                    elevation = 4.dp,
                                    spotColor = NeonRed.copy(alpha = 0.2f),
                                    ambientColor = NeonRed.copy(alpha = 0.1f),
                                    shape = RoundedCornerShape(12.dp)
                                ),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = NeonRed.copy(alpha = 0.1f)
                            ),
                            border = BorderStroke(1.dp, SolidColor(NeonRed.copy(alpha = 0.3f)))
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.spacedBy(8.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = null,
                                    tint = NeonRed,
                                    modifier = Modifier.size(24.dp)
                                )

                                Text(
                                    text = it,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonRed,
                                    modifier = Modifier.weight(1f)
                                )
                            }
                        }
                    }
                }
            }
        },

        // Action Buttons with improved styling
        actionButtons = {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = accentColor.copy(alpha = 0.2f),
                        ambientColor = accentColor.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                ),
                border = BorderStroke(1.dp, SolidColor(accentColor.copy(alpha = 0.3f)))
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Header
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.TouchApp,
                            contentDescription = null,
                            tint = accentColor
                        )

                        Text(
                            text = "Actions",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = accentColor
                        )
                    }

                    Divider(color = accentColor.copy(alpha = 0.2f))

                    // Calculate button with improved styling
                    Button(
                        onClick = {
                            if (calculatorMode == CalculatorMode.DATE) {
                                viewModel.calculateDateResult()
                            } else {
                                viewModel.calculateTimeResult()
                            }
                            FeedbackUtil.buttonPress(context)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp)
                            .shadow(
                                elevation = 4.dp,
                                spotColor = accentColor.copy(alpha = 0.3f),
                                ambientColor = accentColor.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(12.dp)
                            ),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = accentColor,
                            contentColor = Color.Black,
                            disabledContainerColor = NeonCard.copy(alpha = 0.5f),
                            disabledContentColor = NeonText.copy(alpha = 0.5f)
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = if (calculatorMode == CalculatorMode.DATE)
                                    Icons.Default.CalendarMonth
                                else
                                    Icons.Default.Timer,
                                contentDescription = null,
                                modifier = Modifier.size(24.dp)
                            )
                            Text(
                                text = if (calculatorMode == CalculatorMode.DATE)
                                    "Calculate Date" else "Calculate Time",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                )
                            )
                        }
                    }
                }
            }
        },

        // Result Section
        resultSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                when (calculatorMode) {
                    CalculatorMode.DATE -> {
                        dateResult?.let { result ->
                            CalculatorResultDisplay(
                                title = if (dateMode == DateMode.DIFFERENCE) "Date Difference" else "Result Date",
                                result = result.mainResult,
                                accentColor = accentColor,
                                details = result.details.map { it to it }
                            )
                        } ?: run {
                            if (error == null) {
                                Text(
                                    text = if (dateMode == DateMode.DIFFERENCE)
                                        "Select two dates to calculate the difference"
                                    else
                                        "Enter days to add or subtract",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = NeonText.copy(alpha = 0.7f)
                                )
                            }
                        }
                    }
                    CalculatorMode.TIME -> {
                        timeResult?.let { result ->
                            CalculatorResultDisplay(
                                title = if (timeMode == TimeMode.DIFFERENCE) "Time Difference" else "Result Time",
                                result = result.mainResult,
                                accentColor = accentColor,
                                details = result.details.map { it to it }
                            )
                        } ?: run {
                            if (error == null) {
                                Text(
                                    text = if (timeMode == TimeMode.DIFFERENCE)
                                        "Select two times to calculate the difference"
                                    else
                                        "Enter hours, minutes, or seconds to add or subtract",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = NeonText.copy(alpha = 0.7f)
                                )
                            }
                        }
                    }
                }
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DatePickerField(
    label: String,
    date: LocalDate,
    onDateChange: (LocalDate) -> Unit,
    accentColor: Color = NeonGlow,
    modifier: Modifier = Modifier
) {
    var showDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current

    NeonTextField(
        value = date.format(DateTimeFormatter.ISO_LOCAL_DATE),
        onValueChange = { },
        label = { Text(label) },
        readOnly = true,
        modifier = modifier.fillMaxWidth(),
        accentColor = accentColor,
        trailingIcon = {
            IconButton(onClick = {
                showDialog = true
                FeedbackUtil.buttonPress(context)
            }) {
                Icon(
                    Icons.Default.CalendarMonth,
                    contentDescription = "Select date",
                    tint = accentColor
                )
            }
        }
    )

    if (showDialog) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = date.toEpochDay() * 86400000
        )

        DatePickerDialog(
            onDismissRequest = { showDialog = false },
            confirmButton = {
                TextButton(onClick = {
                    showDialog = false
                    datePickerState.selectedDateMillis?.let { millis ->
                        val selectedDate = LocalDate.ofEpochDay(millis / 86400000)
                        onDateChange(selectedDate)
                    }
                    FeedbackUtil.buttonPress(context)
                }) {
                    Text("OK", color = accentColor)
                }
            },
            dismissButton = {
                TextButton(onClick = {
                    showDialog = false
                    FeedbackUtil.buttonPress(context)
                }) {
                    Text("Cancel", color = NeonText)
                }
            },
            colors = DatePickerDefaults.colors(
                containerColor = NeonCard,
                titleContentColor = accentColor,
                headlineContentColor = NeonText,
                weekdayContentColor = NeonText,
                dayContentColor = NeonText,
                selectedDayContainerColor = accentColor,
                todayDateBorderColor = accentColor.copy(alpha = 0.5f)
            )
        ) {
            DatePicker(
                state = datePickerState,
                colors = DatePickerDefaults.colors(
                    containerColor = NeonCard,
                    titleContentColor = accentColor,
                    headlineContentColor = NeonText,
                    weekdayContentColor = NeonText,
                    dayContentColor = NeonText,
                    selectedDayContainerColor = accentColor,
                    todayDateBorderColor = accentColor.copy(alpha = 0.5f)
                ),
                showModeToggle = true
            )
        }
    }
}

@Composable
private fun TimePickerField(
    label: String,
    time: LocalTime,
    onTimeChange: (LocalTime) -> Unit,
    accentColor: Color = NeonGlow,
    modifier: Modifier = Modifier
) {
    var showDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current

    // Format time as HH:MM
    val timeString = time.format(DateTimeFormatter.ofPattern("HH:mm"))

    NeonTextField(
        value = timeString,
        onValueChange = { },
        label = { Text(label) },
        readOnly = true,
        modifier = modifier.fillMaxWidth(),
        accentColor = accentColor,
        trailingIcon = {
            IconButton(onClick = {
                showDialog = true
                FeedbackUtil.buttonPress(context)
            }) {
                Icon(
                    Icons.Default.Timer,
                    contentDescription = "Select time",
                    tint = accentColor
                )
            }
        }
    )

    if (showDialog) {
        // Custom time picker dialog
        var hour by remember { mutableStateOf(time.hour) }
        var minute by remember { mutableStateOf(time.minute) }

        AlertDialog(
            onDismissRequest = { showDialog = false },
            title = {
                Text(
                    text = "Select Time",
                    style = MaterialTheme.typography.titleLarge,
                    color = accentColor
                )
            },
            text = {
                Column(
                    modifier = Modifier.padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Time display
                    Text(
                        text = String.format("%02d:%02d", hour, minute),
                        style = MaterialTheme.typography.displayMedium,
                        color = accentColor,
                        fontWeight = FontWeight.Bold
                    )

                    // Hour selector
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Hour:",
                            style = MaterialTheme.typography.bodyLarge,
                            color = NeonText
                        )

                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            IconButton(
                                onClick = {
                                    hour = (hour - 1).mod(24)
                                    FeedbackUtil.buttonPress(context)
                                }
                            ) {
                                Icon(
                                    Icons.Default.Remove,
                                    contentDescription = "Decrease hour",
                                    tint = accentColor
                                )
                            }

                            Text(
                                text = String.format("%02d", hour),
                                style = MaterialTheme.typography.titleLarge,
                                color = NeonText
                            )

                            IconButton(
                                onClick = {
                                    hour = (hour + 1).mod(24)
                                    FeedbackUtil.buttonPress(context)
                                }
                            ) {
                                Icon(
                                    Icons.Default.Add,
                                    contentDescription = "Increase hour",
                                    tint = accentColor
                                )
                            }
                        }
                    }

                    // Minute selector
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Minute:",
                            style = MaterialTheme.typography.bodyLarge,
                            color = NeonText
                        )

                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            IconButton(
                                onClick = {
                                    minute = (minute - 1).mod(60)
                                    FeedbackUtil.buttonPress(context)
                                }
                            ) {
                                Icon(
                                    Icons.Default.Remove,
                                    contentDescription = "Decrease minute",
                                    tint = accentColor
                                )
                            }

                            Text(
                                text = String.format("%02d", minute),
                                style = MaterialTheme.typography.titleLarge,
                                color = NeonText
                            )

                            IconButton(
                                onClick = {
                                    minute = (minute + 1).mod(60)
                                    FeedbackUtil.buttonPress(context)
                                }
                            ) {
                                Icon(
                                    Icons.Default.Add,
                                    contentDescription = "Increase minute",
                                    tint = accentColor
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDialog = false
                        onTimeChange(LocalTime.of(hour, minute))
                        FeedbackUtil.buttonPress(context)
                    }
                ) {
                    Text("OK", color = accentColor)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDialog = false
                        FeedbackUtil.buttonPress(context)
                    }
                ) {
                    Text("Cancel", color = NeonText)
                }
            },
            containerColor = NeonCard,
            titleContentColor = accentColor,
            textContentColor = NeonText
        )
    }
}
