package com.app.wordifynumbers.ui.theme

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

// Primary Colors
val NeonDeepPink = Color(0xFFE75480)
val NeonDeepBlue = Color(0xFF0A0F2C)
val NeonGlow = Color(0xFF7DF9FF)
val NeonText = Color(0xFFFFFFFF) // Pure white for maximum visibility
val NeonHint = Color(0xFF9CA3AF) // Hint text color
val NeonSurface = Color(0xFF181B2C)

// Background Colors
val NeonCard = Color(0xFF23263A)
val NeonBackground = Color(0xFF10101A)
val NeonNav = Color(0xFF181B2C)

// Accent Colors
val NeonRed = Color(0xFFFF5370)
val NeonGreen = Color(0xFF21F6B6)
val NeonBlue = Color(0xFF00BFFF)
val NeonPurple = Color(0xFF9D7DFF)
val NeonCyan = Color(0xFF7DF9FF)
val NeonOrange = Color(0xFFFFB86C)
val NeonYellow = Color(0xFFFFF685)
val NeonGold = Color(0xFFFFD700)
val NeonGray = Color(0xFF9E9E9E)
val NeonPink = Color(0xFFFF69B4)

// Border Definitions
object NeonBorderConstants {
    val Thin = 1.dp
    val Medium = 2.dp
    val Thick = 3.dp
}