package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.ui.viewmodel.VisualizationType
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.*
import com.app.wordifynumbers.ui.navigation.MultiModalBackHandler
import com.app.wordifynumbers.ui.navigation.NavigationUtils
import com.app.wordifynumbers.util.FeedbackUtil
import kotlin.math.*

/**
 * A modern, international-standard Statistics Calculator screen
 * Supports data analysis with various statistical measures and visualizations
 */
@Composable
fun StatisticsCalculatorScreen(modifier: Modifier = Modifier) {
    // ViewModel for business logic
    val viewModel: StatisticsCalculatorViewModel = viewModel()

    // Collect state from ViewModel
    val dataInput by viewModel.dataInput.collectAsState()
    val parsedData by viewModel.parsedData.collectAsState()
    val visualizationType by viewModel.visualizationType.collectAsState()
    val statisticsResult by viewModel.statisticsResult.collectAsState()
    val error by viewModel.error.collectAsState()
    val availableLocales by viewModel.availableLocales.collectAsState()
    val selectedLocale by viewModel.selectedLocale.collectAsState()
    val sampleDataSets by viewModel.sampleDataSets.collectAsState()

    // Local state
    var showLocaleDialog by remember { mutableStateOf(false) }
    var showInfoDialog by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val scrollState = rememberScrollState()

    // Handle back button press using standardized multi-modal handler
    MultiModalBackHandler(
        modalStates = NavigationUtils.createModalStates(
            showInfoDialog to { showInfoDialog = false },
            showLocaleDialog to { showLocaleDialog = false }
        )
    )

    // Info Dialog
    if (showInfoDialog) {
        AlertDialog(
            onDismissRequest = { showInfoDialog = false },
            title = {
                Text(
                    text = "Statistics Calculator Help",
                    style = MaterialTheme.typography.titleLarge,
                    color = NeonGlow
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        text = "This calculator helps you analyze numerical data and calculate various statistical measures.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "How to use:",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonGlow
                    )

                    Text(
                        text = "1. Enter your data as numbers separated by commas\n" +
                              "2. View the calculated statistics\n" +
                              "3. Choose different visualization types\n" +
                              "4. Change the locale to see numbers formatted according to different regional standards",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "You can also use the sample data sets to explore different types of distributions.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "Statistical Measures:",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonGlow
                    )

                    Text(
                        text = "• Central Tendency: Mean, Median, Mode\n" +
                              "• Dispersion: Variance, Standard Deviation, Range\n" +
                              "• Shape: Skewness, Kurtosis\n" +
                              "• Distribution: Percentiles, Quartiles\n" +
                              "• Advanced: Geometric Mean, Harmonic Mean, Coefficient of Variation",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showInfoDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = NeonGlow
                    )
                ) {
                    Text("Close")
                }
            },
            containerColor = NeonCard,
            titleContentColor = NeonGlow,
            textContentColor = NeonText
        )
    }

    // Locale Selection Dialog
    if (showLocaleDialog) {
        AlertDialog(
            onDismissRequest = { showLocaleDialog = false },
            title = {
                Text(
                    text = "Select Locale",
                    style = MaterialTheme.typography.titleLarge,
                    color = NeonGlow
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    availableLocales.forEach { locale ->
                        val isSelected = locale == selectedLocale
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .clickable {
                                    viewModel.setSelectedLocale(locale)
                                    showLocaleDialog = false
                                    FeedbackUtil.buttonPress(context)
                                },
                            color = if (isSelected) NeonGlow.copy(alpha = 0.2f) else Color.Transparent
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = viewModel.getLocaleDisplayName(locale),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (isSelected) NeonGlow else NeonText
                                )

                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = NeonGlow
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showLocaleDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = NeonGlow
                    )
                ) {
                    Text("Cancel")
                }
            },
            containerColor = NeonCard,
            titleContentColor = NeonGlow,
            textContentColor = NeonText
        )
    }

    // Main Screen Content
    StandardCalculatorLayout(
        title = "Statistics Calculator",
        icon = Icons.Default.BarChart,
        accentColor = NeonGlow,
        showInfoButton = true,
        onInfoClick = {
            showInfoDialog = true
            FeedbackUtil.buttonPress(context)
        },

        // Input Section
        inputSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Data Input
                NeonTextField(
                    value = dataInput,
                    onValueChange = { viewModel.updateDataInput(it) },
                    label = { Text("Enter numbers separated by commas") },
                    modifier = Modifier.fillMaxWidth(),
                    accentColor = NeonGlow,
                    isError = error != null,
                    supportingText = { Text(error ?: "Example: 10,15,20,25,30") }
                )

                // Locale Selection
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Locale: ${viewModel.getLocaleDisplayName(selectedLocale)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    IconButton(
                        onClick = {
                            showLocaleDialog = true
                            FeedbackUtil.buttonPress(context)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Language,
                            contentDescription = "Change Locale",
                            tint = NeonGlow
                        )
                    }
                }

                // Sample Data Sets
                Text(
                    text = "Sample Data Sets",
                    style = MaterialTheme.typography.titleSmall,
                    color = NeonGlow
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    sampleDataSets.keys.take(3).forEach { key ->
                        NeonButton(
                            onClick = {
                                viewModel.loadSampleData(key)
                                FeedbackUtil.buttonPress(context)
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(key)
                        }
                    }
                }

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    sampleDataSets.keys.drop(3).take(2).forEach { key ->
                        NeonButton(
                            onClick = {
                                viewModel.loadSampleData(key)
                                FeedbackUtil.buttonPress(context)
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(key)
                        }
                    }
                }

                // Visualization Type Selection
                if (statisticsResult != null) {
                    Text(
                        text = "Visualization Type",
                        style = MaterialTheme.typography.titleSmall,
                        color = NeonGlow
                    )

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        VisualizationType.values().take(3).forEach { type ->
                            NeonButton(
                                onClick = {
                                    viewModel.setVisualizationType(type)
                                    FeedbackUtil.buttonPress(context)
                                },
                                modifier = Modifier.weight(1f),
                                accentColor = if (visualizationType == type) NeonGlow else NeonGlow.copy(alpha = 0.5f)
                            ) {
                                Text(type.displayName)
                            }
                        }
                    }

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        VisualizationType.values().drop(3).forEach { type ->
                            NeonButton(
                                onClick = {
                                    viewModel.setVisualizationType(type)
                                    FeedbackUtil.buttonPress(context)
                                },
                                modifier = Modifier.weight(1f),
                                accentColor = if (visualizationType == type) NeonGlow else NeonGlow.copy(alpha = 0.5f)
                            ) {
                                Text(type.displayName)
                            }
                        }
                    }
                }
            }
        },

        // Action Buttons
        actionButtons = {
            CalculatorActionButton(
                text = "Calculate Statistics",
                onClick = {
                    viewModel.calculateStatistics()
                    FeedbackUtil.buttonPress(context)
                },
                accentColor = NeonGlow,
                modifier = Modifier.fillMaxWidth(),
                icon = Icons.Default.Calculate
            )
        },

        // Result Section
        resultSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                statisticsResult?.let { stats ->
                    // Basic Statistics Card
                    NeonCard(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Column(
                            modifier = Modifier.padding(18.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "Basic Statistics",
                                style = MaterialTheme.typography.titleMedium,
                                color = NeonGlow
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            // Basic statistics
                            Text(
                                text = "Basic Measures",
                                style = MaterialTheme.typography.titleSmall,
                                color = NeonGlow.copy(alpha = 0.8f)
                            )
                            StatRow("Sample Size", stats.sampleSize.toString())
                            StatRow("Minimum", viewModel.formatNumber(stats.min))
                            StatRow("Maximum", viewModel.formatNumber(stats.max))
                            StatRow("Range", viewModel.formatNumber(stats.range))

                            Divider(
                                color = NeonGlow.copy(alpha = 0.3f),
                                modifier = Modifier.padding(vertical = 8.dp)
                            )

                            // Central tendency
                            Text(
                                text = "Central Tendency",
                                style = MaterialTheme.typography.titleSmall,
                                color = NeonGlow.copy(alpha = 0.8f)
                            )
                            StatRow("Mean", viewModel.formatNumber(stats.mean))
                            StatRow("Median", viewModel.formatNumber(stats.median))
                            StatRow("Mode", stats.modes.joinToString(", ") { viewModel.formatNumber(it) })
                            StatRow("Geometric Mean", viewModel.formatNumber(stats.geometricMean))
                            StatRow("Harmonic Mean", viewModel.formatNumber(stats.harmonicMean))

                            Divider(
                                color = NeonGlow.copy(alpha = 0.3f),
                                modifier = Modifier.padding(vertical = 8.dp)
                            )

                            // Dispersion
                            Text(
                                text = "Dispersion",
                                style = MaterialTheme.typography.titleSmall,
                                color = NeonGlow.copy(alpha = 0.8f)
                            )
                            StatRow("Variance", viewModel.formatNumber(stats.variance))
                            StatRow("Std Deviation", viewModel.formatNumber(stats.standardDeviation))
                            StatRow("Mean Abs Deviation", viewModel.formatNumber(stats.meanAbsoluteDeviation))
                            StatRow("Coef of Variation", viewModel.formatPercent(stats.coefficientOfVariation))
                            StatRow("IQR", viewModel.formatNumber(stats.iqr))

                            Divider(
                                color = NeonGlow.copy(alpha = 0.3f),
                                modifier = Modifier.padding(vertical = 8.dp)
                            )

                            // Shape
                            Text(
                                text = "Distribution Shape",
                                style = MaterialTheme.typography.titleSmall,
                                color = NeonGlow.copy(alpha = 0.8f)
                            )
                            StatRow("Skewness", viewModel.formatNumber(stats.skewness))
                            StatRow("Kurtosis", viewModel.formatNumber(stats.kurtosis))

                            Divider(
                                color = NeonGlow.copy(alpha = 0.3f),
                                modifier = Modifier.padding(vertical = 8.dp)
                            )

                            // Percentiles
                            Text(
                                text = "Percentiles",
                                style = MaterialTheme.typography.titleSmall,
                                color = NeonGlow.copy(alpha = 0.8f)
                            )
                            StatRow("10th Percentile", viewModel.formatNumber(stats.percentiles[10] ?: 0.0))
                            StatRow("25th (Q1)", viewModel.formatNumber(stats.q1))
                            StatRow("50th (Median)", viewModel.formatNumber(stats.median))
                            StatRow("75th (Q3)", viewModel.formatNumber(stats.q3))
                            StatRow("90th Percentile", viewModel.formatNumber(stats.percentiles[90] ?: 0.0))
                            StatRow("95th Percentile", viewModel.formatNumber(stats.percentiles[95] ?: 0.0))
                            StatRow("99th Percentile", viewModel.formatNumber(stats.percentiles[99] ?: 0.0))
                        }
                    }

                    // Data Visualization
                    NeonCard(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxSize()
                        ) {
                            Text(
                                text = "${visualizationType.displayName} Visualization",
                                style = MaterialTheme.typography.titleMedium,
                                color = NeonGlow
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Box(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .clip(RoundedCornerShape(8.dp))
                                    .background(NeonCard.copy(alpha = 0.5f)),
                                contentAlignment = Alignment.Center
                            ) {
                                // Placeholder for visualization
                                // In a real implementation, this would be replaced with actual visualization components
                                Text(
                                    text = "Visualization coming soon",
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = NeonText
                                )
                            }
                        }
                    }

                    // Summary Card
                    NeonCard {
                        Column(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxWidth()
                        ) {
                            Text(
                                text = "Data Summary",
                                style = MaterialTheme.typography.titleMedium,
                                color = NeonGlow
                            )

                            Spacer(modifier = Modifier.height(12.dp))

                            Text(
                                text = viewModel.generateSummary(stats),
                                color = NeonText
                            )
                        }
                    }
                } ?: run {
                    if (error == null) {
                        Text(
                            text = "Enter data and click 'Calculate Statistics' to see results",
                            style = MaterialTheme.typography.bodyLarge,
                            color = NeonText.copy(alpha = 0.7f)
                        )
                    }
                }
            }
        }
    )
}

@Composable
private fun StatRow(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            color = NeonText.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            color = NeonGlow
        )
    }
}
