package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import kotlin.math.*

@Composable
fun StatisticsVisualizer(
    data: List<Double>,
    visType: VisualizationType,
    modifier: Modifier = Modifier
) {
    when (visType) {
        VisualizationType.HISTOGRAM -> HistogramPlot(data, modifier)
        VisualizationType.BOX_PLOT -> BoxPlot(data, modifier)
        VisualizationType.SCATTER_PLOT -> ScatterPlot(data, modifier)
        VisualizationType.NORMAL_DISTRIBUTION -> NormalDistributionPlot(data, modifier)
    }
}

@Composable
private fun HistogramPlot(
    data: List<Double>,
    modifier: Modifier = Modifier
) {
    if (data.isEmpty()) return
    
    val bins = 10
    val min = data.minOrNull() ?: 0.0
    val max = data.maxOrNull() ?: 0.0
    val range = max - min
    val binWidth = range / bins
    
    val histogram = List(bins) { bin ->
        val start = min + bin * binWidth
        val end = start + binWidth
        data.count { it in start..end }.toDouble()
    }
    
    val maxCount = histogram.maxOrNull() ?: 0.0

    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
    ) {
        val width = size.width
        val height = size.height
        val barWidth = width / bins
        
        // Draw axes
        drawLine(
            color = NeonText,
            start = Offset(0f, height),
            end = Offset(width, height),
            strokeWidth = 2.dp.toPx()
        )
        drawLine(
            color = NeonText,
            start = Offset(0f, 0f),
            end = Offset(0f, height),
            strokeWidth = 2.dp.toPx()
        )
        
        // Draw bars
        histogram.forEachIndexed { index, count ->
            val barHeight = (count / maxCount * height).toFloat()
            drawRect(
                color = NeonGlow.copy(alpha = 0.5f),
                topLeft = Offset(index * barWidth, height - barHeight),
                size = Size(barWidth * 0.9f, barHeight),
            )
        }
    }
}

@Composable
private fun BoxPlot(
    data: List<Double>,
    modifier: Modifier = Modifier
) {
    if (data.isEmpty()) return
    
    val sortedData = data.sorted()
    val q1 = sortedData[sortedData.size / 4]
    val median = sortedData[sortedData.size / 2]
    val q3 = sortedData[3 * sortedData.size / 4]
    val iqr = q3 - q1
    val min = sortedData.first()
    val max = sortedData.last()
    
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
    ) {
        val width = size.width
        val height = size.height
        val boxHeight = height * 0.4f
        val centerY = height / 2
        
        // Draw whiskers
        drawLine(
            color = NeonText,
            start = Offset(scaleX(min, min, max, width), centerY),
            end = Offset(scaleX(q1, min, max, width), centerY),
            strokeWidth = 2.dp.toPx()
        )
        drawLine(
            color = NeonText,
            start = Offset(scaleX(q3, min, max, width), centerY),
            end = Offset(scaleX(max, min, max, width), centerY),
            strokeWidth = 2.dp.toPx()
        )
        
        // Draw box
        drawRect(
            color = NeonGlow.copy(alpha = 0.3f),
            topLeft = Offset(
                scaleX(q1, min, max, width),
                centerY - boxHeight/2
            ),
            size = Size(
                scaleX(q3, min, max, width) - scaleX(q1, min, max, width),
                boxHeight
            )
        )
        
        // Draw median line
        drawLine(
            color = NeonGlow,
            start = Offset(
                scaleX(median, min, max, width),
                centerY - boxHeight/2
            ),
            end = Offset(
                scaleX(median, min, max, width),
                centerY + boxHeight/2
            ),
            strokeWidth = 3.dp.toPx()
        )
    }
}

@Composable
private fun ScatterPlot(
    data: List<Double>,
    modifier: Modifier = Modifier
) {
    if (data.size < 2) return
    
    val pairedData = data.windowed(2, 2)
    val xMin = pairedData.minOf { it[0] }
    val xMax = pairedData.maxOf { it[0] }
    val yMin = pairedData.minOf { it[1] }
    val yMax = pairedData.maxOf { it[1] }
    
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(1f)
    ) {
        val width = size.width
        val height = size.height
        
        // Draw axes
        drawLine(
            color = NeonText,
            start = Offset(0f, height),
            end = Offset(width, height),
            strokeWidth = 2.dp.toPx()
        )
        drawLine(
            color = NeonText,
            start = Offset(0f, 0f),
            end = Offset(0f, height),
            strokeWidth = 2.dp.toPx()
        )
        
        // Draw points
        pairedData.forEach { point ->
            val x = scaleX(point[0], xMin, xMax, width)
            val y = height - scaleX(point[1], yMin, yMax, height)
            
            drawCircle(
                color = NeonGlow.copy(alpha = 0.7f),
                radius = 5.dp.toPx(),
                center = Offset(x, y)
            )
        }
    }
}

@Composable
private fun NormalDistributionPlot(
    data: List<Double>,
    modifier: Modifier = Modifier
) {
    val mean = data.average()
    val stdDev = sqrt(data.map { (it - mean).pow(2) }.average())
    
    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
    ) {
        val width = size.width
        val height = size.height
        val points = List(100) { i ->
            val x = mean - 3 * stdDev + (6 * stdDev * i / 99)
            val y = (1 / (stdDev * sqrt(2 * PI))) * 
                    exp(-(x - mean).pow(2) / (2 * stdDev.pow(2)))
            Offset(
                scaleX(x, mean - 3 * stdDev, mean + 3 * stdDev, width),
                height - scaleX(y, 0.0, 1 / (stdDev * sqrt(2 * PI)), height)
            )
        }
        
        // Draw curve
        val path = Path().apply {
            moveTo(points.first().x, points.first().y)
            points.forEach { point ->
                lineTo(point.x, point.y)
            }
        }
        
        drawPath(
            path = path,
            color = NeonGlow,
            style = Stroke(width = 2.dp.toPx())
        )
        
        // Draw mean line
        drawLine(
            color = NeonRed,
            start = Offset(scaleX(mean, mean - 3 * stdDev, mean + 3 * stdDev, width), 0f),
            end = Offset(scaleX(mean, mean - 3 * stdDev, mean + 3 * stdDev, width), height),
            strokeWidth = 2.dp.toPx()
        )
    }
}

private fun scaleX(value: Double, min: Double, max: Double, width: Float): Float {
    return ((value - min) / (max - min) * width).toFloat()
}

enum class VisualizationType {
    HISTOGRAM,
    BOX_PLOT,
    SCATTER_PLOT,
    NORMAL_DISTRIBUTION
}
