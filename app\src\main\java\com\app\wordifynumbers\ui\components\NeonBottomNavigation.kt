package com.app.wordifynumbers.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * A custom navigation bar item with neon styling
 */
@Composable
fun NeonNavigationBarItem(
    selected: Boolean,
    onClick: () -> Unit,
    selectedColor: Color,
    unselectedColor: Color = Color.Gray.copy(alpha = 0.6f),
    icon: @Composable () -> Unit,
    label: @Composable (() -> Unit)? = null
) {
    val color = if (selected) selectedColor else unselectedColor
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .padding(horizontal = 8.dp)
            .clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(48.dp)
                .padding(8.dp),
            contentAlignment = Alignment.Center
        ) {
            if (selected) {
                // Add glow effect when selected
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .clip(RoundedCornerShape(8.dp))
                        .background(selectedColor.copy(alpha = 0.1f))
                )
            }
            icon()
        }
        
        // Add label if provided
        if (label != null) {
            Spacer(modifier = Modifier.height(4.dp))
            Box(contentAlignment = Alignment.Center) {
                label()
            }
        }
    }
}

/**
 * A custom bottom navigation bar with neon styling
 */
@Composable
fun NeonBottomNavigation(
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color.Black.copy(alpha = 0.8f),
    contentColor: Color = Color.White,
    content: @Composable RowScope.() -> Unit
) {
    NavigationBar(
        modifier = modifier.height(64.dp),
        containerColor = backgroundColor,
        contentColor = contentColor,
        content = content
    )
}
