package com.app.wordifynumbers.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf

/**
 * Temporary stubs for Room database components to avoid compilation errors
 * while Room KSP issue is being resolved.
 * 
 * These stubs provide basic functionality using in-memory storage.
 */

// Room annotation stubs
annotation class Entity(val tableName: String = "")
annotation class PrimaryKey
annotation class Dao
annotation class Database(val entities: Array<kotlin.reflect.KClass<*>>, val version: Int, val exportSchema: Boolean = true)
annotation class TypeConverters(val value: kotlin.reflect.KClass<*>)
annotation class Insert(val onConflict: Int = 0)
annotation class Update
annotation class Delete
annotation class Query(val value: String)
annotation class TypeConverter

// OnConflictStrategy stub
object OnConflictStrategy {
    const val REPLACE = 1
    const val IGNORE = 2
    const val ABORT = 3
}

// Room database stub
abstract class RoomDatabase {
    abstract class Callback {
        open fun onCreate(db: Any) {}
    }
}

// Room builder stub
object Room {
    fun <T : RoomDatabase> databaseBuilder(
        context: android.content.Context,
        klass: kotlin.reflect.KClass<T>,
        name: String
    ): DatabaseBuilder<T> {
        @Suppress("UNCHECKED_CAST")
        return DatabaseBuilder<T>() as DatabaseBuilder<T>
    }
}

class DatabaseBuilder<T : RoomDatabase> {
    fun fallbackToDestructiveMigration(): DatabaseBuilder<T> = this
    fun addCallback(callback: RoomDatabase.Callback): DatabaseBuilder<T> = this
    fun build(): T {
        // Return a stub implementation
        @Suppress("UNCHECKED_CAST")
        return SimpleFinanceDatabaseStub() as T
    }
}

// SQLite stub
class SupportSQLiteDatabase

/**
 * Stub implementation of SimpleFinanceDatabase for compilation
 */
class SimpleFinanceDatabaseStub : SimpleFinanceDatabase() {
    private val daoStub = SimpleFinanceEntryDaoStub()
    
    override fun financeEntryDao(): SimpleFinanceEntryDao = daoStub
    
    companion object {
        @Volatile
        private var INSTANCE: SimpleFinanceDatabase? = null
        
        fun getDatabase(context: android.content.Context): SimpleFinanceDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = SimpleFinanceDatabaseStub()
                INSTANCE = instance
                instance
            }
        }
    }
}

/**
 * Stub implementation of SimpleFinanceEntryDao for compilation
 */
class SimpleFinanceEntryDaoStub : SimpleFinanceEntryDao {
    private val entries = mutableListOf<SimpleFinanceEntry>()
    
    override suspend fun insert(entry: SimpleFinanceEntry) {
        entries.removeAll { it.id == entry.id }
        entries.add(entry)
    }
    
    override suspend fun update(entry: SimpleFinanceEntry) {
        val index = entries.indexOfFirst { it.id == entry.id }
        if (index >= 0) {
            entries[index] = entry
        }
    }
    
    override suspend fun delete(entry: SimpleFinanceEntry) {
        entries.removeAll { it.id == entry.id }
    }
    
    override suspend fun deleteById(id: String) {
        entries.removeAll { it.id == id }
    }
    
    override fun getAllEntries(): Flow<List<SimpleFinanceEntry>> {
        return flowOf(entries.sortedByDescending { it.date })
    }
    
    override fun getEntriesByType(type: EntryType): Flow<List<SimpleFinanceEntry>> {
        return flowOf(entries.filter { it.type == type }.sortedByDescending { it.date })
    }
    
    override fun getEntriesByCategory(category: String): Flow<List<SimpleFinanceEntry>> {
        return flowOf(entries.filter { it.category == category }.sortedByDescending { it.date })
    }
    
    override fun searchEntries(query: String): Flow<List<SimpleFinanceEntry>> {
        return flowOf(entries.filter { it.note.contains(query, ignoreCase = true) }.sortedByDescending { it.date })
    }
    
    override fun getEntriesInDateRange(startDate: Long, endDate: Long): Flow<List<SimpleFinanceEntry>> {
        return flowOf(entries.filter { it.date in startDate..endDate }.sortedByDescending { it.date })
    }
    
    override fun getTotalIncome(): Flow<Double?> {
        return flowOf(entries.filter { it.type == EntryType.INCOME }.sumOf { it.amount })
    }
    
    override fun getTotalExpense(): Flow<Double?> {
        return flowOf(entries.filter { it.type == EntryType.EXPENSE }.sumOf { it.amount })
    }
    
    override fun getTotalIncomeInDateRange(startDate: Long, endDate: Long): Flow<Double?> {
        return flowOf(
            entries.filter { 
                it.type == EntryType.INCOME && it.date in startDate..endDate 
            }.sumOf { it.amount }
        )
    }
    
    override fun getTotalExpenseInDateRange(startDate: Long, endDate: Long): Flow<Double?> {
        return flowOf(
            entries.filter { 
                it.type == EntryType.EXPENSE && it.date in startDate..endDate 
            }.sumOf { it.amount }
        )
    }
    
    override suspend fun deleteAllEntries() {
        entries.clear()
    }
}
