package com.app.wordifynumbers.ui.screens

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Calculate
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel

@Composable
fun BasicCalculatorScreen(
    modifier: Modifier = Modifier,
    viewModel: CalculatorViewModel
) {
    StandardCalculatorLayout(
        title = "Basic Calculator",
        icon = Icons.Default.Calculate,
        accentColor = NeonBlue,
        showInfoButton = true,
        onInfoClick = { /* TODO: Show info dialog */ },
        
        // Input Section - Calculator Display
        inputSection = {
            CalculatorDisplay(
                expression = viewModel.state.collectAsState().value.display,
                result = viewModel.state.collectAsState().value.result,
                isError = viewModel.state.collectAsState().value.isError,
                onExpressionChange = viewModel::onInput,
                accentColor = NeonBlue
            )
        },
        
        // No separate action buttons needed
        actionButtons = null,
        
        // Result Section - Calculator Pad
        resultSection = {
            CalculatorPad(
                onNumberClick = viewModel::onNumber,
                onOperatorClick = viewModel::onOperator,
                onDeleteClick = viewModel::onDelete,
                onClearClick = viewModel::onClear,
                onEqualsClick = viewModel::onEquals,
                accentColor = NeonBlue,
                modifier = Modifier.fillMaxWidth()
            )
        },
        
        // Additional Content - History
        additionalContent = {
            val history = viewModel.history.collectAsState().value
            
            if (history.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(1.dp, NeonBlue.copy(alpha = 0.3f)),
                    shape = RoundedCornerShape(24.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "Recent Calculations",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonBlue
                        )
                        
                        Divider(
                            color = NeonBlue.copy(alpha = 0.3f),
                            modifier = Modifier.padding(vertical = 4.dp)
                        )
                        
                        history.take(5).forEach { item ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = item.input,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonText.copy(alpha = 0.8f)
                                )
                                
                                Text(
                                    text = "= ${item.result}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonBlue
                                )
                            }
                        }
                    }
                }
            }
        }
    )
}
