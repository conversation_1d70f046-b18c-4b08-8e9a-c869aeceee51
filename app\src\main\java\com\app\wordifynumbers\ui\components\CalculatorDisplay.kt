package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import kotlinx.coroutines.delay

@Composable
fun CalculatorDisplay(
    expression: String,
    result: String?,
    isError: Boolean = false,
    onExpressionChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow
) {
    val clipboardManager = LocalClipboardManager.current
    var showCopiedMessage by remember { mutableStateOf(false) }
    val currentResult = if (isError) "Error" else result ?: ""

    // Animated glow effect
    val infiniteTransition = rememberInfiniteTransition(label = "resultGlow")
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1.0f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Column(modifier = modifier.fillMaxWidth()) {
        // Expression input area with enhanced styling
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
        ) {
            Column(
                modifier = Modifier.fillMaxWidth()
            ) {
                // Expression with enhanced feedback
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = expression.ifEmpty { "Enter expression" },
                        style = MaterialTheme.typography.titleLarge,
                        color = if (expression.isEmpty()) NeonText.copy(alpha = 0.4f) else NeonText.copy(alpha = 0.9f),
                        modifier = Modifier.weight(1f)
                    )

                    AnimatedVisibility(
                        visible = expression.isNotEmpty(),
                        enter = fadeIn() + expandHorizontally(),
                        exit = fadeOut() + shrinkHorizontally()
                    ) {
                        IconButton(
                            onClick = { onExpressionChange("") },
                            modifier = Modifier.size(36.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Backspace,
                                contentDescription = "Clear",
                                tint = accentColor.copy(alpha = 0.7f)
                            )
                        }
                    }
                }

                // Divider with gradient
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(1.dp)
                        .background(
                            brush = Brush.horizontalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    accentColor.copy(alpha = 0.5f),
                                    Color.Transparent
                                )
                            )
                        )
                )

                Spacer(modifier = Modifier.height(12.dp))

                // Result with enhanced animations
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // Equals sign with animation
                            Text(
                                text = if (isError) "Error" else "=",
                                style = MaterialTheme.typography.titleLarge,
                                color = if (isError) NeonRed.copy(alpha = glowAlpha) else accentColor.copy(alpha = glowAlpha)
                            )

                            // Result with glow effect
                            Text(
                                text = currentResult,
                                style = MaterialTheme.typography.headlineMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = if (isError) NeonRed.copy(alpha = glowAlpha) else accentColor.copy(alpha = glowAlpha)
                            )
                        }
                    }

                    // Copy button with animation
                    if (!isError && result != null && result.isNotEmpty()) {
                        IconButton(
                            onClick = {
                                clipboardManager.setText(AnnotatedString(result))
                                showCopiedMessage = true
                            },
                            modifier = Modifier
                                .size(40.dp)
                                .clip(RoundedCornerShape(8.dp))
                        ) {
                            Icon(
                                imageVector = Icons.Default.ContentCopy,
                                contentDescription = "Copy result",
                                tint = accentColor.copy(alpha = 0.8f)
                            )
                        }
                    }
                }

                // Copied message with animation
                AnimatedVisibility(
                    visible = showCopiedMessage,
                    enter = fadeIn() + expandVertically(),
                    exit = fadeOut() + shrinkVertically()
                ) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Check,
                            contentDescription = null,
                            tint = accentColor,
                            modifier = Modifier.size(16.dp)
                        )

                        Spacer(modifier = Modifier.width(4.dp))

                        Text(
                            text = "Copied to clipboard",
                            style = MaterialTheme.typography.bodySmall,
                            color = accentColor
                        )
                    }
                }

                if (showCopiedMessage) {
                    LaunchedEffect(Unit) {
                        delay(2000)
                        showCopiedMessage = false
                    }
                }
            }
        }
    }
}

@Composable
fun CalculationHistory(
    history: List<HistoryEntry>,
    onHistoryItemClick: (HistoryEntry) -> Unit,
    modifier: Modifier = Modifier
) {
    if (history.isNotEmpty()) {
        NeonCard(
            modifier = modifier
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = "History",
                    style = MaterialTheme.typography.titleMedium,
                    color = NeonGlow
                )

                Spacer(modifier = Modifier.height(8.dp))

                history.forEach { entry ->
                    NeonSlide {
                        Surface(
                            onClick = { onHistoryItemClick(entry) },
                            color = NeonCard.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp)
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column {
                                    Text(
                                        text = entry.expression,
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = NeonText
                                    )
                                    Text(
                                        text = entry.result,
                                        style = MaterialTheme.typography.titleMedium,
                                        color = NeonGlow
                                    )
                                }

                                Icon(
                                    imageVector = Icons.Default.History,
                                    contentDescription = null,
                                    tint = NeonGlow.copy(alpha = 0.5f)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

data class HistoryEntry(
    val expression: String,
    val result: String,
    val timestamp: Long = System.currentTimeMillis()
)
