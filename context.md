# Wordify Numbers Android App Context for Cursor AI

## Project Overview

**App Name**: Wordify Numbers  
**Purpose**: An Android application that provides a suite of innovative conversion and educational tools:
1. **Number to Words**: Converts numeric input (e.g., 123) to English word representation (e.g., "One Hundred Twenty-Three").
2. **Digit Translator**: Displays digits (0-9) in multiple languages (English, Spanish, French, German, Hindi).
3. **Cultural Number Insights**: Shares cultural, historical, or mathematical facts about numbers or  after conversions.
5. **Personalized Conversion Profiles**: Saves user-defined conversion settings (e.g., , languages) for quick access.

**Target Audience**: General users, educators, travelers, finance enthusiasts, trivia lovers, and professionals needing frequent conversions.

**Platform**: Android-only (no cross-platform requirements).

## Features

### Number to Words
- **Input**: Positive integers up to 999,999,999.
- **Output**: English word representation (e.g., 123 → "One Hundred Twenty-Three").
- **Additional**: Copy-to-clipboard button, error handling for invalid inputs (e.g., non-numeric, out-of-range).

### Digit Translator
- **Functionality**: Displays digits (0-9) and their names in selected languages.
- **Languages**: English, Spanish, French, German, Hindi (stored locally for offline use).
- **UI**: Dropdown menu for language selection, grid/list for translations.


### Cultural Number Insights
- **Functionality**: Displays a cultural, historical, or mathematical fact related to the input number, digit, or currency after each conversion (e.g., "7 is a lucky number in many cultures" or "The euro (€) was introduced in 1999").
- **Purpose**: Educates and engages users, encouraging repeated use through trivia.
- **Data Source**: Local JSON or hardcoded facts; optional API integration (e.g., numbersapi.com) for dynamic facts.
- **UI**: Show fact in a `Card` below the conversion result, with a "Next Fact" button for variety.

### Personalized Conversion Profiles
- **Functionality**: Allows users to save and name conversion profiles (e.g., "Travel Budget" for USD to EUR/JPY with Spanish translations).
- **Purpose**: Streamlines frequent conversions for specific use cases (e.g., travel, education, business).
- **Storage**: SharedPreferences or Room database for profile data (, language, last input).
- **UI**: Dedicated "Profiles" tab or bottom sheet to create, edit, and select profiles.

### Supporting Features
- **History**: Stores up to 10 recent conversions (number-to-words, digit translations, currency) in SharedPreferences.
- **UI**: Tab-based navigation (four tabs: Number to Words, Digit Translator, , Profiles).
- **Theme**: Neon-inspired (blue/purple gradient), supports light/dark modes using Material 3 dynamic theming.
- **Accessibility**: Large text, high-contrast colors, optional voice input support.
- **Error Handling**: User-friendly messages for invalid inputs, network errors, and API issues using Snackbars.

## Technical Stack

- **Language**: Kotlin
- **UI Framework**: Jetpack Compose (Material 3)
- **Networking**: Retrofit for API calls
- **State Management**: ViewModel with Kotlin Coroutines
- **Local Storage**: SharedPreferences for history, cached rates, and profiles; optional Room for profiles

- **Testing**: JUnit for unit tests, Compose Testing for UI tests
- **Minimum API**: 24 (Nougat, covers ~95% of devices)
- **IDE**: Android Studio (Koala | 2024.1.1 recommended)

## Development Environment

### Prerequisites
- **Android Studio**: Install the latest version (Koala | 2024.1.1).
- **JDK**: Version 17.
- **Android SDK**: API 34 (UpsideDownCake).
- **Emulator**: Configure for API 24 and 34 for testing.
- **Git**: Initialize a repository for version control (optional GitHub integration).

### Project Setup
1. **Create Project**:
   - Template: Empty Activity (Jetpack Compose)
   - Name: NumLingua
   - Package: `com.example.numlingua`
   - Minimum API: 24
   - Language: Kotlin
2. **Gradle Configuration**:
   - **Project-level `build.gradle`**:
     - Repositories: `google()`, `mavenCentral()`
     - Android Gradle Plugin: 8.5.2
   - **App-level `build.gradle`**:
     - Enable Jetpack Compose
     - Dependencies:
       - Jetpack Compose (BOM 2024.06.00)
       - Retrofit (2.9.0)
       - Gson (2.10.1)
       - ViewModel (2.8.0)
       - Coroutines (1.8.0)
       - Material 3 (1.3.0)
     - Gradle version: 8.7 (in `gradle-wrapper.properties`)
3.
4. **Sync Project**:
   - Click `Sync Project with Gradle Files` in Android Studio.
   - If Gradle errors occur (noted from developer's past experience), verify Gradle and plugin compatibility.

## File Structure

- **`MainActivity.kt`**: Entry point with tab-based navigation using `TabRow`.
- **`NumberConverterViewModel.kt`**: Manages state and logic for all features (number-to-words, digit translations, , profiles).
- **`NumberToWords.kt`**: Utility class for converting numbers to English words.
- **`DigitTranslator.kt`**: Utility class with local mappings for digit translations.
- **`CulturalInsights.kt`**: Utility class for managing and retrieving cultural facts.
- **`ConversionProfile.kt`**: Data class and logic for managing user profiles.
- **``**: Retrofit service for fetching .
- **`FactsApiService.kt`**: Optional Retrofit service for numbersapi.com.
- **`Theme.kt`**: Defines neon-inspired theme with Material 3.
- **`UiComponents.kt`**: Composable functions for UI screens and shared components.

## Development Guidelines for Cursor AI

### General Instructions
- **Code Style**: Follow Kotlin conventions and Android best practices (e.g., use `val` over `var`, sealed classes for state).
- **Modularity**: Keep logic separate (e.g., UI in composables, business logic in ViewModel, utilities in dedicated classes).
- **Error Handling**: 
  - Validate inputs (e.g., non-numeric for number-to-words).
  - Handle network errors with `try-catch` for API calls.
  - Use Snackbars for user-facing errors.
- **Performance**: 
  - Cache API responses (, facts) in SharedPreferences.
  - Optimize Compose recompositions using `remember` and `derivedStateOf`.
- **Testing**:
  - Write unit tests for `NumberToWords`, `DigitTranslator`, and `CulturalInsights` (e.g., test edge cases like 0, 999999999).
  - Use Compose Testing for UI interactions (e.g., tab navigation, profile selection).
  - Test on emulators (API 24, 34) and a physical device.

### Specific Tasks
1. **MainActivity.kt**:
   - Implement a `TabRow` with four tabs: "Number to Words", "Digit Translator", "", "Profiles".
   - Use `NumberConverterViewModel` for state management.
   - Each tab renders a dedicated composable (`NumberToWordsScreen`, `DigitTranslatorScreen`, `CScreen`, `ProfilesScreen`).
2. **Number to Words**:
   - Create `NumberToWords.kt` with a function to convert numbers to English words.
   - Handle ranges: 0-9 (units), 10-19 (teens), 20-99 (tens), 100-999 (hundreds), up to millions.
   - Edge cases: Zero, invalid inputs, numbers > 999,999,999.
   - UI: `OutlinedTextField` for input, `Text` for result, `Card` for cultural fact, `Button` for copy-to-clipboard.
3. **Digit Translator**:
   - Create `DigitTranslator.kt` with a `Map` of languages to digit translations (0-9).
   - Support English, Spanish, French, German, Hindi (hardcoded for simplicity).
   - UI: `ExposedDropdownMenuBox` for language selection, `LazyVerticalGrid` for translations, `Card` for cultural fact.
4. 

   - Create `CulturalInsights.kt` with a local `Map` or JSON of facts (e.g., number trivia, currency history).
   - Optional: Integrate `FactsApiService.kt` for numbersapi.com to fetch dynamic facts.
   - Logic: Select a relevant fact based on input (number, digit, currency) or show a random fact.
   - UI: Display in a `Card` with a "Next Fact" button to cycle through facts.
6. **Personalized Conversion Profiles**:
   - Create `ConversionProfile.kt` with a data class (e.g., `data class Profile(name: String, : Pair<String, String>, language: String, lastInput: String)`).
   - Store profiles in SharedPreferences (JSON serialized) or Room database.
   - Logic: Allow users to save, edit, delete, and select profiles.
   - UI: `ProfilesScreen` with a `LazyColumn` for profile list, buttons for add/edit/delete, and a button to apply a profile to other screens.
7. **History**:
   - Store recent conversions in SharedPreferences (JSON serialized, up to 10 entries).
   - Display in a `BottomSheetScaffold` or separate screen.
8. **Theme**:
   - Define a neon-inspired theme in `Theme.kt` (blue/purple gradients, e.g., primary: #BB86FC).
   - Support light/dark modes with Material 3 dynamic theming.
9. **Copy-to-Clipboard**:
   - Use `ClipboardManager` to copy results.
   - Add a `Button` in each screen for this action.

### Known Developer Context
- **Experience**: The developer has worked on an Android app (VidStream Pro) using Kotlin and Android Studio, with experience in Jetpack Compose and GitHub Copilot.
- **Challenges**: Faced Gradle dependency issues (e.g., `Could not determine the dependencies`). Ensure clear Gradle configurations and compatibility (Plugin 8.5.2, Gradle 8.7).
- **Preferences**: Prefers neon-style UI (blue/purple), as seen in VidStream Pro. Emphasize a modern, visually appealing design with educational and personalized features.
- **Tools**: Uses Android Studio and AI coding assistants (e.g., Copilot). This context is for Cursor AI, so provide precise, error-free code suggestions.

### Cursor AI-Specific Instructions
- **Code Completion**:
  - Suggest complete files (e.g., `MainActivity.kt`, `CulturalInsights.kt`) based on the file structure.
  - Ensure imports are correct and dependencies are resolved.
  - Avoid incomplete snippets; provide full, runnable code.
- **Error Prevention**:
  - Double-check Gradle dependencies for compatibility with Jetpack Compose and Retrofit.
  - Validate API endpoints and response models (e.g., `ExchangeRateResponse` for currency API, `FactResponse` for facts API).
  - Include comments for complex logic (e.g., number-to-words algorithm, profile serialization).
- **Context Awareness**:
  - Reference existing files in the project (e.g., `Theme.kt` for theming).
  - Maintain consistency with neon-inspired design and Material 3 components.
- **Testing Support**:
  - Generate unit tests for utility classes (`NumberToWords`, `DigitTranslator`, `CulturalInsights`).
  - Suggest Compose Testing code for UI interactions (e.g., tab navigation, profile selection).
- **Debugging** neoplasms  - Include Logcat statements for API calls and errors.
  - Suggest checks for common Android issues (e.g., network connectivity, API rate limits).

### Example Workflow with Cursor AI
1. **Create `MainActivity.kt`**:
   - Cursor AI should generate a tab-based layout with Jetpack Compose.
   - Prompt: "Generate MainActivity.kt for NumLingua with a TabRow for four screens, using Jetpack Compose and NumberConverterViewModel."
2. **Implement `CulturalInsights.kt`**:
   - Cursor AI should create a utility class for managing cultural facts.
   - Prompt: "Create CulturalInsights.kt with a local Map of number and currency facts, and optional Retrofit integration for numbersapi.com."
3. **Set Up Profiles**:
   - Cursor AI should create `ConversionProfile.kt` and related UI.
   - Prompt: "Generate ConversionProfile.kt with a data class and logic for saving profiles, plus a ProfilesScreen composable."
4. **Debug Gradle Issues**:
   - If Gradle errors occur, prompt Cursor AI: "Suggest fixes for Gradle dependency conflicts in NumLingua with Jetpack Compose and Retrofit."

### Publishing Notes
- **Signed AAB**: Generate via `Build > Generate Signed Bundle/APK` in Android Studio.
- **Google Play Console**:
  - Prepare assets: Neon-style app icon (512x512px), screenshots of all screens (highlighting facts and profiles), privacy policy for API usage.
  - Use internal testing track before public release.
- **Compliance**: Ensure no sensitive data (e.g., API keys) is hardcoded in the codebase.

## Additional Notes
- **Offline Support**: Cache , facts, and use local data for digit translations and profiles to ensure functionality without internet.
- **Performance**: Optimize API calls by limiting frequency (e.g., once every 24 hours unless forced refresh).
- **Scalability**: Design `DigitTranslator` and `CulturalInsights` to easily add new languages or facts (e.g., via JSON or backend in future iterations).
- **User Experience**: Incorporate animations (e.g., fade-in for facts), a neon-themed UI, and personalized profiles to align with developer's aesthetic preferences and user engagement goals.


- **Jetpack Compose**: [developer.android.com/compose](https://developer.android.com/compose)
- **Retrofit**: [square.github.io/retrofit](https://square.github.io/retrofit)
- **Material 3**: [m3.material.io](https://m3.material.io)

This context file provides all necessary information for Cursor AI to assist in developing NumLingua with its unique features. Use it as a reference for code generation, debugging, and testing. If additional files or clarifications are needed, prompt Cursor AI with specific requests based on this context.


# Cursor AI Prompt for WordifyNumbers: Create Business and Finance Calculator User Guide

## Objective

Create a **User Guide** for the **Business and Finance Calculator** feature in the **Big Digits Learning Hub** of the **WordifyNumbers** Android app, enabling users (businessmen in the USA, India, European countries like Germany, France, Spain, and global markets) to understand and use the calculator for daily financial tasks. The guide must be clear, user-friendly, and assume no prior technical knowledge, explaining **what the calculator does**, **how to navigate it**, and **step-by-step instructions** for all 18 features (e.g., Profit Margin, Tax/VAT Estimator, SEPA Payment Cost). Write in simple language, align with the neon-themed UI (#BB86FC, dark gradient #121212 to #1E1E2E), and emphasize the app’s offline functionality and multilingual support (English, Spanish, French, German, Hindi). The guide is a standalone document, focusing solely on the calculator’s usage, designed to be included in the app’s Help section or as a downloadable PDF.

## Content Requirements

- **Tone**: Friendly, professional, and encouraging, guiding users like a helpful advisor.
- **Structure**: Organized sections for introduction, navigation, and each feature, with numbered steps, screenshots (described for UI), and tips.
- **Details**:
  - **What**: Explain the calculator’s purpose (financial tasks for business).
  - **Navigation**: Describe accessing the calculator, selecting countries/languages, and using the UI.implementation "androidx.activity:activity-compose:1.7.0"
  implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.5.1"
  - **Features**: Step-by-step instructions for all 18 features, including inputs, outputs, and saving results.
  - **Screenshots**: Describe neon-styled UI elements (#BB86FC) for clarity (e.g., dropdowns, buttons).
  - **Tips**: Practical advice (e.g., “Save budgets for monthly reviews”).
- **Offline Emphasis**: Highlight that all features work without internet.
- **Multilingual**: Note language selection for results (e.g., “one million” in English, “ek crore” in Hindi).
- **Audience**: Businessmen (e.g., retailers, SMEs) in USA, India, EU, with examples relevant to each region.

## User Guide Content

---

# WordifyNumbers: Business and Finance Calculator User Guide

## Welcome to the Business and Finance Calculator!

The **Business and Finance Calculator** in **WordifyNumbers** is your go-to tool for managing daily business finances, whether you’re running a shop in New York, a startup in Mumbai, or a company in Berlin. This calculator helps you calculate profits, taxes, loans, budgets, and more, all **without needing an internet connection**. It works in **English**, **Spanish**, **French**, **German**, and **Hindi**, showing results in numbers and words (e.g., “$1,000,000, one million dollars” or “₹10,00,00,000, one crore”). Designed with a vibrant neon look (purple #BB86FC on a dark background), it’s easy to use for everyone, from small business owners to corporate managers.

This guide shows you how to use all 18 features, from estimating EU VAT to planning savings goals, with step-by-step instructions and tips. Let’s get started!

## Getting Started

### Opening the Calculator

1. **Open WordifyNumbers**: Launch the app on your Android phone (works on Android 7.0+).
2. **Go to Digits Tab**: Tap the **Digits** tab at the bottom (look for the magnifying glass icon in purple #BB86FC).
3. **Select Calculator**: On the Big Digits Learning Hub screen, tap the **Calculator** button (neon purple outline). You’ll see a screen with dropdowns and input fields.

**Screenshot Description**: The Digits tab shows a magnifying glass icon (#BB86FC). The Hub screen has a neon “Calculator” button, a dropdown for calculation types (e.g., Profit Margin), and a country selector (e.g., USA, India, Germany).

**Tip**: The app works offline, so you can use it anywhere—on a flight, in a remote area, or during a meeting!

### Choosing Your Country and Language

1. **Select Country**: Tap the **Country** dropdown (neon #BB86FC) to choose your region (e.g., USA, India, Germany). This adjusts  ($, ₹, €) and tax rates (e.g., USA state taxes, India GST, Germany VAT).
2. **Select Language**: Tap the **Language** dropdown to pick English, Spanish, French, German, or Hindi. Results will show in your language (e.g., “one million” in English, “ein Million” in German).
3. **Confirm**: Your choices are saved automatically and apply to all calculations.

**Screenshot Description**: The Calculator screen shows two neon dropdowns: “Country” (e.g., “India” selected) and “Language” (e.g., “Hindi”). Input fields below adjust to the currency (e.g., ₹).

**Tip**: Choose your country for accurate tax and currency settings, especially for EU VAT or India GST calculations.

### Navigating the Calculator

- **Calculation Type**: Tap the neon dropdown to pick a feature (e.g., Tax/VAT Estimator, Loan Repayment). The screen updates with relevant input fields.
- **Input Fields**: Enter numbers in neon-bordered fields (e.g., Revenue, Loan Amount). Use the keyboard for precise amounts.
- **Calculate Button**: Tap the purple “Calculate” button to see results in numbers and words.
- **Save Button**: Tap “Save” to store results for later (e.g., “2025 Budget”).
- **History**: Tap “View History” (bottom sheet slides up) to see saved calculations.
- **Notifications**: Toggle the neon switch to enable/disable reminders (e.g., tax deadlines).

**Screenshot Description**: The screen shows a neon dropdown for calculation types, input fields with purple borders (#BB86FC), a “Calculate” button, and a “View History” button. Results appear in a neon Card with numbers and words.

**Tip**: Save important calculations to track your business over time, like monthly budgets or tax estimates.

## Using the Calculator Features

Below are step-by-step instructions for all 18 features, with examples for USA, India, and EU users.

### 1. Profit Margin Calculator

**What It Does**: Calculates how much profit you make on a product or deal, showing gross and net margins. Perfect for retailers in the USA, India, or EU.

**How to Use**:

1. Select **Profit Margin Calculator** from the calculation type dropdown.
2. Choose your **Country** (e.g., USA, India, Germany) for currency ($, ₹, €).
3. Enter **Revenue** (e.g., $1,000,000 for a USA sale, ₹10,00,00,000 for an India deal, €1,000,000 for a Germany contract).
4. Enter **Cost of Goods Sold (COGS)** (e.g., $600,000, ₹6,00,00,000, €600,000).
5. (Optional) Enter **Operating Expenses** (e.g., $200,000, ₹2,00,00,000, €200,000).
6. Tap **Calculate**. See results: Gross Profit ($400,000), Gross Margin (40%), Net Profit ($200,000), Net Margin (20%), in words (e.g., “four hundred thousand dollars” in English, “vierhunderttausend Euro” in German).
7. Tap **Save** to store (name it, e.g., “Q1 Sales”).

**Screenshot Description**: Shows input fields for Revenue, COGS, Expenses (neon borders), a “Calculate” button, and a neon Card with results (e.g., “Gross Profit: $400,000, four hundred thousand dollars”).

**Tip**: Use this to compare product profitability, like a USA gadget sale vs. an India textile deal.

### 2. Tax and VAT Estimator

**What It Does**: Estimates taxes for USA (federal/state), India (GST, income tax), and EU (VAT, corporate tax), helping with compliance.

**How to Use**:

1. Select **Tax and VAT Estimator** from the dropdown.
2. Choose **Country** (e.g., USA, India, France).
3. For USA: Select **State** (e.g., California: 8.84%) and **Filing Status** (e.g., Corporation).
4. For India: Select **Tax Type** (e.g., GST: 18%).
5. For EU: Select **Country** (e.g., France: 20% VAT).
6. Enter **Taxable Income/Revenue** (e.g., $500,000, ₹5,00,00,000, €500,000).
7. Tap **Calculate**. See results: Tax/VAT Amount (e.g., $105,000 USA, ₹90,00,000 India, €100,000 France), Effective Rate (e.g., 21%).
8. Tap **Save** (e.g., “2025 VAT”).

**Screenshot Description**: Shows dropdowns for Country and State/Tax Type, an input field for Income, and a neon Card with results (e.g., “VAT: €100,000, one hundred thousand euros”).

**Tip**: Save tax estimates monthly to prepare for filings, especially EU VAT deadlines.

### 3. Loan Repayment Calculator

**What It Does**: Estimates monthly payments and interest for business loans, used in USA, India, and EU (e.g., SEPA loans).

**How to Use**:

1. Select **Loan Repayment Calculator**.
2. Choose **Country** for typical rates (e.g., 6% USA, 10% India, 4% Germany).
3. Enter **Loan Amount** (e.g., $500,000, ₹5,00,00,000, €500,000).
4. Enter **Annual Interest Rate** (e.g., 6%).
5. Enter **Loan Term** (years, e.g., 5).
6. Select **Payment Frequency** (monthly, quarterly).
7. Tap **Calculate**. See results: Monthly Payment ($9,660), Total Interest, Total Cost, and a 12-month schedule.
8. Tap **Save** (e.g., “Equipment Loan”).

**Screenshot Description**: Shows input fields for Amount, Rate, Term, a dropdown for Frequency, a table for the schedule, and a neon Card with results (e.g., “Monthly: $9,660”).

**Tip**: Check the schedule to plan cash flow for loan repayments.

### 4. Budget Planner

**What It Does**: Helps plan and track budgets for businesses in USA, India, or EU.

**How to Use**:

1. Select **Budget Planner**.
2. Choose **Country** for currency.
3. Enter **Total Budget** (e.g., $1,000,000, ₹10,00,00,000, €1,000,000).
4. Add **Categories** (e.g., Marketing, Salaries) and amounts via neon input fields.
5. Select **Time Period** (monthly, yearly).
6. Tap **Calculate**. See results: Remaining Budget (e.g., €200,000), Category Percentages, and alerts if overspent.
7. Tap **Save** (e.g., “Annual Budget”).

**Screenshot Description**: Shows a field for Total Budget, a list of editable Category fields, and a neon Card with results (e.g., “Remaining: €200,000”).

**Tip**: Update your budget monthly to stay on track, especially for EU compliance costs.

### 5. Investment Return Calculator

**What It Does**: Estimates returns on investments (e.g., stocks, ventures) for USA, India, EU markets.

**How to Use**:

1. Select **Investment Return Calculator**.
2. Choose **Country** for typical rates (e.g., 8% USA, 12% India, 5% Germany).
3. Enter **Initial Investment** (e.g., $100,000, ₹1,00,00,000, €100,000).
4. Enter **Annual Return Rate** (e.g., 8%).
5. Enter **Time Horizon** (years, e.g., 10).
6. Select **Type** (Simple or Compound Interest) and **Frequency** (monthly, yearly).
7. Tap **Calculate**. See results: Future Value, Total Return, Annualized Return.
8. Tap **Save** (e.g., “Stock Investment”).

**Screenshot Description**: Shows input fields for Investment, Rate, Time, dropdowns for Type/Frequency, and a neon Card with results (e.g., “Future Value: €215,892”).

**Tip**: Compare Simple vs. Compound to decide investment strategies.

### 6. 

**What It Does**: Converts  for international trade (e.g., USA-India, EU-USA).

**How to Use**:

1. Select ****.
2. Choose **Country** to set default currency (e.g., USD for USA, INR for India, EUR for Germany).
3. Enter **Amount** (e.g., $1,000,000, ₹10,00,00,000, €1,000,000).
4. Select **Source Currency** (e.g., USD) and **Target Currency** (e.g., INR).
5. Tap **Calculate**. See results: Converted Amount (e.g., $1,000,000 → ₹8,35,00,000).
6. Tap **Save** (e.g., “Export Conversion”).

**Screenshot Description**: Shows an input field for Amount, dropdowns for Source/Target Currency, and a neon Card with results (e.g., “₹8,35,00,000, eight crore thirty-five lakh”).

**Tip**: Use for quick pricing in international deals, like EU-India exports.

### 7. Break-Even Analysis

**What It Does**: Calculates sales needed to cover costs, key for retail in USA, India, EU.

**How to Use**:

1. Select **Break-Even Analysis**.
2. Choose **Country** for currency.
3. Enter **Fixed Costs** (e.g., $200,000, ₹2,00,00,000, €200,000).
4. Enter **Variable Cost per Unit** (e.g., $50, ₹5,000, €50).
5. Enter **Selling Price per Unit** (e.g., $100, ₹10,000, €100).
6. Tap **Calculate**. See results: Break-Even Point (e.g., 4,000 units), Revenue.
7. Tap **Save** (e.g., “Product Launch”).

**Screenshot Description**: Shows input fields for Costs and Prices, and a neon Card with results (e.g., “Break-Even: 4,000 units”).

**Tip**: Use to decide if a new product is profitable.

### 8. Savings Goal Planner

**What It Does**: Plans savings for business goals (e.g., equipment, expansion).

**How to Use**:

1. Select **Savings Goal Planner**.
2. Choose **Country** for currency and rates.
3. Enter **Goal Amount** (e.g., $500,000, ₹5,00,00,000, €500,000).
4. Enter **Monthly Contribution** (e.g., $10,000, ₹10,00,000, €10,000).
5. (Optional) Enter **Interest Rate** (e.g., 3% USA, 6% India, 2% Germany).
6. Enter **Time Frame** (years, e.g., 5).
7. Tap **Calculate**. See results: Time to Goal, Total Contributions, Interest.
8. Tap **Save** (e.g., “New Store Fund”).

**Screenshot Description**: Shows input fields for Goal, Contribution, Rate, Time, and a neon Card with results (e.g., “Time: 4.8 years”).

**Tip**: Set multiple goals to track different projects.

### 9. Cash Flow Forecast

**What It Does**: Projects cash flow for business planning in USA, India, EU.

**How to Use**:

1. Select **Cash Flow Forecast**.
2. Choose **Country** for currency.
3. Enter **Initial Cash Balance** (e.g., $100,000, ₹1,00,00,000, €100,000).
4. Enter **Monthly Inflows** (e.g., $50,000, ₹50,00,000, €50,000).
5. Enter **Monthly Outflows** (e.g., $40,000, ₹40,00,000, €40,000).
6. Enter **Forecast Period** (months, e.g., 12).
7. Tap **Calculate**. See results: Monthly Balances, Net Cash Flow, warnings for negative balances.
8. Tap **Save** (e.g., “2025 Forecast”).

**Screenshot Description**: Shows input fields for Balance, Inflows, Outflows, Period, a table for balances, and a neon Card with results (e.g., “Net Flow: €120,000”).

**Tip**: Check warnings to avoid cash shortages.

### 10. ROI Analysis

**What It Does**: Measures return on investment for projects (e.g., marketing).

**How to Use**:

1. Select **ROI Analysis**.
2. Choose **Country** for currency.
3. Enter **Investment Cost** (e.g., $50,000, ₹50,00,000, €50,000).
4. Enter **Net Profit** (e.g., $20,000, ₹20,00,000, €20,000).
5. Enter **Time Period** (years, e.g., 2).
6. Tap **Calculate**. See results: ROI (e.g., 40%), Annualized ROI.
7. Tap **Save** (e.g., “Ad Campaign ROI”).

**Screenshot Description**: Shows input fields for Cost, Profit, Time, and a neon Card with results (e.g., “ROI: 40%”).

**Tip**: Compare ROI for different projects to prioritize investments.

### 11. Inventory Turnover

**What It Does**: Assesses inventory efficiency for retail/manufacturing.

**How to Use**:

1. Select **Inventory Turnover**.
2. Choose **Country** for currency.
3. Enter **Cost of Goods Sold (COGS)** (e.g., $500,000, ₹5,00,00,000, €500,000).
4. Enter **Average Inventory** (e.g., $100,000, ₹1,00,00,000, €100,000).
5. Select **Time Period** (yearly, quarterly).
6. Tap **Calculate**. See results: Turnover Ratio (e.g., 5 times), Days to Sell (e.g., 73 days).
7. Tap **Save** (e.g., “Stock Analysis”).

**Screenshot Description**: Shows input fields for COGS, Inventory, a dropdown for Period, and a neon Card with results (e.g., “Turnover: 5 times”).

**Tip**: Aim for a higher ratio to reduce storage costs.

### 12. Depreciation Calculator

**What It Does**: Calculates asset depreciation for accounting (e.g., equipment).

**How to Use**:

1. Select **Depreciation Calculator**.
2. Choose **Country** for currency.
3. Enter **Asset Cost** (e.g., $100,000, ₹1,00,00,000, €100,000).
4. Enter **Salvage Value** (e.g., $10,000, ₹10,00,000, €10,000).
5. Enter **Useful Life** (years, e.g., 5).
6. Select **Method** (Straight-Line, Declining Balance).
7. Tap **Calculate**. See results: Annual Depreciation, 5-year schedule.
8. Tap **Save** (e.g., “Vehicle Depreciation”).

**Screenshot Description**: Shows input fields for Cost, Salvage, Life, a dropdown for Method, a table for the schedule, and a neon Card with results.

**Tip**: Use Straight-Line for simple accounting, Declining for faster write-offs.

### 13. Working Capital Calculator

**What It Does**: Evaluates liquidity for daily operations.

**How to Use**:

1. Select **Working Capital Calculator**.
2. Choose **Country** for currency.
3. Enter **Current Assets** (e.g., $300,000, ₹3,00,00,000, €300,000).
4. Enter **Current Liabilities** (e.g., $150,000, ₹1,50,00,000, €150,000).
5. Tap **Calculate**. See results: Working Capital (e.g., €150,000), Current Ratio (e.g., 2.0).
6. Tap **Save** (e.g., “Q1 Liquidity”).

**Screenshot Description**: Shows input fields for Assets, Liabilities, and a neon Card with results (e.g., “Capital: €150,000”).

**Tip**: Aim for a Current Ratio above 1.5 for healthy finances.

### 14. SEPA Payment Cost Estimator (EU-Specific)

**What It Does**: Estimates costs for SEPA transactions in EU countries.

**How to Use**:

1. Select **SEPA Payment Cost Estimator**.
2. Choose **Country** (e.g., Germany, France).
3. Enter **Transaction Amount** (e.g., €10,000).
4. Select **Transaction Type** (SEPA Credit Transfer, Direct Debit).
5. Select **Bank Fee Structure** (Flat, e.g., €0.50; Percentage, e.g., 0.1%).
6. Tap **Calculate**. See results: Transaction Cost (e.g., €1), Total Cost.
7. Tap **Save** (e.g., “SEPA Payment”).

**Screenshot Description**: Shows an input field for Amount, dropdowns for Type/Fee, and a neon Card with results (e.g., “Cost: €1”).

**Tip**: Use for cross-border EU payments to minimize costs.

### 15. MiFID II Cost Reporting (EU-Specific)

**What It Does**: Estimates transaction costs for EU investments under MiFID II.

**How to Use**:

1. Select **MiFID II Cost Reporting**.
2. Choose **Country** (e.g., Spain).
3. Enter **Investment Amount** (e.g., €100,000).
4. Select **Instrument Type** (Stocks, Bonds, ETFs).
5. Enter **Broker Fee** (e.g., 0.2% or €10 flat).
6. Enter **Additional Costs** (e.g., 0.1% stamp duty).
7. Tap **Calculate**. See results: Total Cost (e.g., €300), Cost Breakdown.
8. Tap **Save** (e.g., “Stock Trade”).

**Screenshot Description**: Shows input fields for Amount, Costs, dropdowns for Instrument/Fee, and a neon Card with results (e.g., “Total Cost: €300”).

**Tip**: Check costs before investing to comply with EU regulations.

### 16. History and Saved Calculations

**What It Does**: Lets you review past calculations.

**How to Use**:

1. From the Calculator screen, tap **View History** (neon button).
2. See a list of saved calculations (e.g., “2025 VAT: €95,000”).
3. Tap a calculation to view details.
4. Tap **Delete** to remove an entry.
5. Close the bottom sheet by swiping down.

**Screenshot Description**: Shows a bottom sheet with a neon Card list of calculations, each with a “Delete” button (#BB86FC).

**Tip**: Name calculations clearly (e.g., “Q2 Tax”) for easy reference.

### 17. Multilingual Support

**What It Does**: Shows results in your preferred language.

**How to Use**:

1. On the Calculator screen, tap the **Language** dropdown.
2. Choose **English**, **Spanish**, **French**, **German**, or **Hindi**.
3. Perform any calculation. Results show in your language (e.g., “one million” in English, “ein Million” in German).
4. Your choice saves automatically.

**Screenshot Description**: Shows the Language dropdown (neon #BB86FC) with “German” selected, and results like “eine Million Euro”.

**Tip**: Switch languages to share results with international partners.

### 18. Offline Notifications

**What It Does**: Sends reminders for taxes, budgets, or goals, even offline.

**How to Use**:

1. On the Calculator screen, find the **Notifications** toggle (neon switch).
2. Tap to enable/disable reminders (e.g., daily budgets, monthly VAT).
3. If enabled, get alerts (e.g., “File Germany VAT by 20th!”) at set times.
4. Your choice saves automatically.

**Screenshot Description**: Shows a neon toggle switch (#BB86FC) labeled “Notifications” in the Calculator settings area.

**Tip**: Enable notifications to stay on top of deadlines, like EU VAT filings.

## Additional Tips

- **Explore All Features**: Try each calculator to discover tools for your business, like SEPA for EU payments or GST for India sales.
- **Save Regularly**: Save calculations to build a history for tax season or investor reports.
- **Use Offline**: No internet needed—perfect for travel or remote areas.
- **Check Alerts**: Pay attention to budget or cash flow warnings to avoid financial issues.
- **Contact Support**: If stuck, email <EMAIL> (available in the app’s Help section).

## Troubleshooting

- **Wrong Currency?** Check your Country setting in the dropdown.
- **No Results?** Ensure all required fields are filled (e.g., Revenue for Profit Margin).
- **Language Issue?** Reselect your language to refresh results.
- **Notifications Not Working?** Confirm the toggle is enabled and check your phone’s notification settings.

---

## Implementation Instructions

- **File**: Create `UserGuide.txt` or `UserGuide.md` in the `docs` module, formatted as plain text or Markdown for inclusion in the app’s Help section or as a PDF.
- **Content**:
  - Use the provided text, ensuring clear headings, numbered steps, and concise language.
  - Describe UI elements with neon theme details (#BB86FC, dark gradient #121212 to #1E1E2E).
  - Include screenshot descriptions (no actual images, just text) for key screens.
- **Integration**:
  - Add a **Help** button in `BigDigitsScreen.kt` (neon #BB86FC) to open the guide in a WebView or TextView.
  - Optionally, generate a PDF version using a library like `PdfDocument` and store in app assets.
- **Testing**:
  - **Readability**: Verify the guide is clear for non-technical users (e.g., test with sample users).
  - **Accuracy**: Ensure steps match the UI in `BigDigitsScreen.kt` (e.g., dropdowns, buttons).
  - **Language**: Check multilingual examples (e.g., Hindi, German) align with `BusinessCalculator.kt` mappings.
  - **Emulator**: Test on API 24, 34; confirm guide displays correctly in app.
- **Error Handling**:
  - If guide fails to load, show a Snackbar (#BB86FC action text, #121212 background) with “Try again” option.
  - Log issues: `Log.d("UserGuide", "Error: $message")`.

## Developer Notes

- **Preferences**: User-friendly, neon UI (#BB86FC), offline, relevant for USA, India, EU.
- **Tools**: Android Studio, Cursor AI.
- **Challenges**: Ensure guide matches UI exactly; keep language simple.

## Deliverables

- **File**: `UserGuide.txt` or `UserGuide.md` with the provided content.
- **Integration**: Update `BigDigitsScreen.kt` to include a Help button linking to the guide.
- **Tests**: Readability and UI accuracy tests.
- **Documentation**: Inline comments in `BigDigitsScreen.kt` for Help integration.

## Notes

- **Offline**: Guide stored locally in app assets, no internet needed.
- **Retention**: Clear instructions encourage frequent use of calculator features.
- **UI**: Neon styling (#BB86FC) in guide descriptions matches app.
- **UX**: Simple language, regional examples (USA, India, EU), and practical tips ensure accessibility.
- **Scalability**: Guide can be updated for new calculator features.

Generate the User Guide as specified, ensuring clear, user-friendly instructions for the Business and Finance Calculator. Provide the guide content in a text file, with comments for integration, aligned with Android best practices and neon UI (#BB86FC).

# Final Checklist
- [ ] Global: all screens use the dark‑neon background & accent palette.
- [ ] Calculator Selection: title + intro + glow‑border cards.
- [ ] Calculator Tabs: themed TextFields, Buttons, shadows, layouts.
- [ ] Backend: all VM methods tested & passing.
- [ ] Consistency: spacing, typography, icon treatment, micro‑animations across the app.

Use this prompt with your designer/developer as the single source of truth for implementing the full theme across Wordify Numbers.