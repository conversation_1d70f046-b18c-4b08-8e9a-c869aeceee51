{"name": "openvpn", "displayName": "OpenVPN", "description": "Language support and snippets for OpenVPN configuration files", "version": "0.3.4", "publisher": "<PERSON>berg", "license": "MIT", "author": {"name": "<PERSON>", "url": "http://github.com/idleberg"}, "scripts": {"build": "gulp", "lint": "vscode-linter", "test": "npm run lint", "vscode:prepublish": "npm run build"}, "keywords": ["openvpn", "open vpn"], "repository": {"type": "git", "url": "https://github.com/idleberg/vscode-openvpn"}, "homepage": "https://github.com/idleberg/vscode-openvpn#readme", "bugs": {"url": "https://github.com/idleberg/vscode-openvpn/issues"}, "devDependencies": {"gulp": "^4.0.2", "gulp-raster": "^0.2.0", "gulp-rename": "^2.0.0", "husky": "^8.0.3", "vscode": "^1.1.37", "vscode-linter": "^0.3.1"}, "icon": "images/logo.png", "engines": {"vscode": "^1.0.0"}, "categories": ["Programming Languages", "Snippets"], "contributes": {"languages": [{"id": "openvpn", "aliases": ["OpenVPN Configuration", "OpenVPN"], "extensions": ["ovpn", "openvpn", "conf"], "configuration": "./config/openvpn.json"}], "grammars": [{"language": "openvpn", "scopeName": "source.openvpn", "path": "./syntaxes/openvpn.tmLanguage"}], "snippets": [{"language": "openvpn", "path": "./snippets/openvpn.json"}]}}