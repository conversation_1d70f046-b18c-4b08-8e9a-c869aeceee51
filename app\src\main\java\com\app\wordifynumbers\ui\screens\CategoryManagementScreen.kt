package com.app.wordifynumbers.ui.screens

import android.content.Context
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.FeedbackUtil

/**
 * Screen for managing custom categories
 */
@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun CategoryManagementScreen(
    onBackClick: () -> Unit,
    primaryColor: Color = NeonBlue
) {
    val context = LocalContext.current
    val keyboardController = LocalSoftwareKeyboardController.current

    // State
    var showAddCategoryDialog by remember { mutableStateOf(false) }
    var newCategoryName by remember { mutableStateOf("") }
    var editingCategory by remember { mutableStateOf<String?>(null) }
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    var categoryToDelete by remember { mutableStateOf<String?>(null) }

    // Get categories from preferences
    val standardCategories = listOf("Income", "Expense", "Budget", "Investment", "Insurance", "Saving", "Tax", "Debt")
    val sharedPrefs = context.getSharedPreferences("finance_notepad_prefs", Context.MODE_PRIVATE)
    val customCategories = remember {
        mutableStateOf(sharedPrefs.getStringSet("custom_categories", mutableSetOf())?.toList() ?: emptyList())
    }

    // Function to add a new category
    fun addCategory(name: String) {
        if (name.isBlank()) return

        val currentCategories = sharedPrefs.getStringSet("custom_categories", mutableSetOf()) ?: mutableSetOf()
        currentCategories.add(name)
        sharedPrefs.edit().putStringSet("custom_categories", currentCategories).apply()

        // Update state
        customCategories.value = currentCategories.toList()

        // Show confirmation
        android.widget.Toast.makeText(
            context,
            "Category '$name' added successfully",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }

    // Function to update a category
    fun updateCategory(oldName: String, newName: String) {
        if (newName.isBlank()) return

        val currentCategories = sharedPrefs.getStringSet("custom_categories", mutableSetOf()) ?: mutableSetOf()
        currentCategories.remove(oldName)
        currentCategories.add(newName)
        sharedPrefs.edit().putStringSet("custom_categories", currentCategories).apply()

        // Update state
        customCategories.value = currentCategories.toList()

        // Show confirmation
        android.widget.Toast.makeText(
            context,
            "Category updated successfully",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }

    // Function to delete a category
    fun deleteCategory(name: String) {
        val currentCategories = sharedPrefs.getStringSet("custom_categories", mutableSetOf()) ?: mutableSetOf()
        currentCategories.remove(name)
        sharedPrefs.edit().putStringSet("custom_categories", currentCategories).apply()

        // Update state
        customCategories.value = currentCategories.toList()

        // Show confirmation
        android.widget.Toast.makeText(
            context,
            "Category '$name' deleted",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(NeonBackground)
    ) {
        // App header
        CategoryHeader(
            title = "Wordify Numbers",
            subtitle = "Category Management",
            primaryColor = primaryColor,
            onThemeChange = { /* Theme change not available here */ },
            actions = {
                // Back button
                IconButton(
                    onClick = onBackClick,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(NeonCard)
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = primaryColor
                    )
                }

                Spacer(modifier = Modifier.width(8.dp))
            }
        )

        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header section
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard
                ),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Category,
                            contentDescription = null,
                            tint = primaryColor,
                            modifier = Modifier.size(24.dp)
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = "Manage Categories",
                            style = MaterialTheme.typography.titleLarge,
                            color = primaryColor,
                            fontWeight = FontWeight.Bold
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "Create, edit, and delete custom categories to better organize your finances.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.8f)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Add category button
                    Button(
                        onClick = {
                            showAddCategoryDialog = true
                            newCategoryName = ""
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = primaryColor
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Add category"
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text("Add New Category")
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Standard categories section
            Text(
                text = "Standard Categories",
                style = MaterialTheme.typography.titleMedium,
                color = NeonText,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )

            Card(
                colors = CardDefaults.cardColors(
                    containerColor = NeonSurface
                ),
                shape = RoundedCornerShape(12.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 200.dp)
                ) {
                    items(standardCategories) { category ->
                        val categoryColor = when (category) {
                            "Income" -> NeonGreen
                            "Expense" -> NeonRed
                            "Budget" -> NeonGold
                            "Investment" -> NeonPurple
                            "Insurance" -> NeonCyan
                            "Saving" -> NeonBlue
                            "Tax" -> NeonOrange
                            "Debt" -> NeonPink
                            else -> primaryColor
                        }

                        val categoryIcon = when (category) {
                            "Income" -> Icons.Default.ArrowUpward
                            "Expense" -> Icons.Default.ArrowDownward
                            "Budget" -> Icons.Default.AccountBalance
                            "Investment" -> Icons.Default.TrendingUp
                            "Insurance" -> Icons.Default.Shield
                            "Saving" -> Icons.Default.Savings
                            "Tax" -> Icons.Default.Receipt
                            "Debt" -> Icons.Default.CreditCard
                            else -> Icons.Default.Label
                        }

                        // Standard category item (non-editable)
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 12.dp)
                        ) {
                            // Category icon
                            Box(
                                modifier = Modifier
                                    .size(40.dp)
                                    .background(
                                        color = categoryColor.copy(alpha = 0.1f),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = categoryIcon,
                                    contentDescription = null,
                                    tint = categoryColor,
                                    modifier = Modifier.size(24.dp)
                                )
                            }

                            Spacer(modifier = Modifier.width(16.dp))

                            // Category name
                            Text(
                                text = category,
                                style = MaterialTheme.typography.bodyLarge,
                                color = NeonText,
                                fontWeight = FontWeight.Medium
                            )

                            Spacer(modifier = Modifier.weight(1f))

                            // Standard label
                            Box(
                                modifier = Modifier
                                    .background(
                                        color = Color.Gray.copy(alpha = 0.2f),
                                        shape = RoundedCornerShape(4.dp)
                                    )
                                    .padding(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                Text(
                                    text = "Standard",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = NeonText.copy(alpha = 0.7f)
                                )
                            }
                        }

                        // Divider
                        if (category != standardCategories.last()) {
                            Divider(
                                color = Color.White.copy(alpha = 0.1f),
                                modifier = Modifier.padding(horizontal = 16.dp)
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Custom categories section
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp)
            ) {
                Text(
                    text = "Custom Categories",
                    style = MaterialTheme.typography.titleMedium,
                    color = NeonText,
                    fontWeight = FontWeight.Bold
                )

                Text(
                    text = "${customCategories.value.size} categories",
                    style = MaterialTheme.typography.bodySmall,
                    color = NeonText.copy(alpha = 0.7f)
                )
            }

            if (customCategories.value.isEmpty()) {
                // Empty state
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = NeonSurface
                    ),
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Category,
                            contentDescription = null,
                            tint = Color.Gray,
                            modifier = Modifier.size(48.dp)
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = "No Custom Categories Yet",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonText,
                            fontWeight = FontWeight.Medium
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        Text(
                            text = "Create your first custom category to better organize your finances",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText.copy(alpha = 0.7f),
                            textAlign = androidx.compose.ui.text.style.TextAlign.Center
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        Button(
                            onClick = {
                                showAddCategoryDialog = true
                                newCategoryName = ""
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = primaryColor
                            )
                        ) {
                            Icon(
                                imageVector = Icons.Default.Add,
                                contentDescription = "Add category"
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Text("Add Category")
                        }
                    }
                }
            } else {
                // Custom categories list
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = NeonSurface
                    ),
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = 300.dp)
                    ) {
                        items(customCategories.value) { category ->
                            // Generate consistent color based on category name
                            val categoryColor = Color(
                                red = (category.hashCode() % 200 + 55) / 255f,
                                green = ((category.hashCode() / 2) % 200 + 55) / 255f,
                                blue = ((category.hashCode() / 3) % 200 + 55) / 255f
                            )

                            // Custom category item (editable)
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 16.dp, vertical = 12.dp)
                            ) {
                                // Category icon
                                Box(
                                    modifier = Modifier
                                        .size(40.dp)
                                        .background(
                                            color = categoryColor.copy(alpha = 0.1f),
                                            shape = CircleShape
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Star,
                                        contentDescription = null,
                                        tint = categoryColor,
                                        modifier = Modifier.size(24.dp)
                                    )
                                }

                                Spacer(modifier = Modifier.width(16.dp))

                                // Category name
                                Text(
                                    text = category,
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = NeonText,
                                    fontWeight = FontWeight.Medium
                                )

                                Spacer(modifier = Modifier.weight(1f))

                                // Edit button
                                IconButton(
                                    onClick = {
                                        editingCategory = category
                                        newCategoryName = category
                                        showAddCategoryDialog = true
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Edit,
                                        contentDescription = "Edit category",
                                        tint = primaryColor
                                    )
                                }

                                // Delete button
                                IconButton(
                                    onClick = {
                                        categoryToDelete = category
                                        showDeleteConfirmation = true
                                    }
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Delete,
                                        contentDescription = "Delete category",
                                        tint = NeonRed
                                    )
                                }
                            }

                            // Divider
                            if (category != customCategories.value.last()) {
                                Divider(
                                    color = Color.White.copy(alpha = 0.1f),
                                    modifier = Modifier.padding(horizontal = 16.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    // Add/Edit Category Dialog
    if (showAddCategoryDialog) {
        Dialog(
            onDismissRequest = {
                showAddCategoryDialog = false
                editingCategory = null
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = NeonSurface
                ),
                shape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // Dialog header
                    Text(
                        text = if (editingCategory == null) "Add New Category" else "Edit Category",
                        style = MaterialTheme.typography.titleLarge,
                        color = primaryColor,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Category name field
                    OutlinedTextField(
                        value = newCategoryName,
                        onValueChange = { newCategoryName = it },
                        label = { Text("Category Name", color = NeonText) },
                        placeholder = { Text("Enter category name", color = NeonHint) },
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(
                            capitalization = KeyboardCapitalization.Words,
                            imeAction = ImeAction.Done
                        ),
                        keyboardActions = androidx.compose.foundation.text.KeyboardActions(
                            onDone = { keyboardController?.hide() }
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = primaryColor,
                            unfocusedBorderColor = primaryColor.copy(alpha = 0.5f),
                            focusedTextColor = NeonText,
                            unfocusedTextColor = NeonText,
                            cursorColor = primaryColor,
                            focusedContainerColor = NeonCard,
                            unfocusedContainerColor = NeonCard
                        ),
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    // Action buttons
                    Row(
                        horizontalArrangement = Arrangement.End,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // Cancel button
                        TextButton(
                            onClick = {
                                showAddCategoryDialog = false
                                editingCategory = null
                            }
                        ) {
                            Text("Cancel", color = NeonText)
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        // Save button
                        Button(
                            onClick = {
                                if (newCategoryName.isNotBlank()) {
                                    if (editingCategory != null) {
                                        // Update existing category
                                        updateCategory(editingCategory!!, newCategoryName)
                                    } else {
                                        // Add new category
                                        addCategory(newCategoryName)
                                    }

                                    showAddCategoryDialog = false
                                    editingCategory = null
                                    newCategoryName = ""
                                    FeedbackUtil.buttonPress(context)
                                }
                            },
                            enabled = newCategoryName.isNotBlank(),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = primaryColor,
                                disabledContainerColor = primaryColor.copy(alpha = 0.3f)
                            )
                        ) {
                            Text(if (editingCategory == null) "Add Category" else "Update Category")
                        }
                    }
                }
            }
        }
    }

    // Delete Confirmation Dialog
    if (showDeleteConfirmation && categoryToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteConfirmation = false
                categoryToDelete = null
            },
            title = {
                Text(
                    "Delete Category",
                    color = NeonText,
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Text(
                    "Are you sure you want to delete the category '$categoryToDelete'? This action cannot be undone.",
                    color = NeonText.copy(alpha = 0.8f)
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        categoryToDelete?.let { deleteCategory(it) }
                        showDeleteConfirmation = false
                        categoryToDelete = null
                        FeedbackUtil.buttonPress(context)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = NeonRed
                    )
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeleteConfirmation = false
                        categoryToDelete = null
                    }
                ) {
                    Text("Cancel", color = NeonText)
                }
            },
            containerColor = NeonSurface,
            titleContentColor = NeonText,
            textContentColor = NeonText.copy(alpha = 0.8f)
        )
    }
}

// Category Management Header
@Composable
private fun CategoryHeader(
    title: String,
    subtitle: String,
    primaryColor: Color,
    onThemeChange: () -> Unit,
    actions: @Composable RowScope.() -> Unit = {},
    currentTheme: String = "DARK"
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = NeonBackground
            )
            .padding(top = 8.dp, bottom = 16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            // Top row with app title and actions
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // App logo and title
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Logo
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(primaryColor.copy(alpha = 0.2f)),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "W",
                            style = MaterialTheme.typography.headlineMedium.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = primaryColor
                        )
                    }

                    Spacer(modifier = Modifier.width(12.dp))

                    // App title
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = NeonText
                    )
                }

                Spacer(modifier = Modifier.weight(1f))

                // Action buttons
                Row(
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically,
                    content = actions
                )

                // Theme toggle button
                IconButton(
                    onClick = onThemeChange,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(NeonCard)
                ) {
                    Icon(
                        imageVector = when (currentTheme) {
                            "DARK" -> Icons.Default.DarkMode
                            "LIGHT" -> Icons.Default.LightMode
                            "FINANCE" -> Icons.Default.AttachMoney
                            else -> Icons.Default.DarkMode
                        },
                        contentDescription = "Toggle theme",
                        tint = primaryColor
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Subtitle with icon
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Category,
                    contentDescription = null,
                    tint = primaryColor,
                    modifier = Modifier.size(24.dp)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.titleMedium,
                    color = primaryColor
                )
            }
        }
    }
}
