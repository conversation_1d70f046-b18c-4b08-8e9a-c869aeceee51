package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalculatorSettings(
    settings: UICalculatorPreferences,
    onSettingsChange: (UICalculatorPreferences) -> Unit,
    onClearHistory: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showClearHistoryDialog by remember { mutableStateOf(false) }

    NeonCard(
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "Calculator Settings",
                style = MaterialTheme.typography.titleLarge,
                color = NeonGlow
            )

            // Precision Setting
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Decimal Places",
                    color = NeonText
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    IconButton(
                        onClick = {
                            onSettingsChange(
                                settings.copy(
                                    decimalPlaces = (settings.decimalPlaces - 1).coerceAtLeast(0)
                                )
                            )
                        },
                        enabled = settings.decimalPlaces > 0
                    ) {
                        Icon(
                            imageVector = Icons.Default.Remove,
                            contentDescription = "Decrease precision",
                            tint = NeonText
                        )
                    }
                    
                    Text(
                        text = settings.decimalPlaces.toString(),
                        color = NeonGlow,
                        style = MaterialTheme.typography.titleMedium
                    )
                    
                    IconButton(
                        onClick = {
                            onSettingsChange(
                                settings.copy(
                                    decimalPlaces = (settings.decimalPlaces + 1).coerceAtMost(10)
                                )
                            )
                        },
                        enabled = settings.decimalPlaces < 10
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Increase precision",
                            tint = NeonText
                        )
                    }
                }
            }

            // Angle Unit Setting
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Angle Unit",
                    color = NeonText
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    AngleUnit.values().forEach { unit ->
                        NeonButton(
                            onClick = {
                                onSettingsChange(settings.copy(angleUnit = unit))
                            },
                            enabled = settings.angleUnit != unit
                        ) {
                            Text(unit.displayName)
                        }
                    }
                }
            }

            // Number Format Setting
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Number Format",
                    color = NeonText
                )
                
                NeonSegmentedButtons(
                    options = NumberFormat.values().map { it.displayName },
                    selectedOption = settings.numberFormat.displayName,
                    onOptionSelected = { selected ->
                        NumberFormat.values()
                            .find { it.displayName == selected }
                            ?.let { format ->
                                onSettingsChange(settings.copy(numberFormat = format))
                            }
                    }
                )
            }

            // Auto-Calculate Setting
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Auto-Calculate",
                    color = NeonText
                )
                
                Switch(
                    checked = settings.autoCalculate,
                    onCheckedChange = {
                        onSettingsChange(settings.copy(autoCalculate = it))
                    },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = NeonGlow,
                        checkedTrackColor = NeonGlow.copy(alpha = 0.5f),
                        uncheckedThumbColor = NeonText,
                        uncheckedTrackColor = NeonText.copy(alpha = 0.5f)
                    )
                )
            }

            // History Settings
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Calculator History",
                        color = NeonText
                    )
                    Text(
                        text = "Clear all calculation history",
                        style = MaterialTheme.typography.bodySmall,
                        color = NeonText.copy(alpha = 0.7f)
                    )
                }
                
                NeonButton(
                    onClick = { showClearHistoryDialog = true },
                    modifier = Modifier
                ) {
                    Icon(
                        imageVector = Icons.Default.DeleteForever,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Clear History")
                }
            }
        }
    }

    if (showClearHistoryDialog) {
        AlertDialog(
            onDismissRequest = { showClearHistoryDialog = false },
            title = {
                Text(
                    text = "Clear History",
                    color = NeonRed
                )
            },
            text = {
                Text(
                    text = "Are you sure you want to clear all calculation history? This action cannot be undone.",
                    color = NeonText
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        onClearHistory()
                        showClearHistoryDialog = false
                    },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = NeonRed
                    )
                ) {
                    Text("Clear")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showClearHistoryDialog = false }
                ) {
                    Text("Cancel")
                }
            }
        )
    }
}
