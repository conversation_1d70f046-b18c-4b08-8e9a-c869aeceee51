package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.*

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CalculationHistoryBottomSheet(
    type: CalculationType,
    onDismiss: () -> Unit,
    onItemClick: (CalculationHistoryItem) -> Unit,
    onClearHistory: () -> Unit,
    historyItems: List<CalculationHistoryItem>,
    modifier: Modifier = Modifier
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        containerColor = NeonDeepBlue,
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Calculation History",
                    style = MaterialTheme.typography.titleLarge,
                    color = NeonGlow
                )
                
                IconButton(
                    onClick = {
                        onClearHistory()
                        onDismiss()
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.DeleteSweep,
                        contentDescription = "Clear history",
                        tint = NeonRed
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            if (historyItems.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No history available",
                        color = NeonText.copy(alpha = 0.6f)
                    )
                }
            } else {
                LazyColumn(
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    contentPadding = PaddingValues(bottom = 32.dp)
                ) {
                    items(historyItems) { item ->
                        NeonCard(
                            modifier = Modifier
                                .fillMaxWidth()
                                .animateItemPlacement()
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp)
                            ) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = item.getFormattedDate(),
                                        style = MaterialTheme.typography.bodySmall,
                                        color = NeonText.copy(alpha = 0.6f)
                                    )
                                    
                                    IconButton(
                                        onClick = { 
                                            onItemClick(item)
                                            onDismiss()
                                        }
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Refresh,
                                            contentDescription = "Use this calculation",
                                            tint = NeonGlow
                                        )
                                    }
                                }

                                Spacer(modifier = Modifier.height(4.dp))

                                NeonShimmer {
                                    Column {
                                        Text(
                                            text = item.input,
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = NeonText,
                                            maxLines = 2,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                        
                                        Text(
                                            text = "= ${item.result}",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = NeonGlow
                                        )
                                    }
                                }

                                when (type) {
                                    CalculationType.UNIT_CONVERSION,
                                    CalculationType.FINANCIAL,
                                    CalculationType.STATISTICS -> {
                                        AnimatedVisibility(
                                            visible = true,
                                            enter = expandVertically() + fadeIn(),
                                            exit = shrinkVertically() + fadeOut()
                                        ) {
                                            Chart(item)
                                        }
                                    }
                                    else -> { /* No chart needed */ }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun Chart(item: CalculationHistoryItem) {
    // Add mini chart visualization based on calculation type
    when (item.type) {
        CalculationType.UNIT_CONVERSION -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(top = 8.dp)
            ) {
                // Simple conversion arrow visualization
                Canvas(modifier = Modifier.fillMaxSize()) {
                    drawLine(
                        color = NeonGlow.copy(alpha = 0.5f),
                        start = Offset(0f, size.height / 2),
                        end = Offset(size.width, size.height / 2),
                        strokeWidth = 2.dp.toPx()
                    )
                    // Add arrow head
                    val arrowSize = 10.dp.toPx()
                    drawLine(
                        color = NeonGlow.copy(alpha = 0.5f),
                        start = Offset(size.width - arrowSize, size.height / 2 - arrowSize),
                        end = Offset(size.width, size.height / 2),
                        strokeWidth = 2.dp.toPx()
                    )
                    drawLine(
                        color = NeonGlow.copy(alpha = 0.5f),
                        start = Offset(size.width - arrowSize, size.height / 2 + arrowSize),
                        end = Offset(size.width, size.height / 2),
                        strokeWidth = 2.dp.toPx()
                    )
                }
            }
        }
        CalculationType.FINANCIAL -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(top = 8.dp)
            ) {
                // Mini growth curve
                Canvas(modifier = Modifier.fillMaxSize()) {
                    val path = Path().apply {
                        moveTo(0f, size.height)
                        cubicTo(
                            size.width * 0.3f, size.height * 0.7f,
                            size.width * 0.6f, size.height * 0.5f,
                            size.width, size.height * 0.2f
                        )
                    }
                    drawPath(
                        path = path,
                        color = NeonGreen.copy(alpha = 0.5f),
                        style = Stroke(width = 2.dp.toPx())
                    )
                }
            }
        }
        CalculationType.STATISTICS -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(top = 8.dp)
            ) {
                // Mini bell curve
                Canvas(modifier = Modifier.fillMaxSize()) {
                    val path = Path().apply {
                        moveTo(0f, size.height)
                        cubicTo(
                            size.width * 0.2f, size.height,
                            size.width * 0.4f, 0f,
                            size.width * 0.5f, 0f
                        )
                        cubicTo(
                            size.width * 0.6f, 0f,
                            size.width * 0.8f, size.height,
                            size.width, size.height
                        )
                    }
                    drawPath(
                        path = path,
                        color = NeonPurple.copy(alpha = 0.5f),
                        style = Stroke(width = 2.dp.toPx())
                    )
                }
            }
        }
        else -> { /* No chart needed */ }
    }
}
