package com.app.wordifynumbers.ui.components

import androidx.compose.foundation.BorderStroke as ComposeBorderStroke
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Re-export of Compose's BorderStroke to make it available throughout the app
 */
typealias BorderStroke = ComposeBorderStroke

/**
 * Creates a border stroke with the given width and color
 */
fun createBorderStroke(width: Dp = 1.dp, color: Color): BorderStroke {
    return BorderStroke(width, SolidColor(color))
}
