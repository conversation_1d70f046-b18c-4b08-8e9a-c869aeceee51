package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.NumberConverterViewModel
import com.app.wordifynumbers.NumberConverterViewModelFactory
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.utils.NumberToWords
import com.app.wordifynumbers.utils.CulturalInsights

import com.app.wordifynumbers.util.FeedbackUtil
import java.util.Locale

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NumberToWordsScreen(
    onNavigateToDigitTranslator: () -> Unit = {},
    onNavigateToDetailedTranslation: () -> Unit = {},
    modifier: Modifier = Modifier,
    onNavigateToCalculator: (String) -> Unit = {},
    onNavigateToLargeNumbers: () -> Unit = {}
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val viewModel: NumberConverterViewModel = viewModel(factory = NumberConverterViewModelFactory(context))
    val numberInput by viewModel.numberInput.collectAsState()
    val numberWords by viewModel.numberWords.collectAsState()
    val numberError by viewModel.numberError.collectAsState()
    val supportsDetailedFormat by viewModel.supportsDetailedFormat.collectAsState()
    val scrollState = rememberScrollState()

    // Let MainScreen handle back navigation automatically

    // State for language dropdown
    var expanded by remember { mutableStateOf(false) }
    var selectedLanguage by remember { mutableStateOf("English") }
    var selectedLocale by remember { mutableStateOf(Locale.US) }

    // List of 20 most used world languages
    val languages = listOf(
        "English" to Locale.US,
        "Chinese (Mandarin)" to Locale.CHINESE,
        "Spanish" to Locale("es"),
        "Hindi" to Locale("hi"),
        "Arabic" to Locale("ar"),
        "Bengali" to Locale("bn"),
        "Portuguese" to Locale("pt"),
        "Russian" to Locale("ru"),
        "Japanese" to Locale.JAPANESE,
        "German" to Locale.GERMAN,
        "French" to Locale.FRENCH,
        "Turkish" to Locale("tr"),
        "Korean" to Locale.KOREAN,
        "Italian" to Locale.ITALIAN,
        "Polish" to Locale("pl"),
        "Ukrainian" to Locale("uk"),
        "Dutch" to Locale("nl"),
        "Thai" to Locale("th"),
        "Indonesian" to Locale("id"),
        "Vietnamese" to Locale("vi"),
        "Urdu" to Locale("ur") // Added Urdu language
    )

    // Get cultural insight for the current number
    val number = numberInput.toLongOrNull() ?: 0L
    val culturalInsight = remember(number, selectedLocale) {
        CulturalInsights.getInsightForNumber(number, selectedLocale)
    }

    // Background gradient animation
    val infiniteTransition = rememberInfiniteTransition(label = "backgroundPulse")
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.95f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(8000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseAnimation"
    )

    // Glow animation for text
    val glowAnimation by infiniteTransition.animateFloat(
        initialValue = 0.7f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        NeonBackground,
                        NeonBackground.copy(alpha = 0.8f)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Custom header with app name on top (matching Finance Notepad)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp, vertical = 6.dp)
                    .shadow(
                        elevation = 10.dp,
                        spotColor = NeonGold.copy(alpha = 0.3f),
                        ambientColor = NeonGold.copy(alpha = 0.2f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.95f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp, horizontal = 12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // App name at the top
                    Text(
                        text = "Wordify Numbers",
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 0.5.sp
                        ),
                        color = NeonGold,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Divider with glow effect
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(0.95f)
                            .height(1.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        NeonGold.copy(alpha = 0.3f),
                                        NeonGold.copy(alpha = 0.5f),
                                        NeonGold.copy(alpha = 0.3f),
                                        Color.Transparent
                                    )
                                )
                            )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Screen title and actions in a row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Screen title with icon
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Numbers,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(24.dp)
                            )

                            Text(
                                text = "Number to Words",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 18.sp
                                ),
                                color = NeonGold
                            )
                        }

                        // Action buttons
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Digit Translator
                            WordifyHeaderAction(
                                icon = Icons.Default.Transform,
                                contentDescription = "Digit Translator",
                                accentColor = NeonGold,
                                onClick = {
                                    onNavigateToDigitTranslator()
                                    FeedbackUtil.buttonPress(context)
                                }
                            )

                            // Calculator
                            WordifyHeaderAction(
                                icon = Icons.Default.Calculate,
                                contentDescription = "Calculators",
                                accentColor = NeonGold,
                                onClick = {
                                    onNavigateToCalculator("basic")
                                    FeedbackUtil.buttonPress(context)
                                }
                            )


                            // Large Numbers
                            WordifyHeaderAction(
                                icon = Icons.Default.School,
                                contentDescription = "Large Numbers",
                                accentColor = NeonGold,
                                onClick = {
                                    onNavigateToLargeNumbers()
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                    }
                }
            }

            // Feature introduction card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = NeonGold.copy(alpha = 0.2f),
                        ambientColor = NeonGold.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = NeonGold,
                            modifier = Modifier.size(24.dp)
                        )

                        Text(
                            text = "Convert Digits to Words",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = NeonGold
                        )
                    }

                    Divider(
                        color = NeonGold.copy(alpha = 0.3f),
                        thickness = 1.dp,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Text(
                        text = "Enter any number and see how it's written in words across different languages. Perfect for learning, education, or document preparation.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }
            }

            // Language selection card (moved to the top)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = NeonGold.copy(alpha = 0.2f),
                        ambientColor = NeonGold.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Language,
                            contentDescription = null,
                            tint = NeonGold,
                            modifier = Modifier.size(24.dp)
                        )

                        Text(
                            text = "Select Your Language",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = NeonGold
                        )
                    }

                    Divider(
                        color = NeonGold.copy(alpha = 0.3f),
                        thickness = 1.dp,
                        modifier = Modifier.fillMaxWidth()
                    )

                    // Language dropdown
                    Box(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        OutlinedTextField(
                            value = selectedLanguage,
                            onValueChange = { },
                            readOnly = true,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(80.dp)
                                .shadow(
                                    elevation = 12.dp,
                                    spotColor = NeonGold.copy(alpha = 0.3f),
                                    ambientColor = NeonGold.copy(alpha = 0.2f),
                                    shape = RoundedCornerShape(16.dp)
                                ),
                            label = { Text("Choose from 21 languages") },
                            trailingIcon = {
                                IconButton(onClick = { expanded = !expanded }) {
                                    Icon(
                                        imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                        contentDescription = "Select language",
                                        tint = NeonGold
                                    )
                                }
                            },
                            textStyle = MaterialTheme.typography.headlineMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = NeonGold,
                                unfocusedBorderColor = NeonGold.copy(alpha = 0.5f),
                                focusedLabelColor = NeonGold,
                                unfocusedLabelColor = NeonGold.copy(alpha = 0.7f),
                                focusedTextColor = NeonText,
                                unfocusedTextColor = NeonText,
                                // Enhanced container colors
                                focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                                unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                            ),
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Language,
                                    contentDescription = null,
                                    tint = NeonGold,
                                    modifier = Modifier.size(32.dp)
                                )
                            }
                        )

                        DropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false },
                            modifier = Modifier
                                .fillMaxWidth(0.9f)
                                .background(NeonCard)
                                .heightIn(max = 400.dp) // Limit height for scrolling
                        ) {
                            // Search field
                            var searchQuery by remember { mutableStateOf("") }

                            OutlinedTextField(
                                value = searchQuery,
                                onValueChange = { searchQuery = it },
                                placeholder = { Text("Search languages", color = NeonText.copy(alpha = 0.7f)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 16.dp, vertical = 8.dp),
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedBorderColor = NeonGold,
                                    unfocusedBorderColor = NeonGold.copy(alpha = 0.5f),
                                    focusedLabelColor = NeonGold,
                                    unfocusedLabelColor = NeonGold.copy(alpha = 0.7f),
                                    cursorColor = NeonGold,
                                    focusedTextColor = NeonText,
                                    unfocusedTextColor = NeonText,
                                    focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                                    unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                                ),
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Search,
                                        contentDescription = "Search",
                                        tint = NeonGold
                                    )
                                },
                                singleLine = true
                            )

                            // Filter languages based on search
                            val filteredLanguages = remember(searchQuery, languages) {
                                if (searchQuery.isBlank()) {
                                    languages
                                } else {
                                    languages.filter { (name, _) ->
                                        name.contains(searchQuery, ignoreCase = true)
                                    }
                                }
                            }

                            // Language list
                            filteredLanguages.forEach { (name, locale) ->
                                val isSelected = selectedLanguage == name
                                val isUrdu = name == "Urdu" // Highlight Urdu

                                DropdownMenuItem(
                                    text = {
                                        Text(
                                            text = name,
                                            color = when {
                                                isUrdu -> NeonGold // Highlight Urdu with special color
                                                isSelected -> NeonGold
                                                else -> NeonText
                                            },
                                            fontWeight = if (isSelected || isUrdu) FontWeight.Bold else FontWeight.Normal
                                        )
                                    },
                                    onClick = {
                                        selectedLanguage = name
                                        selectedLocale = locale
                                        expanded = false
                                        searchQuery = ""
                                        viewModel.onLanguageSelected(name)
                                        FeedbackUtil.buttonPress(context)
                                    },
                                    leadingIcon = {
                                        Icon(
                                            imageVector = Icons.Default.Check,
                                            contentDescription = null,
                                            tint = if (isSelected) NeonGold else Color.Transparent
                                        )
                                    },
                                    trailingIcon = {
                                        if (isUrdu) {
                                            Icon(
                                                imageVector = Icons.Default.Star,
                                                contentDescription = "Featured language",
                                                tint = NeonGold
                                            )
                                        }
                                    }
                                )
                            }

                            // Show message if no languages match search
                            if (filteredLanguages.isEmpty()) {
                                Text(
                                    text = "No languages match your search",
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    textAlign = TextAlign.Center,
                                    color = NeonText.copy(alpha = 0.7f)
                                )
                            }
                        }
                    }
                }
            }

            // Input card with number field (moved below language selection)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = NeonGold.copy(alpha = 0.2f),
                        ambientColor = NeonGold.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Numbers,
                            contentDescription = null,
                            tint = NeonGold,
                            modifier = Modifier.size(24.dp)
                        )

                        Text(
                            text = "Enter Your Number",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = NeonGold
                        )
                    }

                    Divider(
                        color = NeonGold.copy(alpha = 0.3f),
                        thickness = 1.dp,
                        modifier = Modifier.fillMaxWidth()
                    )

                    // Large input field with enhanced styling
                    OutlinedTextField(
                        value = numberInput,
                        onValueChange = {
                            // Allow any number of digits (no limit)
                            if (it.isEmpty() || it.all { char -> char.isDigit() }) {
                                viewModel.onNumberInputChange(it)
                                FeedbackUtil.buttonPress(context)
                            }
                        },
                        label = { Text("Enter any number") },
                        placeholder = { Text("Example: 9999999999999") },
                        isError = !numberError.isNullOrEmpty(),
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp) // Even larger height for prominence
                            .shadow(
                                elevation = 16.dp,
                                spotColor = NeonGlow.copy(alpha = 0.4f),
                                ambientColor = NeonGlow.copy(alpha = 0.3f),
                                shape = RoundedCornerShape(16.dp)
                            ),
                        singleLine = true,
                        maxLines = 1,
                        textStyle = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = NeonGold,
                            unfocusedBorderColor = NeonGold.copy(alpha = 0.7f), // More visible border
                            focusedLabelColor = NeonGold,
                            unfocusedLabelColor = NeonGold.copy(alpha = 0.8f),
                            cursorColor = NeonGold,
                            focusedTextColor = NeonText,
                            unfocusedTextColor = NeonText,
                            focusedPlaceholderColor = NeonText.copy(alpha = 0.7f),
                            unfocusedPlaceholderColor = NeonText.copy(alpha = 0.5f),
                            // Enhanced container colors
                            focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                            unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                        ),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Numbers,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(40.dp) // Even larger icon
                            )
                        }
                    )

                    if (!numberError.isNullOrEmpty()) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .background(NeonRed.copy(alpha = 0.1f))
                                .padding(8.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Error,
                                contentDescription = null,
                                tint = NeonRed,
                                modifier = Modifier.size(16.dp)
                            )

                            Text(
                                text = numberError ?: "",
                                color = NeonRed,
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }

            // Result card with enhanced styling
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = NeonGold.copy(alpha = 0.2f),
                        ambientColor = NeonGold.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Translate,
                            contentDescription = null,
                            tint = NeonGold,
                            modifier = Modifier.size(24.dp)
                        )

                        Text(
                            text = "In ${selectedLanguage} Words",
                            style = MaterialTheme.typography.titleMedium.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = NeonGold
                        )

                            // Special badge for Urdu
                            if (selectedLanguage == "Urdu") {
                                Card(
                                    shape = RoundedCornerShape(16.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = NeonGlow.copy(alpha = 0.2f)
                                    ),
                                    border = BorderStroke(1.dp, SolidColor(NeonGlow.copy(alpha = 0.5f))),
                                    modifier = Modifier.padding(start = 4.dp)
                                ) {
                                    Text(
                                        text = "Featured",
                                        style = MaterialTheme.typography.labelSmall.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = NeonGlow,
                                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                    )
                                }
                            }
                        }

                    Divider(
                        color = NeonGold.copy(alpha = 0.3f),
                        thickness = 1.dp,
                        modifier = Modifier.fillMaxWidth()
                    )

                    // Action buttons in a row at the end
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Detailed view button (always visible for testing)
                        IconButton(
                            onClick = {
                                onNavigateToDetailedTranslation()
                                FeedbackUtil.buttonPress(context)
                            }
                        ) {
                            Icon(
                                imageVector = Icons.Default.FormatListNumbered,
                                contentDescription = "Show detailed format",
                                tint = NeonGold
                            )
                        }

                        // Copy button
                        IconButton(
                            onClick = {
                                if (numberWords.isNotEmpty()) {
                                    clipboardManager.setText(AnnotatedString(numberWords))
                                    FeedbackUtil.buttonPress(context)
                                }
                            },
                            enabled = numberWords.isNotEmpty()
                        ) {
                            Icon(
                                imageVector = Icons.Default.ContentCopy,
                                contentDescription = "Copy to clipboard",
                                tint = if (numberWords.isNotEmpty()) NeonGold else NeonGold.copy(alpha = 0.3f)
                            )
                        }
                    }

                    // Result box with enhanced styling
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(8.dp))
                            .background(
                                Brush.verticalGradient(
                                    colors = listOf(
                                        NeonCard.copy(alpha = 0.3f),
                                        NeonCard.copy(alpha = 0.5f)
                                    )
                                )
                            )
                            .border(
                                width = 1.dp,
                                color = NeonGold.copy(alpha = 0.3f),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        if (numberWords.isNotEmpty()) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                Text(
                                    text = numberWords,
                                    style = MaterialTheme.typography.headlineSmall.copy(
                                        fontWeight = FontWeight.SemiBold
                                    ),
                                    color = NeonGold,
                                    textAlign = TextAlign.Center,
                                    modifier = Modifier.fillMaxWidth()
                                )

                                // Add a prominent button for detailed view
                                Button(
                                    onClick = {
                                        onNavigateToDetailedTranslation()
                                        FeedbackUtil.buttonPress(context)
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = NeonGold.copy(alpha = 0.2f),
                                        contentColor = NeonGold
                                    ),
                                    modifier = Modifier.padding(top = 8.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.FormatListNumbered,
                                        contentDescription = null,
                                        modifier = Modifier.size(16.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text("Show Detailed Format (with digits)")
                                }
                            }
                        } else {
                            Text(
                                text = "Enter a number to see it in words",
                                style = MaterialTheme.typography.bodyLarge,
                                color = NeonText.copy(alpha = 0.5f),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }

                    // Formatted number display
                    if (numberInput.isNotEmpty() && numberError.isNullOrEmpty()) {
                        val formattedNumber = NumberToWords.formatNumber(number.toDouble(), selectedLocale)
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .background(NeonGold.copy(alpha = 0.1f))
                                .padding(8.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Tag,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(16.dp)
                            )

                            Text(
                                text = "Formatted: $formattedNumber",
                                color = NeonText,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }

            // Fact insights card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = NeonGold.copy(alpha = 0.2f),
                        ambientColor = NeonGold.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Lightbulb,
                            contentDescription = null,
                            tint = NeonGold
                        )

                        Text(
                            text = "Number Insight",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonGold
                        )
                    }

                    // Animated text with enhanced styling
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(8.dp))
                            .background(
                                Brush.verticalGradient(
                                    colors = listOf(
                                        NeonGold.copy(alpha = 0.05f),
                                        NeonGold.copy(alpha = 0.1f)
                                    )
                                )
                            )
                            .padding(12.dp)
                    ) {
                        Text(
                            text = if (numberInput.isNotEmpty() && numberError.isNullOrEmpty())
                                culturalInsight
                            else
                                "Enter a number to see interesting facts and cultural insights.",
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText.copy(alpha = glowAnimation),
                            modifier = Modifier.fillMaxWidth()
                        )
                    }
                }
            }

            // Tips & Information card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 8.dp,
                        spotColor = NeonGold.copy(alpha = 0.2f),
                        ambientColor = NeonGold.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Tips & Information",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonGold
                    )

                    Divider(
                        color = NeonGold.copy(alpha = 0.3f),
                        modifier = Modifier.padding(vertical = 4.dp)
                    )

                    Row(
                        verticalAlignment = Alignment.Top,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = NeonGold.copy(alpha = 0.7f),
                            modifier = Modifier.size(16.dp)
                        )

                        Text(
                            text = "This tool supports numbers up to 999,999,999,999,999 (quadrillion).",
                            style = MaterialTheme.typography.bodySmall,
                            color = NeonText
                        )
                    }

                    Row(
                        verticalAlignment = Alignment.Top,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Lightbulb,
                            contentDescription = null,
                            tint = NeonGold.copy(alpha = 0.7f),
                            modifier = Modifier.size(16.dp)
                        )

                        Text(
                            text = "Try entering large numbers to see how they're structured in different languages. Some languages group digits differently!",
                            style = MaterialTheme.typography.bodySmall,
                            color = NeonText
                        )
                    }
                }
            }

            // Bottom spacer
            Spacer(modifier = Modifier.height(60.dp))
        }
    }
}
