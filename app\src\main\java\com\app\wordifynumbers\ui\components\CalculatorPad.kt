package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material.ripple.LocalRippleTheme
import androidx.compose.material.ripple.RippleAlpha
import androidx.compose.material.ripple.RippleTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.graphicsLayer
import android.content.Context
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.util.FeedbackUtil
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.FastOutSlowInEasing
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import androidx.compose.foundation.isSystemInDarkTheme

@Composable
fun CalculatorPad(
    onNumberClick: (String) -> Unit,
    onOperatorClick: (String) -> Unit,
    onDeleteClick: () -> Unit,
    onClearClick: () -> Unit,
    onEqualsClick: () -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonBlue
) {
    val context = LocalContext.current

    // Professional calculator pad inspired by Casio calculators
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp) // Tighter spacing for more professional look
    ) {
        // Standard calculator layout with optimized spacing
        val padRows = listOf(
            listOf("C", "⌫", "(", ")"),
            listOf("7", "8", "9", "÷"),
            listOf("4", "5", "6", "×"),
            listOf("1", "2", "3", "-"),
            listOf("0", ".", "%", "+")
        )

        // Render each row of buttons with consistent spacing
        padRows.forEach { row ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp) // Consistent spacing
            ) {
                row.forEach { symbol ->
                    when (symbol) {
                        "C" -> {
                            // Clear button with distinctive color
                            ModernCalculatorButton(
                                text = symbol,
                                type = ButtonType.FUNCTION,
                                accentColor = NeonRed,
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f)
                                    .padding(1.dp), // Add padding for consistent spacing
                                onClick = {
                                    onClearClick()
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                        "⌫" -> {
                            ModernCalculatorButton(
                                text = symbol,
                                type = ButtonType.FUNCTION,
                                accentColor = accentColor,
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f)
                                    .padding(1.dp),
                                onClick = {
                                    onDeleteClick()
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                        in listOf("÷", "×", "-", "+", "%") -> {
                            // Operator buttons with distinctive styling
                            ModernCalculatorButton(
                                text = symbol,
                                type = ButtonType.OPERATOR,
                                accentColor = accentColor,
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f)
                                    .padding(1.dp),
                                onClick = {
                                    // Map symbols to their correct operator representation
                                    val operatorToUse = when(symbol) {
                                        "÷" -> "/"
                                        "×" -> "*"
                                        else -> symbol
                                    }
                                    // Directly add the operator to the input
                                    onOperatorClick(operatorToUse)
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                        in listOf("(", ")") -> {
                            ModernCalculatorButton(
                                text = symbol,
                                type = ButtonType.FUNCTION,
                                accentColor = accentColor.copy(alpha = 0.8f),
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f)
                                    .padding(1.dp),
                                onClick = {
                                    onOperatorClick(symbol)
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                        else -> {
                            // Number buttons
                            ModernCalculatorButton(
                                text = symbol,
                                type = ButtonType.NUMBER,
                                accentColor = accentColor,
                                modifier = Modifier
                                    .weight(1f)
                                    .aspectRatio(1f)
                                    .padding(1.dp),
                                onClick = {
                                    onNumberClick(symbol)
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                    }
                }
            }
        }

        // Equals button row - larger and more prominent
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            horizontalArrangement = Arrangement.Center
        ) {
            ModernCalculatorButton(
                text = "=",
                type = ButtonType.EQUALS,
                accentColor = accentColor,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(70.dp) // Taller equals button
                    .padding(1.dp),
                onClick = {
                    onEqualsClick()
                    FeedbackUtil.buttonPress(context)
                }
            )
        }
    }
}

@Composable
fun ModernCalculatorButton(
    text: String,
    type: ButtonType,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonBlue,
    icon: ImageVector? = null
) {
    val interactionSource = remember { MutableInteractionSource() }
    var isPressed by remember { mutableStateOf(false) }

    // Button animations - more subtle for professional look
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f, // Less dramatic scale effect
        animationSpec = tween(
            durationMillis = 80, // Faster animation
            easing = FastOutSlowInEasing
        ),
        label = "buttonScale"
    )

    val glowAlpha by animateFloatAsState(
        targetValue = if (isPressed) 0.6f else 0f,
        animationSpec = tween(
            durationMillis = 80,
            easing = FastOutSlowInEasing
        ),
        label = "glowAlpha"
    )

    // Reset pressed state after animation
    if (isPressed) {
        LaunchedEffect(isPressed) {
            delay(80)
            isPressed = false
        }
    }

    // Button colors based on type - inspired by professional calculators
    val buttonColor = when (type) {
        ButtonType.NUMBER -> Color(0xFF2A2D3E) // Darker background for numbers
        ButtonType.OPERATOR -> Color(0xFF1E3A5F) // Blue-tinted background for operators
        ButtonType.FUNCTION -> Color(0xFF3D2E4F) // Purple-tinted background for functions
        ButtonType.EQUALS -> Color(0xFF1F4F3D) // Green-tinted background for equals
        ButtonType.SCIENTIFIC -> Color(0xFF3D3A2E) // Gold-tinted background for scientific
    }

    val contentColor = when (type) {
        ButtonType.NUMBER -> Color.White
        ButtonType.OPERATOR -> Color(0xFF64B5F6) // Light blue for operators
        ButtonType.FUNCTION -> Color(0xFFCE93D8) // Light purple for functions
        ButtonType.EQUALS -> Color(0xFF81C784) // Light green for equals
        ButtonType.SCIENTIFIC -> Color(0xFFFFD54F) // Gold for scientific
    }

    // Border color based on type
    val borderColor = when (type) {
        ButtonType.NUMBER -> Color(0xFF3F4259).copy(alpha = 0.5f)
        ButtonType.OPERATOR -> Color(0xFF2E5C8F).copy(alpha = 0.5f)
        ButtonType.FUNCTION -> Color(0xFF5D4E6F).copy(alpha = 0.5f)
        ButtonType.EQUALS -> Color(0xFF2F7F5D).copy(alpha = 0.5f)
        ButtonType.SCIENTIFIC -> Color(0xFF5D5A4E).copy(alpha = 0.5f)
    }

    // Button with glow effect
    Box(
        modifier = modifier
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
    ) {
        // Glow effect
        if (isPressed) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .shadow(
                        elevation = 6.dp,
                        shape = RoundedCornerShape(12.dp), // Slightly less rounded
                        spotColor = contentColor.copy(alpha = glowAlpha),
                        ambientColor = contentColor.copy(alpha = glowAlpha * 0.3f)
                    )
            )
        }

        // Button surface
        Surface(
            onClick = {
                isPressed = true
                onClick()
            },
            shape = RoundedCornerShape(12.dp), // Slightly less rounded for professional look
            color = buttonColor,
            border = BorderStroke(1.dp, SolidColor(borderColor)), // Always show border
            tonalElevation = 2.dp, // Subtle elevation
            shadowElevation = 2.dp, // Subtle shadow
            modifier = Modifier.fillMaxSize()
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                if (icon != null) {
                    Icon(
                        imageVector = icon,
                        contentDescription = text,
                        tint = contentColor,
                        modifier = Modifier.size(24.dp) // Consistent icon size
                    )
                } else {
                    Text(
                        text = text,
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = if (type == ButtonType.EQUALS) FontWeight.Bold else FontWeight.Medium,
                            fontSize = when (type) {
                                ButtonType.EQUALS -> 24.sp
                                ButtonType.OPERATOR -> 22.sp
                                else -> 20.sp
                            }
                        ),
                        color = contentColor
                    )
                }
            }
        }
    }
}

@Composable
fun ScientificPad(
    onFunctionClick: (String) -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonCyan
) {
    val context = LocalContext.current
    var showErrorMessage by remember { mutableStateOf<String?>(null) }

    // Modern scientific pad with improved layout and visual appeal
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Organized scientific functions in categories
        val trigFunctions = listOf("sin", "cos", "tan", "asin", "acos", "atan")
        val logFunctions = listOf("log", "ln", "eˣ", "10ˣ")
        val mathFunctions = listOf("√", "x²", "xʸ", "x!")
        val constants = listOf("π", "e", "rand")
        val miscFunctions = listOf("mod", "abs", "floor", "ceil")

        // Category label and function row
        SciCategoryRow("Trigonometry", trigFunctions, onFunctionClick, context, accentColor)
        SciCategoryRow("Logarithmic", logFunctions, onFunctionClick, context, accentColor)
        SciCategoryRow("Math", mathFunctions, onFunctionClick, context, accentColor)
        SciCategoryRow("Constants", constants, onFunctionClick, context, accentColor)
        SciCategoryRow("Misc", miscFunctions, onFunctionClick, context, accentColor)

        // Error message with animation
        AnimatedVisibility(
            visible = showErrorMessage != null,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Surface(
                color = NeonRed.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp),
                border = BorderStroke(1.dp, SolidColor(NeonRed.copy(alpha = 0.3f))),
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = NeonRed,
                        modifier = Modifier.size(16.dp)
                    )
                    Text(
                        text = showErrorMessage ?: "Error in calculation",
                        color = NeonRed,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // Dismiss error after delay
        LaunchedEffect(showErrorMessage) {
            if (showErrorMessage != null) {
                delay(3000)
                showErrorMessage = null
            }
        }
    }
}

@Composable
private fun SciCategoryRow(
    categoryName: String,
    functions: List<String>,
    onFunctionClick: (String) -> Unit,
    context: Context,
    accentColor: Color
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        // Category label
        Text(
            text = categoryName,
            style = MaterialTheme.typography.labelMedium,
            color = accentColor,
            modifier = Modifier.padding(start = 4.dp)
        )

        // Functions row
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState()),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            functions.forEach { symbol ->
                ModernCalculatorButton(
                    text = symbol,
                    type = ButtonType.SCIENTIFIC,
                    accentColor = accentColor,
                    onClick = {
                        try {
                            onFunctionClick(symbol)
                            FeedbackUtil.buttonPress(context)
                        } catch (e: Exception) {
                            // Error handling is done in parent component
                            FeedbackUtil.errorFeedback(context)
                        }
                    },
                    modifier = Modifier.size(56.dp)
                )
            }
        }
    }
}

private val scientificFunctions = listOf(
    ScientificFunction("sin", "Sine"),
    ScientificFunction("cos", "Cosine"),
    ScientificFunction("tan", "Tangent"),
    ScientificFunction("asin", "Arcsine"),
    ScientificFunction("acos", "Arccosine"),
    ScientificFunction("atan", "Arctangent"),
    ScientificFunction("log", "Log base 10"),
    ScientificFunction("ln", "Natural log"),
    ScientificFunction("√", "Square root"),
    ScientificFunction("x²", "Square"),
    ScientificFunction("xʸ", "Power"),
    ScientificFunction("x!", "Factorial"),
    ScientificFunction("π", "Pi constant"),
    ScientificFunction("e", "Euler's number"),
    ScientificFunction("rand", "Random number")
)

@Composable
fun CalculatorButton(
    text: String,
    type: ButtonType,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    icon: ImageVector? = null
) {
    val interactionSource = remember { MutableInteractionSource() }
    var isPressed by remember { mutableStateOf(false) }
    val glowScale by animateFloatAsState(
        targetValue = if (isPressed) 1.2f else 1f,
        animationSpec = tween(
            durationMillis = 100,
            easing = FastOutSlowInEasing
        )
    )

    CompositionLocalProvider(LocalRippleTheme provides NeonRippleTheme) {
        // Reset pressed state after animation
        if (isPressed) {
            LaunchedEffect(isPressed) {
                delay(100)
                isPressed = false
            }
        }
        Box(
            modifier = modifier
                .clip(CircleShape)
                .clickable(
                    interactionSource = interactionSource,
                    indication = null,
                    onClick = {
                        isPressed = true
                        onClick()
                    }
                )
        ) {
            // Glow effect background
            if (isPressed) {
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .graphicsLayer {
                            this.scaleX = glowScale
                            this.scaleY = glowScale
                        }
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    when (type) {
                                        ButtonType.EQUALS -> NeonGlow
                                        ButtonType.OPERATOR -> NeonPurple
                                        ButtonType.SCIENTIFIC -> NeonCyan
                                        ButtonType.NUMBER, ButtonType.FUNCTION -> NeonBlue
                                    }.copy(alpha = 0.2f),
                                    Color.Transparent
                                )
                            )
                        )
                )
            }

            // Button content
            Surface(
                modifier = Modifier
                    .matchParentSize()
                    .graphicsLayer {
                        shadowElevation = if (type == ButtonType.EQUALS) 14f else 8f
                        shape = CircleShape
                        clip = true
                    },
                shape = CircleShape,
                tonalElevation = if (type == ButtonType.EQUALS) 12.dp else 6.dp,
                color = when (type) {
                    ButtonType.NUMBER -> NeonCard.copy(alpha = 0.95f)
                    ButtonType.OPERATOR -> NeonPurple.copy(alpha = 0.18f)
                    ButtonType.FUNCTION -> NeonBlue.copy(alpha = 0.13f)
                    ButtonType.EQUALS -> NeonGlow.copy(alpha = 0.25f)
                    ButtonType.SCIENTIFIC -> NeonCyan.copy(alpha = 0.17f)
                }
            ) {
                Box(
                    modifier = Modifier
                        .aspectRatio(1f)
                        .padding(4.dp),
                    contentAlignment = Alignment.Center
                ) {
                    if (icon != null) {
                        Icon(
                            imageVector = icon,
                            contentDescription = text,
                            tint = when (type) {
                                ButtonType.EQUALS -> NeonGlow
                                ButtonType.OPERATOR -> NeonPurple
                                ButtonType.SCIENTIFIC -> NeonCyan
                                ButtonType.NUMBER, ButtonType.FUNCTION -> NeonText
                            }
                        )
                    } else {
                        Text(
                            text = text,
                            style = MaterialTheme.typography.titleLarge.copy(
                                fontWeight = if (type == ButtonType.EQUALS) FontWeight.Bold else FontWeight.Medium,
                                fontSize = if (type == ButtonType.EQUALS) MaterialTheme.typography.titleLarge.fontSize * 1.2 else MaterialTheme.typography.titleLarge.fontSize
                            ),
                            color = when (type) {
                                ButtonType.EQUALS -> NeonGlow
                                ButtonType.OPERATOR -> NeonPurple
                                ButtonType.SCIENTIFIC -> NeonCyan
                                ButtonType.NUMBER, ButtonType.FUNCTION -> NeonText
                            },
                            maxLines = 1
                        )
                    }
                }
            }
        }
    }
}

data class ScientificFunction(val symbol: String, val description: String)

private object NeonRippleTheme : RippleTheme {
    @Composable
    override fun defaultColor() = NeonGlow.copy(alpha = 0.5f)

    @Composable
    override fun rippleAlpha(): RippleAlpha = RippleTheme.defaultRippleAlpha(
        NeonGlow.copy(alpha = 0.5f),
        lightTheme = !isSystemInDarkTheme()
    )
}
