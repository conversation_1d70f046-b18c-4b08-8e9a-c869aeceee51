package com.app.wordifynumbers.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*

import com.app.wordifynumbers.util.FeedbackUtil
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

@Composable
fun AgeCalculatorScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current

    // Let MainScreen handle back navigation automatically

    // Animated neon glass background
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(NeonGlow.copy(alpha = 0.10f), NeonCard),
                    center = Offset(0.5f, 0.5f),
                    radius = 900f
                )
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // Top Bar
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp, start = 16.dp, end = 16.dp, bottom = 6.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Age Calculator",
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.ExtraBold,
                        color = NeonGlow,
                        letterSpacing = 1.5.sp
                    )
                )
                IconButton(onClick = { /* TODO: Add help or settings */ }) {
                    Icon(Icons.Default.Info, contentDescription = "Info", tint = NeonGlow)
                }
            }
            // Main content
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp)
                    .background(NeonCard.copy(alpha = 0.98f), RoundedCornerShape(22.dp))
                    .shadow(14.dp, RoundedCornerShape(22.dp)),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(18.dp)
            ) {
                var dob by remember { mutableStateOf("") }
                var targetDate by remember { mutableStateOf(
                    SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                ) }
                var result by remember { mutableStateOf("") }
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                // Inputs
                NeonCard(modifier = Modifier.fillMaxWidth()) {
                    Column(
                        modifier = Modifier.padding(18.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        NeonTextField(
                            value = dob,
                            onValueChange = { dob = it },
                            label = { Text("Date of Birth (yyyy-MM-dd)") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true
                        )
                        NeonTextField(
                            value = targetDate,
                            onValueChange = { targetDate = it },
                            label = { Text("Calculate Age At (yyyy-MM-dd)") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true
                        )
                    }
                }
                // Action row
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    NeonButton(
                        onClick = {
                            try {
                                val birthDate = dateFormat.parse(dob)
                                val calcDate = dateFormat.parse(targetDate)
                                
                                val birthCal = Calendar.getInstance().apply { time = birthDate }
                                val calcCal = Calendar.getInstance().apply { time = calcDate }
                                
                                var years = calcCal.get(Calendar.YEAR) - birthCal.get(Calendar.YEAR)
                                var months = calcCal.get(Calendar.MONTH) - birthCal.get(Calendar.MONTH)
                                var days = calcCal.get(Calendar.DAY_OF_MONTH) - birthCal.get(Calendar.DAY_OF_MONTH)
                                
                                if (days < 0) {
                                    months--
                                    val tempCal = Calendar.getInstance().apply { time = birthDate }
                                    tempCal.add(Calendar.MONTH, years * 12 + months)
                                    days = ((calcDate.time - tempCal.time.time) / (1000 * 60 * 60 * 24)).toInt()
                                }
                                if (months < 0) {
                                    years--
                                    months += 12
                                }
                                
                                val totalDays = (calcDate.time - birthDate.time) / (1000 * 60 * 60 * 24)
                                val weeks = totalDays / 7
                                result = buildString {
                                    append("Age: $years years, $months months, $days days\n\n")
                                    append("Or approximately:\n")
                                    append("$months months\n")
                                    append("$weeks weeks\n")
                                    append("$days days\n")
                                    val nextBirthday = Calendar.getInstance().apply {
                                        time = birthDate
                                        set(Calendar.YEAR, calcCal.get(Calendar.YEAR))
                                        if (before(calcCal)) {
                                            add(Calendar.YEAR, 1)
                                        }
                                    }
                                    val daysUntilBirthday = (nextBirthday.time.time - calcDate.time) / (1000 * 60 * 60 * 24)
                                    append("\nDays until next birthday: $daysUntilBirthday")
                                }
                            } catch (e: Exception) {
                                result = "Please enter valid dates (yyyy-MM-dd)"
                            }
                        },
                        modifier = Modifier.weight(1f)
                    ) { Text("Calculate") }
                    NeonButton(
                        onClick = {
                            targetDate = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
                            result = ""
                        },
                        modifier = Modifier.weight(1f)
                    ) { Text("Set Today") }
                }
                // Results
                AnimatedVisibility(result.isNotEmpty()) {
                    NeonCard(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp)
                    ) {
                        Text(
                            text = result,
                            style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.SemiBold),
                            color = if (result.startsWith("Please")) NeonRed else NeonText,
                            modifier = Modifier.padding(18.dp)
                        )
                    }
                }
            }
            // Footer
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Made with 499 by Wordify Numbers",
                    style = MaterialTheme.typography.labelLarge.copy(
                        color = NeonGlow.copy(alpha = 0.7f),
                        fontWeight = FontWeight.Medium
                    )
                )
            }
        }
    }
}
