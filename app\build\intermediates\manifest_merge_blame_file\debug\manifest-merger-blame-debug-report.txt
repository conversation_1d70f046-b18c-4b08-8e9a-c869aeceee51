1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.wordifynumbers.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!-- Essential permissions -->
11    <uses-permission android:name="android.permission.VIBRATE" />
11-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:5:5-65
11-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:5:22-63
12
13    <!-- Optional permissions for enhanced features -->
14    <uses-permission
14-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:8:5-9:51
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:9:22-48
17    <uses-permission
17-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:10:5-11:51
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:10:22-77
19        android:maxSdkVersion="32" />
19-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:11:22-48
20
21    <!-- Biometric authentication for secure features -->
22    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
22-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:14:5-72
22-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:14:22-69
23    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
23-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:15:5-74
23-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:15:22-71
24
25    <!-- Network state for connectivity checks -->
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:18:5-79
26-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:18:22-76
27
28    <permission
28-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
29        android:name="com.app.wordifynumbers.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.app.wordifynumbers.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
33
34    <application
34-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:20:5-44:19
35        android:allowBackup="true"
35-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:21:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:22:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="true"
40        android:fullBackupContent="@xml/backup_rules"
40-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:23:9-54
41        android:icon="@mipmap/ic_launcher"
41-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:24:9-43
42        android:label="@string/app_name"
42-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:25:9-41
43        android:networkSecurityConfig="@xml/network_security_config"
43-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:29:9-69
44        android:preserveLegacyExternalStorage="false"
44-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:31:9-54
45        android:requestLegacyExternalStorage="false"
45-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:30:9-53
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:26:9-54
47        android:supportsRtl="true"
47-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:27:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.WordifyNumbers" >
49-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:28:9-52
50        <activity
50-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:33:9-43:20
51            android:name="com.app.wordifynumbers.MainActivity"
51-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:34:13-41
52            android:exported="true"
52-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:35:13-36
53            android:label="@string/app_name"
53-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:36:13-45
54            android:theme="@style/Theme.WordifyNumbers" >
54-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:37:13-56
55            <intent-filter>
55-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:38:13-42:29
56                <action android:name="android.intent.action.MAIN" />
56-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:39:17-69
56-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:39:25-66
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:41:17-77
58-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:41:27-74
59            </intent-filter>
60        </activity>
61        <activity
61-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\d5c0c7c2641eecb87689e45c9eb3a83c\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
62            android:name="androidx.compose.ui.tooling.PreviewActivity"
62-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\d5c0c7c2641eecb87689e45c9eb3a83c\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
63            android:exported="true" />
63-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\d5c0c7c2641eecb87689e45c9eb3a83c\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
64        <activity
64-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ff02f6e10a46c673194821c2c5cfeff6\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
65            android:name="androidx.activity.ComponentActivity"
65-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ff02f6e10a46c673194821c2c5cfeff6\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
66            android:exported="true" />
66-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ff02f6e10a46c673194821c2c5cfeff6\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
67
68        <provider
68-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
69            android:name="androidx.startup.InitializationProvider"
69-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
70            android:authorities="com.app.wordifynumbers.debug.androidx-startup"
70-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
71            android:exported="false" >
71-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
72            <meta-data
72-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.emoji2.text.EmojiCompatInitializer"
73-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
74                android:value="androidx.startup" />
74-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
75            <meta-data
75-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\a72a25de53dcd6e3c38d92fb8a587888\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
76                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
76-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\a72a25de53dcd6e3c38d92fb8a587888\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
77                android:value="androidx.startup" />
77-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\a72a25de53dcd6e3c38d92fb8a587888\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
78            <meta-data
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
79                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
80                android:value="androidx.startup" />
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
81        </provider>
82
83        <service
83-->[androidx.room:room-runtime:2.6.1] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ead146df563433226bf05ce5489b8fa5\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
84            android:name="androidx.room.MultiInstanceInvalidationService"
84-->[androidx.room:room-runtime:2.6.1] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ead146df563433226bf05ce5489b8fa5\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
85            android:directBootAware="true"
85-->[androidx.room:room-runtime:2.6.1] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ead146df563433226bf05ce5489b8fa5\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
86            android:exported="false" />
86-->[androidx.room:room-runtime:2.6.1] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ead146df563433226bf05ce5489b8fa5\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
87
88        <receiver
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
89            android:name="androidx.profileinstaller.ProfileInstallReceiver"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
90            android:directBootAware="false"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
91            android:enabled="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
92            android:exported="true"
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
93            android:permission="android.permission.DUMP" >
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
95                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
98                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
101                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
104                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
105            </intent-filter>
106        </receiver>
107    </application>
108
109</manifest>
