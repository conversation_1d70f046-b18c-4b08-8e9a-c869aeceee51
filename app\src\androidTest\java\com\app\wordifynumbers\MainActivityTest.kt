package com.app.wordifynumbers

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.Assert.*
import org.junit.Rule

/**
 * Instrumented tests for MainActivity and main app functionality
 * Testing UI components and user interactions for Google Play compliance
 */
@RunWith(AndroidJUnit4::class)
class MainActivityTest {

    @get:Rule
    val composeTestRule = createAndroidComposeRule<MainActivity>()

    @Test
    fun useAppContext() {
        // Context of the app under test
        val appContext = InstrumentationRegistry.getInstrumentation().targetContext
        assertEquals("com.app.wordifynumbers", appContext.packageName)
    }

    @Test
    fun testAppLaunch() {
        // Test that the app launches successfully
        composeTestRule.onNodeWithText("Wordify Numbers").assertExists()
    }

    @Test
    fun testBottomNavigation() {
        // Test that all bottom navigation tabs are present
        composeTestRule.onNodeWithContentDescription("Words tab").assertExists()
        composeTestRule.onNodeWithContentDescription("Finance tab").assertExists()
        composeTestRule.onNodeWithContentDescription("Calculator tab").assertExists()
        composeTestRule.onNodeWithContentDescription("Large Numbers tab").assertExists()
    }

    @Test
    fun testNavigationBetweenTabs() {
        // Test navigation to Finance tab
        composeTestRule.onNodeWithContentDescription("Finance tab").performClick()
        composeTestRule.waitForIdle()
        
        // Test navigation to Calculator tab
        composeTestRule.onNodeWithContentDescription("Calculator tab").performClick()
        composeTestRule.waitForIdle()
        
        // Test navigation to Large Numbers tab
        composeTestRule.onNodeWithContentDescription("Large Numbers tab").performClick()
        composeTestRule.waitForIdle()
        
        // Test navigation back to Words tab
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
    }

    @Test
    fun testNumberToWordsInput() {
        // Ensure we're on the Words tab
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
        
        // Find and interact with number input field
        composeTestRule.onNodeWithContentDescription("Number input field")
            .assertExists()
            .performTextInput("123")
        
        composeTestRule.waitForIdle()
        
        // Check that the conversion appears
        composeTestRule.onNodeWithText("one hundred twenty-three", substring = true)
            .assertExists()
    }

    @Test
    fun testCalculatorNavigation() {
        // Navigate to Calculator tab
        composeTestRule.onNodeWithContentDescription("Calculator tab").performClick()
        composeTestRule.waitForIdle()
        
        // Test that calculator selection screen is shown
        composeTestRule.onNodeWithText("Calculator", substring = true).assertExists()
        
        // Test clicking on a calculator (e.g., Basic Calculator)
        composeTestRule.onNodeWithText("Basic Calculator", substring = true)
            .assertExists()
            .performClick()
        
        composeTestRule.waitForIdle()
        
        // Test that calculator screen is shown
        composeTestRule.onNodeWithContentDescription("Calculator button", substring = true)
            .assertExists()
    }

    @Test
    fun testBackNavigation() {
        // Navigate to Calculator tab
        composeTestRule.onNodeWithContentDescription("Calculator tab").performClick()
        composeTestRule.waitForIdle()
        
        // Click on a calculator
        composeTestRule.onNodeWithText("Basic Calculator", substring = true)
            .performClick()
        composeTestRule.waitForIdle()
        
        // Test back navigation using system back button
        composeTestRule.activityRule.scenario.onActivity { activity ->
            activity.onBackPressed()
        }
        composeTestRule.waitForIdle()
        
        // Should be back to calculator selection
        composeTestRule.onNodeWithText("Calculator", substring = true).assertExists()
    }

    @Test
    fun testAccessibilityFeatures() {
        // Test that content descriptions are present for accessibility
        composeTestRule.onAllNodesWithContentDescription("", substring = false)
            .assertCountEquals(0) // No elements should have empty content descriptions
        
        // Test that important UI elements have proper semantics
        composeTestRule.onNodeWithContentDescription("Words tab")
            .assertHasClickAction()
        
        composeTestRule.onNodeWithContentDescription("Calculator tab")
            .assertHasClickAction()
    }

    @Test
    fun testPerformanceBasics() {
        val startTime = System.currentTimeMillis()
        
        // Perform several navigation actions
        composeTestRule.onNodeWithContentDescription("Finance tab").performClick()
        composeTestRule.waitForIdle()
        
        composeTestRule.onNodeWithContentDescription("Calculator tab").performClick()
        composeTestRule.waitForIdle()
        
        composeTestRule.onNodeWithContentDescription("Large Numbers tab").performClick()
        composeTestRule.waitForIdle()
        
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
        
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        // Navigation should be responsive (less than 2 seconds for all actions)
        assertTrue("Navigation took too long: ${duration}ms", duration < 2000)
    }

    @Test
    fun testInputValidation() {
        // Navigate to Words tab
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
        
        // Test invalid input handling
        composeTestRule.onNodeWithContentDescription("Number input field")
            .performTextClearance()
            .performTextInput("invalid")
        
        composeTestRule.waitForIdle()
        
        // Should handle invalid input gracefully (no crash)
        composeTestRule.onRoot().assertExists()
    }

    @Test
    fun testLargeNumberInput() {
        // Navigate to Words tab
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
        
        // Test large number input
        composeTestRule.onNodeWithContentDescription("Number input field")
            .performTextClearance()
            .performTextInput("1234567890")
        
        composeTestRule.waitForIdle()
        
        // Should handle large numbers without crashing
        composeTestRule.onRoot().assertExists()
    }

    @Test
    fun testMemoryUsage() {
        val runtime = Runtime.getRuntime()
        val initialMemory = runtime.totalMemory() - runtime.freeMemory()
        
        // Perform memory-intensive operations
        repeat(10) {
            composeTestRule.onNodeWithContentDescription("Calculator tab").performClick()
            composeTestRule.waitForIdle()
            
            composeTestRule.onNodeWithContentDescription("Words tab").performClick()
            composeTestRule.waitForIdle()
        }
        
        val finalMemory = runtime.totalMemory() - runtime.freeMemory()
        val memoryIncrease = finalMemory - initialMemory
        
        // Memory increase should be reasonable (less than 50MB)
        assertTrue("Memory usage increased too much: ${memoryIncrease / 1024 / 1024}MB", 
                  memoryIncrease < 50 * 1024 * 1024)
    }

    @Test
    fun testScreenRotation() {
        // Test that the app handles screen rotation properly
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
        
        // Input some text
        composeTestRule.onNodeWithContentDescription("Number input field")
            .performTextInput("456")
        composeTestRule.waitForIdle()
        
        // Simulate rotation by recreating activity
        composeTestRule.activityRule.scenario.recreate()
        composeTestRule.waitForIdle()
        
        // App should still be functional after rotation
        composeTestRule.onNodeWithContentDescription("Words tab").assertExists()
    }

    @Test
    fun testMultipleLanguageSupport() {
        // Navigate to Words tab
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
        
        // Test that language selection is available
        composeTestRule.onNodeWithContentDescription("Select language", substring = true)
            .assertExists()
    }

    @Test
    fun testErrorHandling() {
        // Test that the app handles errors gracefully
        composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        composeTestRule.waitForIdle()
        
        // Try various edge cases
        val edgeCases = listOf("", "0", "-1", "999999999999999999")
        
        edgeCases.forEach { input ->
            composeTestRule.onNodeWithContentDescription("Number input field")
                .performTextClearance()
                .performTextInput(input)
            composeTestRule.waitForIdle()
            
            // App should not crash
            composeTestRule.onRoot().assertExists()
        }
    }

    @Test
    fun testUIResponsiveness() {
        val startTime = System.currentTimeMillis()
        
        // Test rapid interactions
        repeat(5) {
            composeTestRule.onNodeWithContentDescription("Calculator tab").performClick()
            composeTestRule.onNodeWithContentDescription("Words tab").performClick()
        }
        
        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime
        
        // Should handle rapid interactions smoothly
        assertTrue("UI not responsive enough: ${duration}ms", duration < 3000)
        
        // App should still be functional
        composeTestRule.onNodeWithContentDescription("Words tab").assertExists()
    }
}
