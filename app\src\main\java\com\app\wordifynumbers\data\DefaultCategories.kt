package com.app.wordifynumbers.data

/**
 * Default categories for the Finance Notepad
 */
object DefaultCategories {
    val INCOME = listOf(
        "Salary",
        "Bonus",
        "Freelance",
        "Investment",
        "Gift",
        "Refund",
        "Rental Income",
        "Side Hustle",
        "Other Income"
    )

    val EXPENSE = listOf(
        "Food",
        "Groceries",
        "Dining Out",
        "Housing",
        "Rent",
        "Mortgage",
        "Utilities",
        "Transportation",
        "Fuel",
        "Public Transit",
        "Healthcare",
        "Insurance",
        "Entertainment",
        "Shopping",
        "Education",
        "Travel",
        "Personal Care",
        "Subscriptions",
        "Debt Payment",
        "Charity",
        "Other Expense"
    )

    val TRANSFER = listOf(
        "Bank Transfer",
        "Credit Card Payment",
        "Loan Payment",
        "Investment Transfer",
        "Savings Transfer",
        "Other Transfer"
    )

    /**
     * Get categories for a specific entry type
     */
    fun getCategoriesForType(type: EntryType): List<String> {
        return when (type) {
            EntryType.INCOME -> INCOME
            EntryType.EXPENSE -> EXPENSE
            EntryType.TRANSFER -> TRANSFER
        }
    }
}
