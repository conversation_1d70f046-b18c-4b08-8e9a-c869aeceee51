package com.app.wordifynumbers.ui.components

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import java.time.LocalDate

@Composable
fun DatePickerField(
    label: String,
    date: LocalDate,
    onDateChange: (LocalDate) -> Unit,
    modifier: Modifier = Modifier
) {
    // Date picker implementation
    // Note: Actual date picker dialog implementation needed
    OutlinedTextField(
        value = date.toString(),
        onValueChange = { },
        label = { Text(label) },
        modifier = modifier,
        readOnly = true
    )
}
