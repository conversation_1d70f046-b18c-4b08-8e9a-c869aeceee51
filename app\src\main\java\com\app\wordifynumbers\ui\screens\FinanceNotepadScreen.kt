package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.data.DefaultCategories
import com.app.wordifynumbers.data.EntryType
import com.app.wordifynumbers.data.SimpleFinanceEntry
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.FeedbackUtil
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FinanceNotepadScreen(
    modifier: Modifier = Modifier,
    onNavigateToCalculator: (String) -> Unit = {},
    onNavigateToWords: () -> Unit = {},
    onNavigateToLargeNumbers: () -> Unit = {}
) {
    val context = LocalContext.current
    
    // State management
    var selectedEntryType by remember { mutableStateOf(EntryType.EXPENSE) }
    var showAddEntryDialog by remember { mutableStateOf(false) }
    var showCategoryManagement by remember { mutableStateOf(false) }
    var entries by remember { mutableStateOf<List<SimpleFinanceEntry>>(emptyList()) }
    var searchQuery by remember { mutableStateOf("") }
    
    // Sample data for demonstration
    LaunchedEffect(Unit) {
        entries = listOf(
            SimpleFinanceEntry(
                amount = 50.0,
                type = EntryType.EXPENSE,
                category = "Food",
                note = "Lunch at restaurant",
                date = System.currentTimeMillis() - 86400000
            ),
            SimpleFinanceEntry(
                amount = 3000.0,
                type = EntryType.INCOME,
                category = "Salary",
                note = "Monthly salary",
                date = System.currentTimeMillis() - 172800000
            ),
            SimpleFinanceEntry(
                amount = 120.0,
                type = EntryType.EXPENSE,
                category = "Transportation",
                note = "Gas for car",
                date = System.currentTimeMillis() - 259200000
            )
        )
    }
    
    // Animated background
    val infiniteTransition = rememberInfiniteTransition(label = "backgroundEffects")
    val primaryPulse by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(6000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "primaryPulse"
    )

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(
                        NeonDeepBlue.copy(alpha = 0.95f),
                        NeonBackground.copy(alpha = 0.98f),
                        Color.Black.copy(alpha = 0.99f)
                    ),
                    center = Offset(0.5f, 0.3f),
                    radius = 1400f * primaryPulse
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Enhanced header
            FinanceHeader(
                onNavigateToCalculator = onNavigateToCalculator,
                onNavigateToWords = onNavigateToWords,
                onNavigateToLargeNumbers = onNavigateToLargeNumbers,
                onCategoryManagement = { showCategoryManagement = true }
            )
            
            // Entry type selector
            EntryTypeSelector(
                selectedType = selectedEntryType,
                onTypeSelected = { selectedEntryType = it }
            )
            
            // Quick stats
            QuickStatsCard(entries = entries)
            
            // Add entry button
            FloatingActionButton(
                onClick = { 
                    showAddEntryDialog = true
                    FeedbackUtil.buttonPress(context)
                },
                modifier = Modifier.align(Alignment.End),
                containerColor = NeonGreen,
                contentColor = Color.Black
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Add Entry"
                )
            }
            
            // Entries list
            EntriesList(
                entries = entries.filter { 
                    if (selectedEntryType == EntryType.INCOME) it.type == EntryType.INCOME
                    else it.type != EntryType.INCOME
                },
                modifier = Modifier.weight(1f)
            )
        }
    }
    
    // Add entry dialog
    if (showAddEntryDialog) {
        AddEntryDialog(
            onDismiss = { showAddEntryDialog = false },
            onAddEntry = { entry ->
                entries = entries + entry
                showAddEntryDialog = false
                FeedbackUtil.buttonPress(context)
            }
        )
    }
    
    // Category management
    if (showCategoryManagement) {
        CategoryManagementScreen(
            onBackClick = { showCategoryManagement = false }
        )
    }
}

@Composable
private fun FinanceHeader(
    onNavigateToCalculator: (String) -> Unit,
    onNavigateToWords: () -> Unit,
    onNavigateToLargeNumbers: () -> Unit,
    onCategoryManagement: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .shadow(
                elevation = 16.dp,
                spotColor = NeonGlow.copy(alpha = 0.3f),
                ambientColor = NeonGlow.copy(alpha = 0.1f),
                shape = RoundedCornerShape(20.dp)
            ),
        shape = RoundedCornerShape(20.dp),
        colors = CardDefaults.cardColors(
            containerColor = NeonCard.copy(alpha = 0.15f)
        ),
        border = BorderStroke(1.dp, NeonGlow.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Finance Notepad",
                    style = MaterialTheme.typography.headlineLarge.copy(
                        fontWeight = FontWeight.ExtraBold,
                        letterSpacing = 1.2.sp,
                        brush = Brush.linearGradient(
                            colors = listOf(
                                NeonGlow,
                                NeonGreen.copy(alpha = 0.8f)
                            )
                        )
                    )
                )
                Text(
                    text = "Track your income and expenses",
                    style = MaterialTheme.typography.bodyMedium.copy(
                        color = NeonText.copy(alpha = 0.7f),
                        letterSpacing = 0.3.sp
                    )
                )
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                EnhancedNavigationButton(
                    icon = Icons.Default.Category,
                    contentDescription = "Categories",
                    accentColor = NeonOrange,
                    onClick = onCategoryManagement
                )
                
                EnhancedNavigationButton(
                    icon = Icons.Default.Calculate,
                    contentDescription = "Calculator",
                    accentColor = NeonBlue,
                    onClick = { onNavigateToCalculator("basic") }
                )
            }
        }
    }
}

@Composable
private fun EntryTypeSelector(
    selectedType: EntryType,
    onTypeSelected: (EntryType) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        EntryType.values().forEach { type ->
            val isSelected = selectedType == type
            val color = when (type) {
                EntryType.INCOME -> NeonGreen
                EntryType.EXPENSE -> NeonRed
                EntryType.TRANSFER -> NeonBlue
            }
            
            Surface(
                onClick = { onTypeSelected(type) },
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(12.dp),
                color = if (isSelected) color.copy(alpha = 0.2f) else Color.Transparent,
                border = BorderStroke(
                    width = if (isSelected) 2.dp else 1.dp,
                    color = if (isSelected) color else NeonText.copy(alpha = 0.3f)
                )
            ) {
                Text(
                    text = type.name.lowercase().replaceFirstChar { it.uppercase() },
                    modifier = Modifier.padding(16.dp),
                    style = MaterialTheme.typography.titleMedium,
                    color = if (isSelected) color else NeonText.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
private fun QuickStatsCard(entries: List<SimpleFinanceEntry>) {
    val totalIncome = entries.filter { it.type == EntryType.INCOME }.sumOf { it.amount }
    val totalExpense = entries.filter { it.type == EntryType.EXPENSE }.sumOf { it.amount }
    val balance = totalIncome - totalExpense
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = NeonCard.copy(alpha = 0.1f)),
        shape = RoundedCornerShape(16.dp),
        border = BorderStroke(1.dp, NeonGlow.copy(alpha = 0.2f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatItem("Income", totalIncome, NeonGreen)
            StatItem("Expense", totalExpense, NeonRed)
            StatItem("Balance", balance, if (balance >= 0) NeonGreen else NeonRed)
        }
    }
}

@Composable
private fun StatItem(label: String, amount: Double, color: Color) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = NeonText.copy(alpha = 0.7f)
        )
        Text(
            text = NumberFormat.getCurrencyInstance().format(amount),
            style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
            color = color
        )
    }
}

@Composable
private fun EntriesList(
    entries: List<SimpleFinanceEntry>,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(entries) { entry ->
            EntryItem(entry = entry)
        }
    }
}

@Composable
private fun EntryItem(entry: SimpleFinanceEntry) {
    val color = when (entry.type) {
        EntryType.INCOME -> NeonGreen
        EntryType.EXPENSE -> NeonRed
        EntryType.TRANSFER -> NeonBlue
    }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = NeonCard.copy(alpha = 0.1f)),
        shape = RoundedCornerShape(12.dp),
        border = BorderStroke(1.dp, color.copy(alpha = 0.3f))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = entry.category,
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
                    color = color
                )
                if (entry.note.isNotEmpty()) {
                    Text(
                        text = entry.note,
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText.copy(alpha = 0.7f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                Text(
                    text = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault()).format(Date(entry.date)),
                    style = MaterialTheme.typography.bodySmall,
                    color = NeonText.copy(alpha = 0.5f)
                )
            }
            
            Text(
                text = "${if (entry.type == EntryType.EXPENSE) "-" else "+"}${NumberFormat.getCurrencyInstance().format(entry.amount)}",
                style = MaterialTheme.typography.titleLarge.copy(fontWeight = FontWeight.Bold),
                color = color
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AddEntryDialog(
    onDismiss: () -> Unit,
    onAddEntry: (SimpleFinanceEntry) -> Unit
) {
    var amount by remember { mutableStateOf("") }
    var selectedType by remember { mutableStateOf(EntryType.EXPENSE) }
    var selectedCategory by remember { mutableStateOf("") }
    var note by remember { mutableStateOf("") }

    val categories = when (selectedType) {
        EntryType.INCOME -> DefaultCategories.INCOME
        EntryType.EXPENSE -> DefaultCategories.EXPENSE
        EntryType.TRANSFER -> DefaultCategories.TRANSFER
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Add New Entry",
                style = MaterialTheme.typography.headlineSmall,
                color = NeonGlow
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Amount input
                OutlinedTextField(
                    value = amount,
                    onValueChange = { amount = it },
                    label = { Text("Amount") },
                    modifier = Modifier.fillMaxWidth(),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = NeonGlow,
                        focusedLabelColor = NeonGlow,
                        cursorColor = NeonGlow
                    )
                )

                // Type selector
                Text(
                    text = "Type",
                    style = MaterialTheme.typography.labelLarge,
                    color = NeonText
                )
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    EntryType.values().forEach { type ->
                        FilterChip(
                            onClick = { selectedType = type },
                            label = { Text(type.name.lowercase().replaceFirstChar { it.uppercase() }) },
                            selected = selectedType == type,
                            colors = FilterChipDefaults.filterChipColors(
                                selectedContainerColor = when (type) {
                                    EntryType.INCOME -> NeonGreen.copy(alpha = 0.3f)
                                    EntryType.EXPENSE -> NeonRed.copy(alpha = 0.3f)
                                    EntryType.TRANSFER -> NeonBlue.copy(alpha = 0.3f)
                                }
                            )
                        )
                    }
                }

                // Category dropdown
                var expanded by remember { mutableStateOf(false) }
                ExposedDropdownMenuBox(
                    expanded = expanded,
                    onExpandedChange = { expanded = !expanded }
                ) {
                    OutlinedTextField(
                        value = selectedCategory,
                        onValueChange = { },
                        readOnly = true,
                        label = { Text("Category") },
                        trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = NeonGlow,
                            focusedLabelColor = NeonGlow
                        )
                    )
                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = { expanded = false }
                    ) {
                        categories.forEach { category ->
                            DropdownMenuItem(
                                text = { Text(category) },
                                onClick = {
                                    selectedCategory = category
                                    expanded = false
                                }
                            )
                        }
                    }
                }

                // Note input
                OutlinedTextField(
                    value = note,
                    onValueChange = { note = it },
                    label = { Text("Note (Optional)") },
                    modifier = Modifier.fillMaxWidth(),
                    maxLines = 3,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = NeonGlow,
                        focusedLabelColor = NeonGlow,
                        cursorColor = NeonGlow
                    )
                )
            }
        },
        confirmButton = {
            Button(
                onClick = {
                    val amountValue = amount.toDoubleOrNull()
                    if (amountValue != null && amountValue > 0 && selectedCategory.isNotEmpty()) {
                        onAddEntry(
                            SimpleFinanceEntry(
                                amount = amountValue,
                                type = selectedType,
                                category = selectedCategory,
                                note = note,
                                date = System.currentTimeMillis()
                            )
                        )
                    }
                },
                colors = ButtonDefaults.buttonColors(containerColor = NeonGreen)
            ) {
                Text("Add", color = Color.Black)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel", color = NeonText)
            }
        },
        containerColor = NeonCard,
        textContentColor = NeonText
    )
}
