package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel
import com.app.wordifynumbers.util.FeedbackUtil
import kotlin.math.cos
import kotlin.math.sin

// Calculator categories
enum class CalculatorCategory(val title: String, val icon: ImageVector) {
    MATH("Math", Icons.Default.Calculate),
    SPECIAL("Special", Icons.Default.Science)
}

private val calculators = listOf(
    // Math Calculators
    CalculatorType(
        id = "basic",
        title = "Basic",
        icon = Icons.Default.Calculate,
        description = "Standard arithmetic operations",
        category = CalculatorCategory.MATH,
        accentColor = NeonBlue
    ),
    CalculatorType(
        id = "scientific",
        title = "Scientific",
        icon = Icons.Default.Science,
        description = "Advanced mathematical functions",
        category = CalculatorCategory.MATH,
        accentColor = NeonCyan
    ),
    CalculatorType(
        id = "percentage",
        title = "Percentage Calculator",
        icon = Icons.Default.Percent,
        description = "Calculate percentages, increases, and decreases",
        category = CalculatorCategory.MATH,
        accentColor = NeonCyan
    ),
    CalculatorType(
        id = "complex",
        title = "Complex Numbers",
        icon = Icons.Default.Architecture,
        description = "Complex number operations",
        category = CalculatorCategory.MATH,
        accentColor = NeonPurple
    ),
    CalculatorType(
        id = "areaVolume",
        title = "Area/Volume",
        icon = Icons.Default.Architecture,
        description = "Calculate area and volume of shapes",
        category = CalculatorCategory.MATH,
        accentColor = NeonBlue
    ),

    // Special Calculators
    CalculatorType(
        id = "statistics",
        title = "Statistics",
        icon = Icons.Default.Timeline,
        description = "Statistical analysis with international standards",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "bmi",
        title = "Health",
        icon = Icons.Default.MonitorHeart,
        description = "BMI, WHR & BMR with international standards",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "programmer",
        title = "Programmer",
        icon = Icons.Default.Code,
        description = "Binary, hex & octal conversions",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "age",
        title = "Age Calculator",
        icon = Icons.Default.Info,
        description = "Calculate age from date of birth",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "date",
        title = "Date & Time",
        icon = Icons.Default.CalendarToday,
        description = "Date and time calculations",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonOrange
    )
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalculatorScreen(
    modifier: Modifier = Modifier,
    viewModel: CalculatorViewModel,
    onNavigateToCalculator: (String) -> Unit,
    onNavigateToWords: () -> Unit = {},
    onNavigateToLargeNumbers: () -> Unit = {}
) {
    var selectedCategory by remember { mutableStateOf(CalculatorCategory.MATH) }
    var selectedCalculator by remember { mutableStateOf<CalculatorType?>(calculators.firstOrNull()) }
    val context = LocalContext.current
    var recentlyUsedCalculators by remember { mutableStateOf<List<CalculatorType>>(emptyList()) }
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp

    // Enhanced animated background with multiple layers
    val infiniteTransition = rememberInfiniteTransition(label = "backgroundEffects")

    val primaryPulse by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(6000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "primaryPulse"
    )

    val secondaryPulse by infiniteTransition.animateFloat(
        initialValue = 1.1f,
        targetValue = 0.9f,
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "secondaryPulse"
    )

    val rotationAngle by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(20000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "rotation"
    )

    // Category transition animation
    val categoryTransition = updateTransition(
        targetState = selectedCategory,
        label = "categoryTransition"
    )

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(
                        NeonDeepBlue.copy(alpha = 0.95f),
                        NeonBackground.copy(alpha = 0.98f),
                        Color.Black.copy(alpha = 0.99f)
                    ),
                    center = Offset(0.5f, 0.3f),
                    radius = 1400f * primaryPulse
                )
            )
    ) {
        // Animated background orbs for depth
        Box(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    rotationZ = rotationAngle * 0.1f
                }
        ) {
            // Primary orb
            Box(
                modifier = Modifier
                    .size(300.dp)
                    .offset(x = 100.dp, y = 150.dp)
                    .graphicsLayer {
                        scaleX = secondaryPulse
                        scaleY = secondaryPulse
                        alpha = 0.1f
                    }
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                NeonCyan.copy(alpha = 0.3f),
                                Color.Transparent
                            )
                        ),
                        shape = CircleShape
                    )
            )

            // Secondary orb
            Box(
                modifier = Modifier
                    .size(200.dp)
                    .offset(x = (-50).dp, y = 400.dp)
                    .graphicsLayer {
                        scaleX = primaryPulse * 0.8f
                        scaleY = primaryPulse * 0.8f
                        alpha = 0.08f
                    }
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                NeonPurple.copy(alpha = 0.4f),
                                Color.Transparent
                            )
                        ),
                        shape = CircleShape
                    )
            )
        }
        // Main content with enhanced layout
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(
                    top = 24.dp,
                    bottom = 16.dp,
                    start = 20.dp,
                    end = 20.dp
                ),
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // Enhanced top bar with glassmorphism effect
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 16.dp,
                        spotColor = NeonGlow.copy(alpha = 0.3f),
                        ambientColor = NeonGlow.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(20.dp)
                    ),
                shape = RoundedCornerShape(20.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.15f)
                ),
                border = BorderStroke(1.dp, NeonGlow.copy(alpha = 0.2f))
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, vertical = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Enhanced title with gradient effect
                    Column {
                        Text(
                            text = "Calculators",
                            style = MaterialTheme.typography.headlineLarge.copy(
                                fontWeight = FontWeight.ExtraBold,
                                letterSpacing = 1.2.sp,
                                brush = Brush.linearGradient(
                                    colors = listOf(
                                        NeonGlow,
                                        NeonCyan.copy(alpha = 0.8f)
                                    )
                                )
                            )
                        )
                        Text(
                            text = "Choose your calculation tool",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                color = NeonText.copy(alpha = 0.7f),
                                letterSpacing = 0.3.sp
                            )
                        )
                    }

                    // Enhanced navigation buttons with micro-interactions
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        EnhancedNavigationButton(
                            icon = Icons.Default.FormatListNumbered,
                            contentDescription = "Go to Words",
                            accentColor = NeonBlue,
                            onClick = {
                                onNavigateToWords()
                                FeedbackUtil.buttonPress(context)
                            }
                        )

                        EnhancedNavigationButton(
                            icon = Icons.Default.School,
                            contentDescription = "Go to Large Numbers",
                            accentColor = NeonPurple,
                            onClick = {
                                onNavigateToLargeNumbers()
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }
                }
            }

            // Enhanced category selector with modern design
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 12.dp,
                        spotColor = when (selectedCategory) {
                            CalculatorCategory.MATH -> NeonBlue.copy(alpha = 0.3f)
                            CalculatorCategory.SPECIAL -> NeonPink.copy(alpha = 0.3f)
                        },
                        ambientColor = when (selectedCategory) {
                            CalculatorCategory.MATH -> NeonBlue.copy(alpha = 0.1f)
                            CalculatorCategory.SPECIAL -> NeonPink.copy(alpha = 0.1f)
                        },
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.1f)
                ),
                border = BorderStroke(
                    1.dp,
                    when (selectedCategory) {
                        CalculatorCategory.MATH -> NeonBlue.copy(alpha = 0.3f)
                        CalculatorCategory.SPECIAL -> NeonPink.copy(alpha = 0.3f)
                    }
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(8.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    CalculatorCategory.values().forEach { category ->
                        val isSelected = selectedCategory == category
                        val categoryColor = when (category) {
                            CalculatorCategory.MATH -> NeonBlue
                            CalculatorCategory.SPECIAL -> NeonPink
                        }

                        Surface(
                            onClick = {
                                selectedCategory = category
                                selectedCalculator = calculators.firstOrNull { it.category == category }
                                FeedbackUtil.buttonPress(context)
                            },
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            shape = RoundedCornerShape(12.dp),
                            color = if (isSelected) {
                                categoryColor.copy(alpha = 0.15f)
                            } else {
                                Color.Transparent
                            },
                            border = if (isSelected) {
                                BorderStroke(2.dp, categoryColor.copy(alpha = 0.6f))
                            } else {
                                BorderStroke(1.dp, NeonText.copy(alpha = 0.2f))
                            }
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(horizontal = 16.dp),
                                horizontalArrangement = Arrangement.Center,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = category.icon,
                                    contentDescription = null,
                                    tint = if (isSelected) categoryColor else NeonText.copy(alpha = 0.7f),
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = category.title,
                                    style = MaterialTheme.typography.titleMedium.copy(
                                        fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Medium,
                                        letterSpacing = 0.5.sp
                                    ),
                                    color = if (isSelected) categoryColor else NeonText.copy(alpha = 0.7f)
                                )
                            }
                        }
                    }
                }
            }

            // Enhanced calculator grid with adaptive layout
            val gridColumns = if (isLandscape) 3 else 2
            val filteredCalculators = calculators.filter { it.category == selectedCategory }

            LazyVerticalGrid(
                columns = GridCells.Fixed(gridColumns),
                contentPadding = PaddingValues(4.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(
                    items = filteredCalculators,
                    key = { it.id }
                ) { calculator ->
                    // Animate item appearance
                    AnimatedVisibility(
                        visible = true,
                        enter = fadeIn(
                            animationSpec = tween(
                                durationMillis = 600,
                                delayMillis = filteredCalculators.indexOf(calculator) * 100
                            )
                        ) + slideInVertically(
                            animationSpec = tween(
                                durationMillis = 600,
                                delayMillis = filteredCalculators.indexOf(calculator) * 100
                            ),
                            initialOffsetY = { it / 2 }
                        ),
                        exit = fadeOut() + slideOutVertically()
                    ) {
                        EnhancedCalculatorCard(
                            calculator = calculator,
                            isSelected = selectedCalculator?.id == calculator.id,
                            onClick = {
                                selectedCalculator = calculator
                                FeedbackUtil.buttonPress(context)

                                // Update recently used calculators
                                recentlyUsedCalculators = listOf(calculator) + recentlyUsedCalculators.filter { it != calculator }
                                if (recentlyUsedCalculators.size > 5) {
                                    recentlyUsedCalculators = recentlyUsedCalculators.dropLast(1)
                                }

                                // Navigate to the selected calculator
                                onNavigateToCalculator(calculator.id)
                            }
                        )
                    }
                }
            }

            // Enhanced recently used section
            AnimatedVisibility(
                visible = recentlyUsedCalculators.isNotEmpty(),
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = NeonGlow.copy(alpha = 0.2f),
                            ambientColor = NeonGlow.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.08f)
                    ),
                    border = BorderStroke(1.dp, NeonGlow.copy(alpha = 0.15f))
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.History,
                                contentDescription = null,
                                tint = NeonGlow.copy(alpha = 0.8f),
                                modifier = Modifier.size(20.dp)
                            )
                            Text(
                                text = "Recently Used",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.SemiBold,
                                    letterSpacing = 0.5.sp
                                ),
                                color = NeonGlow.copy(alpha = 0.9f)
                            )
                        }

                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            contentPadding = PaddingValues(horizontal = 4.dp)
                        ) {
                            items(
                                items = recentlyUsedCalculators,
                                key = { it.id }
                            ) { calculator ->
                                EnhancedRecentCalculatorChip(
                                    calculator = calculator,
                                    onClick = {
                                        selectedCategory = calculator.category as CalculatorCategory
                                        selectedCalculator = calculator
                                        FeedbackUtil.buttonPress(context)
                                        onNavigateToCalculator(calculator.id)
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

// Enhanced navigation button component
@Composable
private fun EnhancedNavigationButton(
    icon: ImageVector,
    contentDescription: String,
    accentColor: Color,
    onClick: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }

    Surface(
        onClick = onClick,
        modifier = Modifier
            .size(48.dp)
            .shadow(
                elevation = 8.dp,
                spotColor = accentColor.copy(alpha = 0.4f),
                ambientColor = accentColor.copy(alpha = 0.2f),
                shape = CircleShape
            ),
        shape = CircleShape,
        color = NeonCard.copy(alpha = 0.2f),
        border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f)),
        interactionSource = interactionSource
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            accentColor.copy(alpha = 0.1f),
                            Color.Transparent
                        )
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = contentDescription,
                tint = accentColor,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

@Composable
private fun EnhancedCalculatorCard(
    calculator: CalculatorType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val accentColor = calculator.accentColor
    val interactionSource = remember { MutableInteractionSource() }

    // Enhanced animations
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.02f else 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "cardScale"
    )

    val elevation by animateFloatAsState(
        targetValue = if (isSelected) 20f else 8f,
        animationSpec = tween(300, easing = EaseOutCubic),
        label = "cardElevation"
    )

    // Continuous glow animation
    val infiniteTransition = rememberInfiniteTransition(label = "cardGlow")
    val glowIntensity by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.7f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowIntensity"
    )

    val shimmerOffset by infiniteTransition.animateFloat(
        initialValue = -1f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "shimmer"
    )

    Box(
        modifier = Modifier
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
    ) {
        // Enhanced glow effect
        if (isSelected) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .blur(radius = 20.dp)
                    .background(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                accentColor.copy(alpha = glowIntensity * 0.6f),
                                Color.Transparent
                            ),
                            radius = 300f
                        ),
                        shape = RoundedCornerShape(24.dp)
                    )
            )
        }

        // Main card with glassmorphism effect
        Card(
            onClick = onClick,
            modifier = Modifier
                .fillMaxWidth()
                .height(180.dp)
                .shadow(
                    elevation = elevation.dp,
                    shape = RoundedCornerShape(24.dp),
                    spotColor = accentColor.copy(alpha = 0.4f),
                    ambientColor = accentColor.copy(alpha = 0.2f)
                ),
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(
                containerColor = NeonCard.copy(alpha = if (isSelected) 0.25f else 0.15f)
            ),
            border = BorderStroke(
                width = if (isSelected) 2.dp else 1.dp,
                brush = Brush.linearGradient(
                    colors = listOf(
                        accentColor.copy(alpha = if (isSelected) 0.8f else 0.3f),
                        accentColor.copy(alpha = if (isSelected) 0.4f else 0.1f)
                    )
                )
            ),
            interactionSource = interactionSource
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                accentColor.copy(alpha = 0.08f),
                                Color.Transparent,
                                accentColor.copy(alpha = 0.04f)
                            ),
                            start = Offset(0f, 0f),
                            end = Offset(1000f, 1000f)
                        )
                    )
            ) {
                // Shimmer effect overlay
                if (isSelected) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                brush = Brush.linearGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        accentColor.copy(alpha = 0.1f),
                                        Color.Transparent
                                    ),
                                    start = Offset(shimmerOffset * 1000f, shimmerOffset * 1000f),
                                    end = Offset((shimmerOffset + 0.3f) * 1000f, (shimmerOffset + 0.3f) * 1000f)
                                )
                            )
                    )
                }

                // Content layout
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(20.dp),
                    verticalArrangement = Arrangement.SpaceBetween
                ) {
                    // Enhanced icon with multiple layers
                    Box(
                        modifier = Modifier.size(56.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        // Background glow
                        Box(
                            modifier = Modifier
                                .size(56.dp)
                                .background(
                                    brush = Brush.radialGradient(
                                        colors = listOf(
                                            accentColor.copy(alpha = 0.2f),
                                            Color.Transparent
                                        )
                                    ),
                                    shape = RoundedCornerShape(16.dp)
                                )
                        )

                        // Icon container
                        Surface(
                            modifier = Modifier.size(48.dp),
                            shape = RoundedCornerShape(14.dp),
                            color = accentColor.copy(alpha = 0.15f),
                            border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = calculator.icon,
                                    contentDescription = null,
                                    tint = accentColor.copy(alpha = 0.9f),
                                    modifier = Modifier.size(28.dp)
                                )
                            }
                        }
                    }

                    // Enhanced text content
                    Column(
                        verticalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        Text(
                            text = calculator.title,
                            style = MaterialTheme.typography.titleLarge.copy(
                                fontWeight = FontWeight.Bold,
                                letterSpacing = 0.5.sp
                            ),
                            color = if (isSelected) accentColor.copy(alpha = 0.95f) else NeonText.copy(alpha = 0.9f),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )

                        Text(
                            text = calculator.description,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                letterSpacing = 0.2.sp,
                                lineHeight = 18.sp
                            ),
                            color = NeonText.copy(alpha = 0.7f),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun EnhancedRecentCalculatorChip(
    calculator: CalculatorType,
    onClick: () -> Unit
) {
    val accentColor = calculator.accentColor
    val interactionSource = remember { MutableInteractionSource() }

    // Hover animation
    val scale by animateFloatAsState(
        targetValue = 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "chipScale"
    )

    Surface(
        onClick = onClick,
        modifier = Modifier
            .height(48.dp)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .shadow(
                elevation = 6.dp,
                spotColor = accentColor.copy(alpha = 0.3f),
                ambientColor = accentColor.copy(alpha = 0.1f),
                shape = RoundedCornerShape(24.dp)
            ),
        shape = RoundedCornerShape(24.dp),
        color = NeonCard.copy(alpha = 0.15f),
        border = BorderStroke(
            1.dp,
            brush = Brush.linearGradient(
                colors = listOf(
                    accentColor.copy(alpha = 0.4f),
                    accentColor.copy(alpha = 0.2f)
                )
            )
        ),
        interactionSource = interactionSource
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            accentColor.copy(alpha = 0.08f),
                            Color.Transparent
                        )
                    )
                )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(10.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Enhanced icon with background
                Surface(
                    modifier = Modifier.size(28.dp),
                    shape = RoundedCornerShape(8.dp),
                    color = accentColor.copy(alpha = 0.15f),
                    border = BorderStroke(0.5.dp, accentColor.copy(alpha = 0.3f))
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = calculator.icon,
                            contentDescription = null,
                            tint = accentColor.copy(alpha = 0.9f),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }

                Text(
                    text = calculator.title,
                    style = MaterialTheme.typography.labelLarge.copy(
                        fontWeight = FontWeight.Medium,
                        letterSpacing = 0.3.sp
                    ),
                    color = NeonText.copy(alpha = 0.9f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}


