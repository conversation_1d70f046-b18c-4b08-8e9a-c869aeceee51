package com.app.wordifynumbers.utils

import java.util.Locale

/**
 * Provides cultural, historical, and mathematical facts about numbers
 * to enhance the educational value of the app
 */
object CulturalInsights {
    private val numberFacts = mapOf(
        0 to "Zero was developed as a number in India around the 5th century and revolutionized mathematics.",
        1 to "One is the only number that is neither prime nor composite.",
        2 to "Two is the only even prime number.",
        3 to "Three is considered a lucky number in many cultures and is the first odd prime number.",
        4 to "Four is the only number that has the same number of letters as its value in English.",
        5 to "Five is the number of fingers on a human hand and the basis of the decimal and quinary number systems.",
        6 to "Six is the smallest perfect number (sum of its proper divisors equals the number itself: 1+2+3=6).",
        7 to "Seven is considered lucky in many Western cultures and appears frequently in religious contexts.",
        8 to "Eight is considered extremely lucky in Chinese culture as it sounds like the word for prosperity.",
        9 to "Nine is considered lucky in Japanese culture as it sounds like the word for suffering, suggesting perseverance.",
        10 to "Ten is the base of the decimal numeral system used worldwide.",
        11 to "Eleven players make up a football/soccer team on each side.",
        12 to "Twelve is the number of months in a year and hours displayed on most analog clocks.",
        13 to "Thirteen is considered unlucky in Western superstition, leading to triskaidekaphobia (fear of the number 13).",
        42 to "Forty-two is the 'Answer to the Ultimate Question of Life, the Universe, and Everything' in <PERSON>' book.",
        100 to "One hundred years make up a century.",
        666 to "Six hundred sixty-six is known as the 'Number of the Beast' in the Book of Revelation.",
        1000 to "One thousand years make up a millennium."
    )

    private val generalFacts = listOf(
        "Pi (π) is an irrational number that represents the ratio of a circle's circumference to its diameter.",
        "The Fibonacci sequence (0, 1, 1, 2, 3, 5, 8, 13...) appears frequently in nature and art.",
        "Zero is the only number that can't be represented in Roman numerals.",
        "The Golden Ratio (approximately 1.618) is found throughout nature and is considered aesthetically pleasing.",
        "A googol is the number 1 followed by 100 zeros, far larger than the number of atoms in the observable universe.",
        "The number e (approximately 2.718) is the base of natural logarithms and is fundamental in calculus.",
        "Prime numbers have fascinated mathematicians for millennia and are crucial for modern cryptography.",
        "The concept of negative numbers was resisted in Europe until the 17th century.",
        "Different cultures developed different numeral systems - Babylonian used base-60, Maya used base-20.",
        "The number system we use today (0-9) is called the Hindu-Arabic numeral system.",
        "Perfect numbers are rare - only 51 are known, and all even perfect numbers end in 6 or 8.",
        "The ancient Greeks used letters to represent numbers rather than dedicated numerals."
    )

    // Language-specific facts
    private val spanishFacts = mapOf(
        7 to "Siete es un número de suerte en muchas culturas y representa la perfección.",
        13 to "Trece se considera un número de mala suerte en muchas culturas occidentales.",
        15 to "Quince es la edad de la Quinceañera, una importante celebración en la cultura latinoamericana.",
        21 to "Veintiuno es la edad legal para beber alcohol en muchos países de habla hispana."
    )

    private val frenchFacts = mapOf(
        7 to "Sept est considéré comme un nombre chanceux dans de nombreuses cultures.",
        13 to "Treize est considéré comme un nombre malchanceux dans de nombreuses cultures occidentales.",
        14 to "Quatorze juillet est la fête nationale française, commémorant la prise de la Bastille.",
        21 to "Vingt-et-un est l'âge légal pour consommer de l'alcool dans de nombreux pays."
    )

    private val germanFacts = mapOf(
        7 to "Sieben gilt in vielen Kulturen als Glückszahl.",
        9 to "Neun ist eine bedeutende Zahl in der germanischen Mythologie.",
        12 to "Zwölf Monate hat ein Jahr und zwölf Stunden hat eine Uhr.",
        30 to "Der Dreißigjährige Krieg (1618-1648) war ein verheerender Konflikt in der deutschen Geschichte."
    )

    private val hindiFacts = mapOf(
        7 to "सात को कई संस्कृतियों में भाग्यशाली संख्या माना जाता है।",
        9 to "नवरात्रि एक नौ रात का त्योहार है जो देवी दुर्गा को समर्पित है।",
        108 to "१०८ हिंदू धर्म में एक पवित्र संख्या है और एक माला में १०८ मनके होते हैं।"
    )

    private val chineseFacts = mapOf(
        4 to "四 (si) in Chinese culture is considered unlucky because it sounds similar to the word for death.",
        8 to "八 (ba) in Chinese culture is considered lucky because it sounds similar to the word for prosperity.",
        9 to "九 (jiu) in Chinese culture represents longevity because it sounds similar to the word for long-lasting.",
        6 to "六 (liu) in Chinese culture symbolizes smoothness because it sounds similar to the word for flow."
    )

    private val japaneseFacts = mapOf(
        4 to "In Japan, the number 4 (shi) is considered unlucky because it sounds like the word for death.",
        7 to "Seven is considered lucky in Japanese culture, with seven gods of fortune (Shichifukujin).",
        8 to "Eight is associated with prosperity in Japanese culture, representing expanding wealth.",
        9 to "Nine (ku) can be unlucky in Japanese as it sounds like the word for suffering."
    )

    private val arabicFacts = mapOf(
        7 to "The number seven has great importance in Islamic culture, such as seven heavens and seven circumambulations around the Kaaba.",
        99 to "In Islam, Allah has 99 names, known as the Beautiful Names (Asma al-Husna).",
        786 to "The number 786 represents 'Bismillah ir-Rahman ir-Rahim' in the Abjad numeral system.",
        40 to "Forty days is an important period in Islamic culture, appearing in many religious contexts."
    )

    private val russianFacts = mapOf(
        3 to "Three is an important number in Russian fairy tales and folklore, often featuring three heroes, three tasks, etc.",
        7 to "Seven is considered a lucky number in Russian culture.",
        13 to "Unlike Western cultures, the number 13 is not considered particularly unlucky in Russia.",
        40 to "Forty is a significant number in Russian culture, associated with the period of mourning and religious rituals."
    )

    private val koreanFacts = mapOf(
        4 to "In Korea, the number 4 (sa) is considered unlucky because it sounds similar to the word for death.",
        8 to "The number 8 (pal) is considered lucky in Korean culture.",
        3 to "The number 3 (sam) is important in Korean culture and appears in many traditional ceremonies.",
        7 to "The number 7 (chil) symbolizes good luck in Korea."
    )

    private val italianFacts = mapOf(
        13 to "In Italy, the number 13 is considered lucky, unlike in many other Western cultures.",
        17 to "The number 17 is considered unlucky in Italy, as its Roman numeral XVII can be anagrammed to 'VIXI', which means 'I have lived' in Latin (implying death).",
        7 to "Seven is considered a lucky number in Italian culture.",
        3 to "Three is a significant number in Italian and Catholic culture, representing the Trinity."
    )

    /**
     * Gets a fact specific to the provided number if available,
     * otherwise returns a general mathematical or cultural fact
     *
     * @param number The number to get a fact for
     * @return A cultural, historical, or mathematical fact
     */
    fun getFact(number: Long): String {
        return getInsightForNumber(number, Locale.getDefault())
    }

    /**
     * Gets a fact specific to the provided number in the specified locale
     *
     * @param number The number to get a fact for
     * @param locale The locale to determine which language to use
     * @return A cultural, historical, or mathematical fact in the appropriate language
     */
    fun getInsightForNumber(number: Long, locale: Locale): String {
        // Try to get a locale-specific fact first
        val localeFact = when (locale.language) {
            "es" -> spanishFacts[number.toInt()]
            "fr" -> frenchFacts[number.toInt()]
            "de" -> germanFacts[number.toInt()]
            "hi" -> hindiFacts[number.toInt()]
            "zh" -> chineseFacts[number.toInt()]
            "ja" -> japaneseFacts[number.toInt()]
            "ar" -> arabicFacts[number.toInt()]
            "ru" -> russianFacts[number.toInt()]
            "ko" -> koreanFacts[number.toInt()]
            "it" -> italianFacts[number.toInt()]
            else -> null
        }

        if (localeFact != null) {
            return localeFact
        }

        // If no locale-specific fact, try to get a general fact in English
        val specificFact = numberFacts[number.toInt()]
        if (specificFact != null) {
            return specificFact
        }

        // For special cases like even/odd, prime, etc.
        if (number % 2 == 0L) {
            return "$number is an even number. Even numbers are divisible by 2 and end with 0, 2, 4, 6, or 8."
        }
        if (number % 2 == 1L) {
            return "$number is an odd number. Odd numbers are not divisible by 2 and end with 1, 3, 5, 7, or 9."
        }
        if (isPrime(number)) {
            return "$number is a prime number, divisible only by 1 and itself."
        }

        // For larger numbers, provide some context
        if (number > 1000) {
            when {
                number < 10000 -> return "$number has 4 digits. Four-digit numbers are commonly used for years in the Gregorian calendar."
                number < 100000 -> return "$number has 5 digits. Five-digit numbers are often used for ZIP codes in many countries."
                number < 1000000 -> return "$number has 6 digits. Six-digit numbers are commonly used for postal codes and PIN codes."
                number < 10000000 -> return "$number has 7 digits. Seven-digit numbers were traditionally used for phone numbers in many countries."
                number < 1000000000 -> return "$number has ${number.toString().length} digits. Numbers of this magnitude are often used to represent populations."
                number < 1000000000000 -> return "$number has ${number.toString().length} digits. Numbers in the billions are used to describe national budgets and GDPs."
                else -> return "$number has ${number.toString().length} digits. Numbers of this magnitude are rarely encountered in everyday life."
            }
        }

        // Return a random general fact
        return generalFacts.random()
    }

    /**
     * Returns a random fact about numbers
     * @return A random cultural, historical, or mathematical fact
     */
    fun getRandomFact(): String {
        return generalFacts.random()
    }

    /**
     * Checks if a number is prime
     * @param n The number to check
     * @return True if the number is prime, false otherwise
     */
    private fun isPrime(n: Long): Boolean {
        if (n <= 1) return false
        if (n <= 3) return true
        if (n % 2 == 0L || n % 3 == 0L) return false

        var i = 5L
        while (i * i <= n) {
            if (n % i == 0L || n % (i + 2) == 0L) return false
            i += 6
        }
        return true
    }
}
