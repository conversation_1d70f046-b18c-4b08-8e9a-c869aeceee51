package com.app.wordifynumbers.ui.components

/**
 * Enum defining different number formatting options for the calculator with display names.
 */
enum class NumberFormat(val displayName: String) {
    DECIMAL("Decimal"),      // Regular decimal notation
    SCIENTIFIC("Scientific"),    // Scientific notation (e.g., 1.23E4)
    ENGINEERING("Engineering")   // Engineering notation with exponents divisible by 3
}