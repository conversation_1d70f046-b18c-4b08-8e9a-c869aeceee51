package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import kotlinx.coroutines.delay
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.theme.*

/**
 * A stylized box for displaying calculation results
 *
 * @param mainResult The main result text to display
 * @param details Optional list of detail strings to display
 * @param modifier Modifier for the component
 * @param accentColor The accent color for the result
 * @param animateEntry Whether to animate the entry of the component
 */
@Composable
fun CalculationResultBox(
    mainResult: String,
    details: List<String> = emptyList(),
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    animateEntry: Boolean = true
) {
    val infiniteTransition = rememberInfiniteTransition(label = "resultGlow")
    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 1.0f,
        targetValue = 1.02f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseAnimation"
    )

    val content = @Composable {
        Box(
            modifier = Modifier
                .shadow(
                    elevation = 12.dp,
                    spotColor = accentColor.copy(alpha = glowOpacity * 0.6f),
                    ambientColor = accentColor.copy(alpha = glowOpacity * 0.3f),
                    shape = RoundedCornerShape(20.dp)
                )
                .clip(RoundedCornerShape(20.dp))
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            NeonCard.copy(alpha = 0.95f),
                            NeonCard.copy(alpha = 0.85f)
                        ),
                        center = Offset(0.5f, 0.5f),
                        radius = 800f * pulseScale
                    )
                )
                .padding(20.dp)
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                // Main result
                Text(
                    text = mainResult,
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        letterSpacing = 0.5.sp
                    ),
                    color = accentColor,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )

                // Details
                if (details.isNotEmpty()) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // Divider
                        Box(
                            modifier = Modifier
                                .width(80.dp)
                                .height(2.dp)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        colors = listOf(
                                            Color.Transparent,
                                            accentColor.copy(alpha = glowOpacity * 0.7f),
                                            Color.Transparent
                                        )
                                    )
                                )
                        )

                        // Detail items
                        details.forEach { detail ->
                            Text(
                                text = detail,
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = NeonText,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }
        }
    }

    if (animateEntry) {
        var visible by remember { mutableStateOf(false) }

        LaunchedEffect(mainResult) {
            visible = false
            delay(100)
            visible = true
        }

        AnimatedVisibility(
            visible = visible,
            enter = fadeIn(tween(500)) + expandVertically(tween(500, easing = EaseOutCubic)),
            exit = fadeOut(tween(300)) + shrinkVertically(tween(300, easing = EaseInCubic)),
            modifier = modifier
        ) {
            content()
        }
    } else {
        Box(modifier = modifier) {
            content()
        }
    }
}

/**
 * A simpler result box for displaying calculation results
 *
 * @param title The title or label for the result
 * @param result The result text to display
 * @param modifier Modifier for the component
 * @param accentColor The accent color for the result
 * @param animateEntry Whether to animate the entry of the component
 */
@Composable
fun ResultBox(
    title: String,
    result: String,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    animateEntry: Boolean = false
) {
    val infiniteTransition = rememberInfiniteTransition(label = "resultGlow")
    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    val content = @Composable {
        Box(
            modifier = Modifier
                .shadow(
                    elevation = 8.dp,
                    spotColor = accentColor.copy(alpha = glowOpacity * 0.5f),
                    ambientColor = accentColor.copy(alpha = glowOpacity * 0.25f),
                    shape = RoundedCornerShape(16.dp)
                )
                .clip(RoundedCornerShape(16.dp))
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            NeonCard.copy(alpha = 0.9f),
                            NeonCard.copy(alpha = 0.8f)
                        )
                    )
                )
                .padding(16.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Title
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = accentColor
                )

                // Result
                Text(
                    text = result,
                    style = MaterialTheme.typography.bodyLarge,
                    color = NeonText
                )
            }
        }
    }

    if (animateEntry) {
        var visible by remember { mutableStateOf(false) }

        LaunchedEffect(result) {
            visible = false
            delay(100)
            visible = true
        }

        AnimatedVisibility(
            visible = visible,
            enter = fadeIn(tween(500)) + expandVertically(tween(500, easing = EaseOutCubic)),
            exit = fadeOut(tween(300)) + shrinkVertically(tween(300, easing = EaseInCubic)),
            modifier = modifier
        ) {
            content()
        }
    } else {
        Box(modifier = modifier) {
            content()
        }
    }
}
