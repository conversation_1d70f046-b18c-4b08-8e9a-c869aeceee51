package com.app.wordifynumbers.util

import android.content.Context
import android.media.MediaPlayer
import android.media.AudioAttributes
import android.media.SoundPool

class AudioFeedback(private val context: Context) {
    private val soundPool = SoundPool.Builder()
        .setMaxStreams(4)
        .setAudioAttributes(AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_ASSISTANCE_SONIFICATION)
            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
            .build())
        .build()

    private val buttonClickSound = soundPool.load(
        context,
        context.resources.getIdentifier("button_click", "raw", context.packageName),
        1
    )
    private val errorSound = soundPool.load(
        context,
        context.resources.getIdentifier("error_beep", "raw", context.packageName),
        1
    )
    private val successSound = soundPool.load(
        context,
        context.resources.getIdentifier("success_chime", "raw", context.packageName),
        1
    )

    fun playButtonClick() {
        soundPool.play(buttonClickSound, 0.3f, 0.3f, 1, 0, 1f)
    }

    fun playError() {
        soundPool.play(errorSound, 0.5f, 0.5f, 1, 0, 1f)
    }

    fun playSuccess() {
        soundPool.play(successSound, 0.4f, 0.4f, 1, 0, 1f)
    }

    fun release() {
        soundPool.release()
    }
}
