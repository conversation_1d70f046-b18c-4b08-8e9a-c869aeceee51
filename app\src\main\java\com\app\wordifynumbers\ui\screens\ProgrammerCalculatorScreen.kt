package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import java.util.Locale
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.*
import com.app.wordifynumbers.ui.navigation.MultiModalBackHandler
import com.app.wordifynumbers.ui.navigation.NavigationUtils
import com.app.wordifynumbers.util.FeedbackUtil

/**
 * A modern, international-standard Programmer Calculator screen
 * Supports number base conversions and bitwise operations
 */
@Composable
fun ProgrammerCalculatorScreen(modifier: Modifier = Modifier) {
    // ViewModel for business logic
    val viewModel: ProgrammerCalculatorViewModel = viewModel()

    // Collect state from ViewModel
    val inputValue by viewModel.inputValue.collectAsState()
    val inputBase by viewModel.inputBase.collectAsState()
    val binaryValue by viewModel.binaryValue.collectAsState()
    val octalValue by viewModel.octalValue.collectAsState()
    val decimalValue by viewModel.decimalValue.collectAsState()
    val hexValue by viewModel.hexValue.collectAsState()
    val isError by viewModel.isError.collectAsState()
    val secondNumber by viewModel.secondNumber.collectAsState()
    val shiftAmount by viewModel.shiftAmount.collectAsState()
    val bitRepresentation by viewModel.bitRepresentation.collectAsState()
    val wordRepresentation by viewModel.wordRepresentation.collectAsState()
    val availableLocales by viewModel.availableLocales.collectAsState()
    val selectedLocale by viewModel.selectedLocale.collectAsState()

    // Local state
    var showLocaleDialog by remember { mutableStateOf(false) }
    var showInfoDialog by remember { mutableStateOf(false) }

    val context = LocalContext.current
    val scrollState = rememberScrollState()

    // Handle back button press using standardized multi-modal handler
    MultiModalBackHandler(
        modalStates = NavigationUtils.createModalStates(
            showInfoDialog to { showInfoDialog = false },
            showLocaleDialog to { showLocaleDialog = false }
        )
    )

    // Info Dialog
    if (showInfoDialog) {
        AlertDialog(
            onDismissRequest = { showInfoDialog = false },
            title = {
                Text(
                    text = "Programmer Calculator Help",
                    style = MaterialTheme.typography.titleLarge,
                    color = NeonPink
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        text = "This calculator helps programmers with number base conversions and bitwise operations.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "Number Bases:",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonPink
                    )

                    Text(
                        text = "• Binary (BIN): Base-2, uses only 0 and 1\n" +
                              "• Octal (OCT): Base-8, uses digits 0-7\n" +
                              "• Decimal (DEC): Base-10, uses digits 0-9\n" +
                              "• Hexadecimal (HEX): Base-16, uses digits 0-9 and letters A-F",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "Bitwise Operations:",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonPink
                    )

                    Text(
                        text = "• NOT (~): Inverts all bits\n" +
                              "• AND (&): Sets bit to 1 if both bits are 1\n" +
                              "• OR (|): Sets bit to 1 if either bit is 1\n" +
                              "• XOR (^): Sets bit to 1 if bits are different\n" +
                              "• Left Shift (<<): Shifts bits left, multiplying by 2\n" +
                              "• Right Shift (>>): Shifts bits right, dividing by 2",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )

                    Text(
                        text = "How to use:",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonPink
                    )

                    Text(
                        text = "1. Select a number base\n" +
                              "2. Enter a number using the keypad or text field\n" +
                              "3. View conversions to other bases\n" +
                              "4. Explore bitwise operations and bit representation",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showInfoDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = NeonPink
                    )
                ) {
                    Text("Close")
                }
            },
            containerColor = NeonCard,
            titleContentColor = NeonPink,
            textContentColor = NeonText
        )
    }

    // Locale Selection Dialog
    if (showLocaleDialog) {
        AlertDialog(
            onDismissRequest = { showLocaleDialog = false },
            title = {
                Text(
                    text = "Select Locale",
                    style = MaterialTheme.typography.titleLarge,
                    color = NeonPink
                )
            },
            text = {
                Column(
                    modifier = Modifier
                        .verticalScroll(rememberScrollState())
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    availableLocales.forEach { locale ->
                        val isSelected = locale == selectedLocale
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .clickable {
                                    viewModel.setSelectedLocale(locale)
                                    showLocaleDialog = false
                                    FeedbackUtil.buttonPress(context)
                                },
                            color = if (isSelected) NeonPink.copy(alpha = 0.2f) else Color.Transparent
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = viewModel.getLocaleDisplayName(locale),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = if (isSelected) NeonPink else NeonText
                                )

                                if (isSelected) {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = NeonPink
                                    )
                                }
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showLocaleDialog = false },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = NeonPink
                    )
                ) {
                    Text("Cancel")
                }
            },
            containerColor = NeonCard,
            titleContentColor = NeonPink,
            textContentColor = NeonText
        )
    }

    StandardCalculatorLayout(
        title = "Programmer Calculator",
        icon = Icons.Default.Code,
        accentColor = NeonPink,
        showInfoButton = true,
        onInfoClick = {
            showInfoDialog = true
            FeedbackUtil.buttonPress(context)
        },

        // Input Section
        inputSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Number Base Selection",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonPink
                    )

                    // Language selection button
                    TextButton(
                        onClick = {
                            showLocaleDialog = true
                            FeedbackUtil.buttonPress(context)
                        },
                        colors = ButtonDefaults.textButtonColors(
                            contentColor = NeonPink
                        )
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Language,
                                contentDescription = "Select language",
                                tint = NeonPink
                            )
                            Text(
                                text = selectedLocale.getDisplayLanguage(Locale.US),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }

                // Base Selection Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    NumberBase.values().forEach { base ->
                        Button(
                            onClick = {
                                viewModel.setInputBase(base)
                                FeedbackUtil.buttonPress(context)
                            },
                            modifier = Modifier.weight(1f),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (inputBase == base)
                                    NeonPink else NeonCard.copy(alpha = 0.7f),
                                contentColor = if (inputBase == base)
                                    NeonBackground else NeonPink
                            )
                        ) {
                            Text(
                                text = when (base) {
                                    NumberBase.BINARY -> "BIN"
                                    NumberBase.OCTAL -> "OCT"
                                    NumberBase.DECIMAL -> "DEC"
                                    NumberBase.HEXADECIMAL -> "HEX"
                                },
                                style = MaterialTheme.typography.labelMedium.copy(
                                    fontWeight = FontWeight.Bold
                                )
                            )
                        }
                    }
                }

                // Input Field
                CalculatorInputField(
                    label = "Enter ${
                        when (inputBase) {
                            NumberBase.BINARY -> "Binary"
                            NumberBase.OCTAL -> "Octal"
                            NumberBase.DECIMAL -> "Decimal"
                            NumberBase.HEXADECIMAL -> "Hexadecimal"
                        }
                    } Number",
                    value = inputValue,
                    onValueChange = { newValue -> viewModel.updateInputValue(newValue) },
                    accentColor = NeonPink,
                    supportingText = {
                        Text(when (inputBase) {
                            NumberBase.BINARY -> "Use only 0 and 1"
                            NumberBase.OCTAL -> "Use digits 0-7"
                            NumberBase.DECIMAL -> "Use digits 0-9"
                            NumberBase.HEXADECIMAL -> "Use digits 0-9 and letters A-F"
                        })
                    }
                )

                // Number Pad for the selected base
                when (inputBase) {
                    NumberBase.BINARY -> {
                        BinaryNumberPad(
                            onDigitClick = { digit -> viewModel.addDigit(digit) },
                            onClear = { viewModel.clearInput() },
                            onBackspace = { viewModel.backspace() }
                        )
                    }
                    NumberBase.OCTAL -> {
                        OctalNumberPad(
                            onDigitClick = { digit -> viewModel.addDigit(digit) },
                            onClear = { viewModel.clearInput() },
                            onBackspace = { viewModel.backspace() }
                        )
                    }
                    NumberBase.DECIMAL -> {
                        DecimalNumberPad(
                            onDigitClick = { digit -> viewModel.addDigit(digit) },
                            onClear = { viewModel.clearInput() },
                            onBackspace = { viewModel.backspace() }
                        )
                    }
                    NumberBase.HEXADECIMAL -> {
                        HexNumberPad(
                            onDigitClick = { digit -> viewModel.addDigit(digit) },
                            onClear = { viewModel.clearInput() },
                            onBackspace = { viewModel.backspace() }
                        )
                    }
                }
            }
        },

        // No separate action buttons needed
        actionButtons = null,

        // Result Section
        resultSection = {
            if (isError) {
                Text(
                    text = "Error: Invalid number format",
                    style = MaterialTheme.typography.bodyLarge,
                    color = NeonRed
                )
            } else if (inputValue.isNotEmpty()) {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        text = "Conversion Results",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonPink
                    )

                    Divider(
                        color = NeonPink.copy(alpha = 0.3f),
                        modifier = Modifier.padding(vertical = 4.dp)
                    )

                    ConversionResultRow("Binary (BIN)", binaryValue, inputBase == NumberBase.BINARY)
                    ConversionResultRow("Octal (OCT)", octalValue, inputBase == NumberBase.OCTAL)
                    ConversionResultRow("Decimal (DEC)", decimalValue, inputBase == NumberBase.DECIMAL)
                    ConversionResultRow("Hexadecimal (HEX)", hexValue, inputBase == NumberBase.HEXADECIMAL)

                    Divider(
                        color = NeonPink.copy(alpha = 0.3f),
                        modifier = Modifier.padding(vertical = 4.dp)
                    )

                    // Bit representation
                    if (bitRepresentation.isNotEmpty()) {
                        Text(
                            text = "Bit Representation:",
                            style = MaterialTheme.typography.titleSmall,
                            color = NeonPink,
                            modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                        )

                        Text(
                            text = bitRepresentation,
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText
                        )
                    }

                    // Word representation
                    if (wordRepresentation.isNotEmpty()) {
                        Text(
                            text = "In Words:",
                            style = MaterialTheme.typography.titleSmall,
                            color = NeonPink,
                            modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                        )

                        Text(
                            text = wordRepresentation,
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText
                        )
                    }
                }
            } else {
                Text(
                    text = "Enter a number to see conversions",
                    style = MaterialTheme.typography.bodyLarge,
                    color = NeonText.copy(alpha = 0.7f)
                )
            }
        },

        // Additional Content
        additionalContent = {
            if (inputValue.isNotEmpty() && !isError && decimalValue.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = "Bit Operations",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonPink
                        )

                        Divider(
                            color = NeonPink.copy(alpha = 0.3f),
                            modifier = Modifier.padding(vertical = 4.dp)
                        )

                        // Bitwise operations
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "NOT (~)",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateNOT(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Left Shift (<<1)",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateLeftShift1(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Right Shift (>>1)",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateRightShift1(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }

                        Divider(
                            color = NeonPink.copy(alpha = 0.3f),
                            modifier = Modifier.padding(vertical = 4.dp)
                        )

                        // Additional bitwise operations
                        Text(
                            text = "Bitwise Operations",
                            style = MaterialTheme.typography.titleSmall,
                            color = NeonPink,
                            modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Second operand:",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText,
                                modifier = Modifier.weight(1f)
                            )

                            OutlinedTextField(
                                value = secondNumber,
                                onValueChange = { viewModel.updateSecondNumber(it) },
                                modifier = Modifier.weight(1f),
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedBorderColor = NeonPink,
                                    unfocusedBorderColor = NeonPink.copy(alpha = 0.5f),
                                    focusedTextColor = NeonPink,
                                    unfocusedTextColor = NeonText
                                ),
                                singleLine = true
                            )
                        }

                        // AND operation
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "AND (&)",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateAND(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }

                        // OR operation
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "OR (|)",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateOR(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }

                        // XOR operation
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "XOR (^)",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateXOR(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }

                        // Custom shift operations
                        Divider(
                            color = NeonPink.copy(alpha = 0.3f),
                            modifier = Modifier.padding(vertical = 4.dp)
                        )

                        Text(
                            text = "Custom Shift Operations",
                            style = MaterialTheme.typography.titleSmall,
                            color = NeonPink,
                            modifier = Modifier.padding(top = 8.dp, bottom = 4.dp)
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "Shift amount:",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText,
                                modifier = Modifier.weight(1f)
                            )

                            OutlinedTextField(
                                value = shiftAmount,
                                onValueChange = { viewModel.updateShiftAmount(it) },
                                modifier = Modifier.weight(1f),
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedBorderColor = NeonPink,
                                    unfocusedBorderColor = NeonPink.copy(alpha = 0.5f),
                                    focusedTextColor = NeonPink,
                                    unfocusedTextColor = NeonText
                                ),
                                singleLine = true
                            )
                        }

                        // Custom left shift
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Left Shift (<<${shiftAmount.toIntOrNull() ?: 1})",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateCustomLeftShift(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }

                        // Custom right shift
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "Right Shift (>>${shiftAmount.toIntOrNull() ?: 1})",
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonText
                            )

                            Text(
                                text = viewModel.calculateCustomRightShift(),
                                style = MaterialTheme.typography.bodyMedium,
                                color = NeonPink
                            )
                        }
                    }
                }
            }
        }
    )
}

@Composable
private fun ConversionResultRow(
    label: String,
    value: String,
    isCurrentBase: Boolean
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = if (isCurrentBase) NeonPink else NeonText
        )

        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = if (isCurrentBase) FontWeight.Bold else FontWeight.Normal
            ),
            color = if (isCurrentBase) NeonPink else NeonText
        )
    }
}

@Composable
private fun BinaryNumberPad(
    onDigitClick: (String) -> Unit,
    onClear: () -> Unit,
    onBackspace: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("0", onClick = { onDigitClick("0") }, modifier = Modifier.weight(1f))
            NumberButton("1", onClick = { onDigitClick("1") }, modifier = Modifier.weight(1f))
            FunctionButton("C", onClick = onClear, modifier = Modifier.weight(1f))
            FunctionButton("⌫", onClick = onBackspace, modifier = Modifier.weight(1f))
        }
    }
}

@Composable
private fun OctalNumberPad(
    onDigitClick: (String) -> Unit,
    onClear: () -> Unit,
    onBackspace: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("0", onClick = { onDigitClick("0") }, modifier = Modifier.weight(1f))
            NumberButton("1", onClick = { onDigitClick("1") }, modifier = Modifier.weight(1f))
            NumberButton("2", onClick = { onDigitClick("2") }, modifier = Modifier.weight(1f))
            NumberButton("3", onClick = { onDigitClick("3") }, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("4", onClick = { onDigitClick("4") }, modifier = Modifier.weight(1f))
            NumberButton("5", onClick = { onDigitClick("5") }, modifier = Modifier.weight(1f))
            NumberButton("6", onClick = { onDigitClick("6") }, modifier = Modifier.weight(1f))
            NumberButton("7", onClick = { onDigitClick("7") }, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Spacer(modifier = Modifier.weight(1f))
            Spacer(modifier = Modifier.weight(1f))
            FunctionButton("C", onClick = onClear, modifier = Modifier.weight(1f))
            FunctionButton("⌫", onClick = onBackspace, modifier = Modifier.weight(1f))
        }
    }
}

@Composable
private fun DecimalNumberPad(
    onDigitClick: (String) -> Unit,
    onClear: () -> Unit,
    onBackspace: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("7", onClick = { onDigitClick("7") }, modifier = Modifier.weight(1f))
            NumberButton("8", onClick = { onDigitClick("8") }, modifier = Modifier.weight(1f))
            NumberButton("9", onClick = { onDigitClick("9") }, modifier = Modifier.weight(1f))
            FunctionButton("C", onClick = onClear, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("4", onClick = { onDigitClick("4") }, modifier = Modifier.weight(1f))
            NumberButton("5", onClick = { onDigitClick("5") }, modifier = Modifier.weight(1f))
            NumberButton("6", onClick = { onDigitClick("6") }, modifier = Modifier.weight(1f))
            FunctionButton("⌫", onClick = onBackspace, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("1", onClick = { onDigitClick("1") }, modifier = Modifier.weight(1f))
            NumberButton("2", onClick = { onDigitClick("2") }, modifier = Modifier.weight(1f))
            NumberButton("3", onClick = { onDigitClick("3") }, modifier = Modifier.weight(1f))
            Spacer(modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Spacer(modifier = Modifier.weight(1f))
            NumberButton("0", onClick = { onDigitClick("0") }, modifier = Modifier.weight(1f))
            Spacer(modifier = Modifier.weight(1f))
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

@Composable
private fun HexNumberPad(
    onDigitClick: (String) -> Unit,
    onClear: () -> Unit,
    onBackspace: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("7", onClick = { onDigitClick("7") }, modifier = Modifier.weight(1f))
            NumberButton("8", onClick = { onDigitClick("8") }, modifier = Modifier.weight(1f))
            NumberButton("9", onClick = { onDigitClick("9") }, modifier = Modifier.weight(1f))
            FunctionButton("C", onClick = onClear, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("4", onClick = { onDigitClick("4") }, modifier = Modifier.weight(1f))
            NumberButton("5", onClick = { onDigitClick("5") }, modifier = Modifier.weight(1f))
            NumberButton("6", onClick = { onDigitClick("6") }, modifier = Modifier.weight(1f))
            FunctionButton("⌫", onClick = onBackspace, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("1", onClick = { onDigitClick("1") }, modifier = Modifier.weight(1f))
            NumberButton("2", onClick = { onDigitClick("2") }, modifier = Modifier.weight(1f))
            NumberButton("3", onClick = { onDigitClick("3") }, modifier = Modifier.weight(1f))
            NumberButton("0", onClick = { onDigitClick("0") }, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("A", onClick = { onDigitClick("A") }, modifier = Modifier.weight(1f))
            NumberButton("B", onClick = { onDigitClick("B") }, modifier = Modifier.weight(1f))
            NumberButton("C", onClick = { onDigitClick("C") }, modifier = Modifier.weight(1f))
            NumberButton("D", onClick = { onDigitClick("D") }, modifier = Modifier.weight(1f))
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            NumberButton("E", onClick = { onDigitClick("E") }, modifier = Modifier.weight(1f))
            NumberButton("F", onClick = { onDigitClick("F") }, modifier = Modifier.weight(1f))
            Spacer(modifier = Modifier.weight(1f))
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

@Composable
private fun NumberButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    Button(
        onClick = {
            onClick()
            FeedbackUtil.buttonPress(context)
        },
        modifier = modifier,
        colors = ButtonDefaults.buttonColors(
            containerColor = NeonCard.copy(alpha = 0.7f),
            contentColor = NeonPink
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold
            )
        )
    }
}

@Composable
private fun FunctionButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    Button(
        onClick = {
            onClick()
            FeedbackUtil.buttonPress(context)
        },
        modifier = modifier,
        colors = ButtonDefaults.buttonColors(
            containerColor = NeonPink.copy(alpha = 0.2f),
            contentColor = NeonPink
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold
            )
        )
    }
}
