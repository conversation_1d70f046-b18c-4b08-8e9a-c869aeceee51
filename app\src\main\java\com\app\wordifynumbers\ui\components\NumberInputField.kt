package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Error
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.foundation.layout.Column
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import com.app.wordifynumbers.ui.theme.*

@Composable
fun NumberInputField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    validation: NumberValidation = NumberValidation.ANY,
    errorMessage: String? = null,
    helpText: String? = null
) {
    val isValid = when (validation) {
        NumberValidation.ANY -> true
        NumberValidation.POSITIVE -> value.toDoubleOrNull()?.let { it > 0 } ?: true
        NumberValidation.POSITIVE_INT -> value.toIntOrNull()?.let { it > 0 } ?: true
        NumberValidation.PERCENTAGE -> value.toDoubleOrNull()?.let { it in 0.0..100.0 } ?: true
        NumberValidation.ROMAN -> value.matches(Regex("^[IVXLCDM]*$"))
        NumberValidation.FRACTION -> value.matches(Regex("^\\d*\\/?\\d*$"))
        NumberValidation.NEGATIVE -> value.toDoubleOrNull()?.let { it < 0 } ?: true
        NumberValidation.ZERO_TO_ONE -> value.toDoubleOrNull()?.let { it in 0.0..1.0 } ?: true
        else -> false
    }

    Column {
        NeonTextField(
            value = value,
            onValueChange = { newValue ->
                when (validation) {
                    NumberValidation.POSITIVE_INT -> {
                        if (newValue.isEmpty() || newValue.all { it.isDigit() }) {
                            onValueChange(newValue)
                        }
                    }
                    NumberValidation.PERCENTAGE -> {
                        if (newValue.isEmpty() || newValue.matches(Regex("^\\d*\\.?\\d*$"))) {
                            onValueChange(newValue)
                        }
                    }
                    NumberValidation.ROMAN -> {
                        if (newValue.uppercase().matches(Regex("^[IVXLCDM]*$"))) {
                            onValueChange(newValue.uppercase())
                        }
                    }
                    NumberValidation.FRACTION -> {
                        if (newValue.matches(Regex("^\\d*\\/?(\\d*)?$"))) {
                            onValueChange(newValue)
                        }
                    }
                    else -> onValueChange(newValue)
                }
            },
            label = { Text(label) },
            isError = !isValid || errorMessage != null,
            trailingIcon = {
                if (!isValid || errorMessage != null) {
                    Icon(
                        Icons.Filled.Error,
                        "error",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            },
            modifier = modifier
        )
        AnimatedVisibility(
            visible = !isValid || errorMessage != null || helpText != null,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Text(
                text = when {
                    errorMessage != null -> errorMessage
                    !isValid -> getDefaultErrorMessage(validation)
                    else -> helpText ?: ""
                },
                color = if (!isValid || errorMessage != null) MaterialTheme.colorScheme.error else NeonHint
            )
        }
    }
}

private fun getDefaultErrorMessage(validation: NumberValidation): String = when (validation) {
    NumberValidation.POSITIVE -> "Please enter a positive number"
    NumberValidation.POSITIVE_INT -> "Please enter a positive integer"
    NumberValidation.PERCENTAGE -> "Please enter a number between 0 and 100"
    NumberValidation.ROMAN -> "Please use only I, V, X, L, C, D, M"
    NumberValidation.FRACTION -> "Please enter a valid fraction (e.g., 3/4)"
    NumberValidation.NEGATIVE -> "Please enter a negative number"
    NumberValidation.ZERO_TO_ONE -> "Please enter a number between 0 and 1"
    NumberValidation.ANY -> ""
    else -> "Invalid input"
}
