package com.app.wordifynumbers.utils

/**
 * Utility class for translating digits (0-9) into different languages
 * Provides translations with pronunciation guides where appropriate
 */
object DigitTranslator {
    private val digits = listOf("0", "1", "2", "3", "4", "5", "6", "7", "8", "9")

    // Map of language to digit translations with pronunciation guides where helpful
    private val translations = mapOf(
        "English" to listOf(
            "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine"
        ),
        "Spanish" to listOf(
            "Cero (say-ro)", "Uno (oo-no)", "Dos (dose)", "Tres (trace)",
            "<PERSON><PERSON><PERSON> (kwah-tro)", "<PERSON><PERSON><PERSON> (seen-ko)", "<PERSON><PERSON> (says)", "Siete (see-eh-teh)",
            "Ocho (oh-cho)", "Nueve (noo-eh-veh)"
        ),
        "French" to listOf(
            "<PERSON><PERSON><PERSON> (zay-roh)", "Un (uh)", "Deux (duh)", "Trois (twah)",
            "Quatre (katr)", "Cinq (sank)", "Six (sees)", "Sept (set)",
            "Huit (weet)", "Neuf (nuhf)"
        ),
        "German" to listOf(
            "Null (nool)", "Eins (eyens)", "Zwei (tsvey)", "Drei (dry)",
            "Vier (feer)", "Fünf (fuenf)", "Sechs (zeks)", "Sieben (zee-ben)",
            "Acht (ahkt)", "Neun (noyn)"
        ),
        "Hindi" to listOf(
            "शून्य (shoonya)", "एक (ek)", "दो (do)", "तीन (teen)",
            "चार (chaar)", "पाँच (paanch)", "छः (chhah)", "सात (saat)",
            "आठ (aath)", "नौ (nau)"
        ),
        "Chinese (Mandarin)" to listOf(
            "零 (líng)", "一 (yī)", "二 (èr)", "三 (sān)",
            "四 (sì)", "五 (wǔ)", "六 (liù)", "七 (qī)",
            "八 (bā)", "九 (jiǔ)"
        ),
        "Japanese" to listOf(
            "零 (rei)", "一 (ichi)", "二 (ni)", "三 (san)",
            "四 (shi/yon)", "五 (go)", "六 (roku)", "七 (shichi/nana)",
            "八 (hachi)", "九 (kyū/ku)"
        ),
        "Arabic" to listOf(
            "صفر (sifr)", "واحد (waahid)", "اثنان (ithnaan)", "ثلاثة (thalaatha)",
            "أربعة (arba'a)", "خمسة (khamsa)", "ستة (sitta)", "سبعة (sab'a)",
            "ثمانية (thamaaniya)", "تسعة (tis'a)"
        ),
        "Russian" to listOf(
            "ноль (nol')", "один (odin)", "два (dva)", "три (tri)",
            "четыре (chetyre)", "пять (pyat')", "шесть (shest')", "семь (sem')",
            "восемь (vosem')", "девять (devyat')"
        ),
        "Portuguese" to listOf(
            "Zero (zeh-roo)", "Um (oong)", "Dois (doish)", "Três (trays)",
            "Quatro (kwah-troo)", "Cinco (seen-koo)", "Seis (saysh)", "Sete (seh-chee)",
            "Oito (oy-too)", "Nove (noh-vee)"
        ),
        "Italian" to listOf(
            "Zero (dzeh-ro)", "Uno (oo-no)", "Due (doo-eh)", "Tre (treh)",
            "Quattro (kwat-tro)", "Cinque (cheen-kweh)", "Sei (sey)", "Sette (set-teh)",
            "Otto (ot-to)", "Nove (no-veh)"
        ),
        "Korean" to listOf(
            "영 (yeong)", "일 (il)", "이 (i)", "삼 (sam)",
            "사 (sa)", "오 (o)", "육 (yuk)", "칠 (chil)",
            "팔 (pal)", "구 (gu)"
        ),
        "Turkish" to listOf(
            "Sıfır (suh-fuhr)", "Bir (beer)", "İki (ee-kee)", "Üç (ooch)",
            "Dört (durt)", "Beş (besh)", "Altı (al-tuh)", "Yedi (yeh-dee)",
            "Sekiz (seh-keez)", "Dokuz (doh-kooz)"
        ),
        "Polish" to listOf(
            "Zero (zeh-ro)", "Jeden (yeh-den)", "Dwa (dva)", "Trzy (tshi)",
            "Cztery (chte-ri)", "Pięć (pyench)", "Sześć (sheshch)", "Siedem (shye-dem)",
            "Osiem (o-shem)", "Dziewięć (je-vyench)"
        ),
        "Ukrainian" to listOf(
            "нуль (nul')", "один (odyn)", "два (dva)", "три (try)",
            "чотири (chotyry)", "п'ять (p'yat')", "шість (shist')", "сім (sim)",
            "вісім (visim)", "дев'ять (dev'yat')"
        ),
        "Dutch" to listOf(
            "Nul (nul)", "Een (ayn)", "Twee (tvay)", "Drie (dree)",
            "Vier (feer)", "Vijf (fayf)", "Zes (zes)", "Zeven (zay-ven)",
            "Acht (acht)", "Negen (nay-gen)"
        ),
        "Thai" to listOf(
            "ศูนย์ (soon)", "หนึ่ง (neung)", "สอง (song)", "สาม (saam)",
            "สี่ (see)", "ห้า (haa)", "หก (hok)", "เจ็ด (jet)",
            "แปด (paet)", "เก้า (kao)"
        ),
        "Indonesian" to listOf(
            "Nol (nohl)", "Satu (sah-too)", "Dua (doo-ah)", "Tiga (tee-gah)",
            "Empat (em-paht)", "Lima (lee-mah)", "Enam (eh-nahm)", "Tujuh (too-jooh)",
            "Delapan (deh-lah-pahn)", "Sembilan (sem-bee-lahn)"
        ),
        "Vietnamese" to listOf(
            "Không (khong)", "Một (moht)", "Hai (hai)", "Ba (ba)",
            "Bốn (bon)", "Năm (nam)", "Sáu (sau)", "Bảy (bay)",
            "Tám (tam)", "Chín (chin)"
        ),
        "Bengali" to listOf(
            "শূন্য (shunno)", "এক (ek)", "দুই (dui)", "তিন (tin)",
            "চার (char)", "পাঁচ (pãch)", "ছয় (chhoy)", "সাত (shat)",
            "আট (at)", "নয় (noy)"
        ),
        "Urdu" to listOf(
            "صفر (sifar)", "ایک (aik)", "دو (do)", "تین (teen)",
            "چار (chaar)", "پانچ (paanch)", "چھ (chay)", "سات (saat)",
            "آٹھ (aath)", "نو (nau)"
        )
    )

    /**
     * Get translations for all digits in the specified language
     * @param language The language to get translations for
     * @return List of digit-translation pairs
     */
    fun getTranslations(language: String): List<Pair<String, String>> {
        val names = translations[language] ?: translations["English"]!!
        return digits.zip(names)
    }

    /**
     * Get all supported languages
     * @return List of supported language names
     */
    fun getSupportedLanguages(): List<String> {
        return translations.keys.toList()
    }

    /**
     * Translate a specific digit to the specified language
     * @param digit The digit to translate (0-9)
     * @param language The target language
     * @return The translated digit or null if invalid
     */
    fun translateDigit(digit: Int, language: String): String? {
        if (digit < 0 || digit > 9) return null
        val names = translations[language] ?: translations["English"]!!
        return names[digit]
    }
}
