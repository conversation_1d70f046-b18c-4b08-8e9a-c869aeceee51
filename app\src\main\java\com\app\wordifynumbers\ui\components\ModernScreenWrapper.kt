package com.app.wordifynumbers.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.theme.*

/**
 * A modern screen wrapper component that provides a consistent layout and styling for all screens.
 * Includes animated background, header, content area, and footer.
 *
 * @param title The title of the screen
 * @param subtitle Optional subtitle or description text
 * @param icon Optional icon to display in the header
 * @param accentColor The accent color for the screen
 * @param showBackButton Whether to show a back button in the header
 * @param onBackClick Callback for when the back button is clicked
 * @param headerActions Optional composable for additional action buttons in the header
 * @param showFooter Whether to show the footer
 * @param content The main content of the screen
 */
@Composable
fun ModernScreenWrapper(
    title: String,
    subtitle: String? = null,
    icon: ImageVector? = null,
    accentColor: Color = NeonGlow,
    showBackButton: Boolean = false,
    onBackClick: () -> Unit = {},
    headerActions: @Composable (RowScope.() -> Unit)? = null,
    showFooter: Boolean = true,
    showInfoButton: Boolean = false,
    showSettingsButton: Boolean = false,
    onInfoClick: () -> Unit = {},
    onSettingsClick: () -> Unit = {},
    content: @Composable () -> Unit
) {
    // Animated background effects
    val infiniteTransition = rememberInfiniteTransition(label = "backgroundAnimation")
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.95f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(8000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseAnimation"
    )

    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 0.7f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(
                        NeonDeepBlue,
                        NeonBackground
                    ),
                    center = Offset(0.5f, 0.5f),
                    radius = 1200f * pulseScale
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 16.dp, bottom = 8.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // New Wordify Header
            WordifyHeader(
                showScreenTitle = true,
                screenTitle = title,
                screenIcon = icon,
                accentColor = accentColor,
                actions = {
                    if (showBackButton) {
                        WordifyHeaderAction(
                            icon = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            accentColor = accentColor,
                            onClick = onBackClick
                        )
                    }

                    if (showInfoButton) {
                        WordifyHeaderAction(
                            icon = Icons.Default.Info,
                            contentDescription = "Information",
                            accentColor = accentColor,
                            onClick = onInfoClick
                        )
                    }

                    if (showSettingsButton) {
                        WordifyHeaderAction(
                            icon = Icons.Default.Settings,
                            contentDescription = "Settings",
                            accentColor = accentColor,
                            onClick = onSettingsClick
                        )
                    }

                    if (headerActions != null) {
                        headerActions()
                    }
                }
            )

            // Main content
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(horizontal = 12.dp)
            ) {
                content()
            }

            // Footer
            if (showFooter) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Made with 499 by Wordify Numbers",
                        style = MaterialTheme.typography.labelLarge.copy(
                            color = accentColor.copy(alpha = 0.7f),
                            fontWeight = FontWeight.Medium
                        )
                    )
                }
            }
        }
    }
}
