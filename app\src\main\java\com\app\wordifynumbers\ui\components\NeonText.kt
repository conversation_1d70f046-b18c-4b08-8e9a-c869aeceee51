package com.app.wordifynumbers.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.theme.*

/**
 * A stylized text component with neon glow effect
 *
 * @param text The text to display
 * @param modifier Modifier for the component
 * @param style TextStyle to apply to the text
 * @param color Primary color for the text
 * @param textAlign Text alignment
 * @param animated Whether to animate the glow effect
 */
@Composable
fun NeonGlowText(
    text: String,
    modifier: Modifier = Modifier,
    style: TextStyle = MaterialTheme.typography.headlineMedium,
    color: Color = NeonGlow,
    textAlign: TextAlign? = null,
    animated: Boolean = true
) {
    val infiniteTransition = rememberInfiniteTransition(label = "textGlow")
    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.5f,
        targetValue = 0.9f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    val actualOpacity = if (animated) glowOpacity else 0.7f

    Box(modifier = modifier) {
        // Glow shadow
        Text(
            text = text,
            style = style,
            color = color,
            textAlign = textAlign,
            modifier = Modifier.graphicsLayer {
                alpha = actualOpacity
            }
        )

        // Main text
        Text(
            text = text,
            style = style,
            color = color,
            textAlign = textAlign
        )
    }
}

/**
 * A section title with neon styling
 *
 * @param title The title text
 * @param modifier Modifier for the component
 * @param accentColor The accent color for the title
 * @param showLine Whether to show a line under the title
 */
@Composable
fun NeonSectionTitle(
    title: String,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    showLine: Boolean = true
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold,
                letterSpacing = 0.5.sp
            ),
            color = accentColor
        )

        if (showLine) {
            val infiniteTransition = rememberInfiniteTransition(label = "lineGlow")
            val glowOpacity by infiniteTransition.animateFloat(
                initialValue = 0.4f,
                targetValue = 0.8f,
                animationSpec = infiniteRepeatable(
                    animation = tween(2000, easing = EaseInOutSine),
                    repeatMode = RepeatMode.Reverse
                ),
                label = "glowAnimation"
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth(0.3f)
                    .height(2.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                accentColor,
                                accentColor.copy(alpha = glowOpacity),
                                accentColor.copy(alpha = 0.1f)
                            )
                        )
                    )
            )
        }
    }
}

/**
 * A highlighted text box with neon styling
 *
 * @param text The text to display
 * @param modifier Modifier for the component
 * @param accentColor The accent color for the highlight
 */
@Composable
fun NeonHighlightText(
    text: String,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow
) {
    val infiniteTransition = rememberInfiniteTransition(label = "highlightGlow")
    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Box(
        modifier = modifier
            .shadow(
                elevation = 4.dp,
                spotColor = accentColor.copy(alpha = glowOpacity * 0.5f),
                ambientColor = accentColor.copy(alpha = glowOpacity * 0.25f),
                shape = RoundedCornerShape(8.dp)
            )
            .clip(RoundedCornerShape(8.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        NeonCard.copy(alpha = 0.8f),
                        NeonCard.copy(alpha = 0.6f)
                    )
                )
            )
            .padding(horizontal = 12.dp, vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Medium
            ),
            color = NeonText
        )
    }
}

/**
 * A descriptive text block with title and content
 *
 * @param title The title text
 * @param content The content text
 * @param modifier Modifier for the component
 * @param accentColor The accent color for the title
 */
@Composable
fun NeonDescriptionText(
    title: String,
    content: String,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = accentColor
        )

        Text(
            text = content,
            style = MaterialTheme.typography.bodyMedium,
            color = NeonText.copy(alpha = 0.8f)
        )
    }
}
