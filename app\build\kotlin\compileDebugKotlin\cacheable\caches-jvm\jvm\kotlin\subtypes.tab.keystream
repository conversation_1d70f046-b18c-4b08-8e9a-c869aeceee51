#androidx.activity.ComponentActivitykotlin.Annotation,com.app.wordifynumbers.ui.screens.DateResultandroidx.lifecycle.ViewModel2kotlinx.serialization.internal.GeneratedSerializerkotlin.Enum,androidx.lifecycle.ViewModelProvider.Factory"androidx.datastore.core.Serializer(com.app.wordifynumbers.data.RoomDatabase1com.app.wordifynumbers.data.SimpleFinanceDatabase1com.app.wordifynumbers.data.SimpleFinanceEntryDao+androidx.lifecycle.DefaultLifecycleObserver,androidx.compose.material.ripple.RippleTheme#androidx.lifecycle.AndroidViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       