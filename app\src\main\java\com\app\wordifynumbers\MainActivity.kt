package com.app.wordifynumbers

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.*
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.ui.MainScreen
import com.app.wordifynumbers.ui.screens.SplashScreen
import com.app.wordifynumbers.ui.theme.WordifyNumbersTheme
import androidx.core.view.WindowCompat
import com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // Define a simple factory for the ViewModel
        val viewModelFactory = object : ViewModelProvider.Factory {
            override fun <T : ViewModel> create(modelClass: Class<T>): T {
                if (modelClass.isAssignableFrom(CalculatorViewModel::class.java)) {
                    @Suppress("UNCHECKED_CAST")
                    return CalculatorViewModel(application) as T
                }
                throw IllegalArgumentException("Unknown ViewModel class")
            }
        }

        setContent {
            val darkTheme = isSystemInDarkTheme()
            // Use the factory with the viewModel() delegate
            val viewModel: CalculatorViewModel = viewModel(factory = viewModelFactory)

            WordifyNumbersTheme(darkTheme = darkTheme) {
                var showSplash by remember { mutableStateOf(true) }

                if (showSplash) {
                    SplashScreen(
                        onSplashFinished = { showSplash = false }
                    )
                } else {
                    MainScreen(calculatorViewModel = viewModel)
                }
            }
        }
    }
}