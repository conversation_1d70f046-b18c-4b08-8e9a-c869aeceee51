package com.app.wordifynumbers.ui.components

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.FeedbackUtil

/**
 * A standardized calculator layout that provides a consistent look and feel
 * across all calculator screens in the app.
 *
 * @param title The title of the calculator
 * @param icon The icon representing the calculator
 * @param accentColor The accent color for the calculator
 * @param inputSection The composable for the input section
 * @param resultSection The composable for the result section
 * @param actionButtons Optional composable for action buttons
 * @param additionalContent Optional additional content to display
 * @param showInfoButton Whether to show the info button
 * @param onInfoClick Action when info button is clicked
 * @param onBackPressed Optional custom back press handler
 */
@Composable
fun StandardCalculatorLayout(
    title: String,
    icon: ImageVector,
    accentColor: Color,
    inputSection: @Composable () -> Unit,
    resultSection: @Composable () -> Unit,
    actionButtons: @Composable (() -> Unit)? = null,
    additionalContent: @Composable (() -> Unit)? = null,
    showInfoButton: Boolean = false,
    onInfoClick: () -> Unit = {},
    onBackPressed: (() -> Unit)? = null
) {
    val context = LocalContext.current

    // Only handle back button if a custom action is provided
    // Otherwise, let MainScreen handle the navigation
    if (onBackPressed != null) {
        BackHandler {
            onBackPressed()
        }
    }
    // Animated background with subtle pulse effect
    val infiniteTransition = rememberInfiniteTransition(label = "calculatorPulse")
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.98f,
        targetValue = 1.02f,
        animationSpec = infiniteRepeatable(
            animation = tween(5000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "calculatorPulseAnimation"
    )

    val glowOpacity by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 0.7f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    val scrollState = rememberScrollState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        NeonDeepBlue.copy(alpha = 0.9f),
                        NeonBackground.copy(alpha = 0.95f)
                    )
                )
            )
    ) {
        // Background glow effect
        Box(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    scaleX = pulseScale
                    scaleY = pulseScale
                }
                .background(
                    Brush.radialGradient(
                        colors = listOf(
                            accentColor.copy(alpha = 0.05f),
                            Color.Transparent
                        ),
                        center = Offset(0.5f, 0.5f),
                        radius = 1000f
                    )
                )
        )

        // Main content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Icon with background
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .shadow(
                                elevation = 8.dp,
                                spotColor = accentColor.copy(alpha = glowOpacity * 0.5f),
                                ambientColor = accentColor.copy(alpha = glowOpacity * 0.25f),
                                shape = RoundedCornerShape(12.dp)
                            )
                            .background(
                                color = NeonCard.copy(alpha = 0.7f),
                                shape = RoundedCornerShape(12.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = icon,
                            contentDescription = null,
                            tint = accentColor,
                            modifier = Modifier.size(28.dp)
                        )
                    }

                    Text(
                        text = title,
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold,
                            color = accentColor,
                            letterSpacing = 0.5.sp
                        )
                    )
                }

                if (showInfoButton) {
                    IconButton(
                        onClick = onInfoClick,
                        modifier = Modifier
                            .size(40.dp)
                            .background(NeonCard.copy(alpha = 0.7f), RoundedCornerShape(12.dp))
                    ) {
                        Icon(
                            Icons.Default.Info,
                            contentDescription = "Information",
                            tint = accentColor
                        )
                    }
                }
            }

            // Animated accent line
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(2.dp)
                    .padding(horizontal = 16.dp)
                    .graphicsLayer {
                        alpha = glowOpacity
                    }
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color.Transparent,
                                accentColor,
                                accentColor,
                                Color.Transparent
                            )
                        )
                    )
            )

            // Main content area with scroll
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Input Section
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 16.dp,
                            spotColor = accentColor.copy(alpha = 0.2f),
                            ambientColor = accentColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(24.dp)
                        ),
                    shape = RoundedCornerShape(24.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        inputSection()
                    }
                }

                // Action Buttons (if provided)
                if (actionButtons != null) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                    ) {
                        actionButtons()
                    }
                }

                // Results Section
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 12.dp,
                            spotColor = accentColor.copy(alpha = 0.2f),
                            ambientColor = accentColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(24.dp)
                        ),
                    shape = RoundedCornerShape(24.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        resultSection()
                    }
                }

                // Additional Content (if provided)
                if (additionalContent != null) {
                    additionalContent()
                }
            }

            // Footer
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Made with 499 by Wordify Numbers",
                    style = MaterialTheme.typography.labelLarge.copy(
                        color = accentColor.copy(alpha = 0.7f),
                        fontWeight = FontWeight.Medium
                    )
                )
            }
        }
    }
}

/**
 * A standardized result display component for calculators
 *
 * @param title The title of the result
 * @param result The result value to display
 * @param accentColor The accent color for styling
 * @param details Optional additional details to display
 */
@Composable
fun CalculatorResultDisplay(
    title: String,
    result: String,
    accentColor: Color,
    details: List<Pair<String, String>> = emptyList()
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Title
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = accentColor
        )

        // Main result
        Text(
            text = result,
            style = MaterialTheme.typography.headlineMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = accentColor,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center
        )

        // Details
        if (details.isNotEmpty()) {
            Divider(
                color = accentColor.copy(alpha = 0.3f),
                modifier = Modifier.padding(vertical = 8.dp)
            )

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                details.forEach { (label, value) ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = label,
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText.copy(alpha = 0.7f)
                        )

                        Text(
                            text = value,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            color = NeonText
                        )
                    }
                }
            }
        }
    }
}

/**
 * A standardized input field for calculators
 *
 * @param label The label for the input field
 * @param value The current value
 * @param onValueChange Callback when value changes
 * @param accentColor The accent color for styling
 * @param modifier Modifier for the component
 * @param supportingText Optional supporting text to display
 * @param isError Whether the input has an error
 * @param singleLine Whether the input should be single line
 */
@Composable
fun CalculatorInputField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    accentColor: Color,
    modifier: Modifier = Modifier,
    supportingText: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    singleLine: Boolean = true,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default
) {
    Column(
        modifier = modifier
    ) {
        NeonTextField(
            value = value,
            onValueChange = onValueChange,
            label = { Text(label) },
            modifier = Modifier.fillMaxWidth(),
            accentColor = accentColor,
            isError = isError,
            singleLine = singleLine,
            keyboardOptions = keyboardOptions
        )

        // Display supporting text if provided
        if (supportingText != null) {
            Box(
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            ) {
                supportingText()
            }
        }
    }
}

/**
 * A standardized action button for calculators
 *
 * @param text The button text
 * @param onClick Callback when button is clicked
 * @param accentColor The accent color for styling
 * @param modifier Modifier for the component
 * @param enabled Whether the button is enabled
 * @param icon Optional icon to display
 */
@Composable
fun CalculatorActionButton(
    text: String,
    onClick: () -> Unit,
    accentColor: Color,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null
) {
    NeonButton(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        accentColor = accentColor
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
            }
            Text(text)
        }
    }
}
