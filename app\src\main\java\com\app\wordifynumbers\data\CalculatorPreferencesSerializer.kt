package com.app.wordifynumbers.data

import androidx.datastore.core.Serializer
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.InputStream
import java.io.OutputStream
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object CalculatorPreferencesSerializer : Serializer<CalculatorPreferences> {
    override val defaultValue: CalculatorPreferences = CalculatorPreferences()
    
    override suspend fun readFrom(input: InputStream): CalculatorPreferences = withContext(Dispatchers.IO) {
        try {
            val jsonString = input.readBytes().decodeToString()
            Json.decodeFromString<CalculatorPreferences>(jsonString)
        } catch (e: SerializationException) {
            e.printStackTrace()
            defaultValue
        }
    }

    override suspend fun writeTo(t: CalculatorPreferences, output: OutputStream) = withContext(Dispatchers.IO) {
        val jsonString = Json.encodeToString(t)
        output.write(jsonString.encodeToByteArray())
    }
}
