package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun ResultBox(
    title: String,
    result: String,
    modifier: Modifier = Modifier,
    animateEntry: Boolean = true
) {
    var showCopiedMessage by remember { mutableStateOf(false) }
    val clipboardManager = LocalClipboardManager.current
    val scope = rememberCoroutineScope()

    val scale by animateFloatAsState(
        targetValue = if (animateEntry) 1f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        )
    )

    LaunchedEffect(result) {
        showCopiedMessage = false
    }

    Column(
        modifier = modifier
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .clip(RoundedCornerShape(16.dp))
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        NeonCard.copy(alpha = 0.3f),
                        NeonCard.copy(alpha = 0.1f)
                    )
                )
            )
            .drawWithContent {
                drawContent()
                // Draw animated border glow
                val glowAlpha = 0.3f + (kotlin.math.sin(
                    System.currentTimeMillis() / 1000.0
                ) * 0.1f).toFloat()
                drawRect(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            NeonGlow.copy(alpha = glowAlpha),
                            NeonGlow.copy(alpha = 0f)
                        )
                    ),
                    size = size.copy(height = 2.dp.toPx())
                )
            }
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = NeonGlow,
                fontWeight = FontWeight.Medium
            )
            
            IconButton(
                onClick = {
                    clipboardManager.setText(AnnotatedString(result))
                    scope.launch {
                        showCopiedMessage = true
                        delay(2000)
                        showCopiedMessage = false
                    }
                }
            ) {
                Icon(
                    imageVector = Icons.Default.ContentCopy,
                    contentDescription = "Copy result",
                    tint = if (showCopiedMessage) NeonGreen else NeonText
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = result,
            style = MaterialTheme.typography.bodyLarge,
            color = NeonText
        )

        AnimatedVisibility(
            visible = showCopiedMessage,
            enter = fadeIn() + expandVertically(),
            exit = fadeOut() + shrinkVertically()
        ) {
            Text(
                text = "Copied to clipboard!",
                style = MaterialTheme.typography.bodySmall,
                color = NeonGreen,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

@Composable
fun NumberFactBox(
    title: String,
    fact: String,
    modifier: Modifier = Modifier
) {
    val glowValue = remember { Animatable(0f) }
    
    LaunchedEffect(fact) {
        glowValue.animateTo(
            targetValue = 1f,
            animationSpec = tween(500)
        )
    }

    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            color = NeonGlow
        )
        Spacer(modifier = Modifier.height(8.dp))
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(12.dp))
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            NeonDeepBlue.copy(alpha = 0.7f),
                            NeonCard.copy(alpha = 0.5f)
                        )
                    )
                )
                .drawWithContent {
                    drawContent()
                    drawCircle(
                        color = NeonGlow.copy(alpha = 0.1f * glowValue.value),
                        radius = size.width * 0.8f,
                        center = Offset(size.width / 2f, size.height / 2f)
                    )
                }
                .padding(16.dp)
        ) {
            Text(
                text = fact,
                style = MaterialTheme.typography.bodyMedium,
                color = NeonText,
                modifier = Modifier.graphicsLayer {
                    alpha = glowValue.value
                }
            )
        }
    }
}

@Composable
fun CalculationResultBox(
    mainResult: String,
    details: List<String>,
    modifier: Modifier = Modifier,
    onSaveComparison: () -> Unit = {}
) {
    var expanded by remember { mutableStateOf(false) }
    
    Column(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        NeonCard.copy(alpha = 0.3f),
                        NeonCard.copy(alpha = 0.1f)
                    )
                )
            )
            .clickable { expanded = !expanded }
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = mainResult,
                style = MaterialTheme.typography.titleLarge,
                color = NeonGlow,
                fontWeight = FontWeight.Bold
            )
            IconButton(onClick = onSaveComparison) {
                Icon(
                    imageVector = Icons.Default.Save,
                    contentDescription = "Save comparison",
                    tint = NeonGlow
                )
            }
        }

        AnimatedVisibility(
            visible = expanded,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Column(
                modifier = Modifier.padding(top = 8.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                details.forEach { detail ->
                    Text(
                        text = detail,
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText.copy(alpha = 0.8f)
                    )
                }
            }
        }
    }
}
