Stack trace:
Frame         Function      Args
0007FFFFA0B0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA0B0, 0007FFFF8FB0) msys-2.0.dll+0x1FE8E
0007FFFFA0B0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA388) msys-2.0.dll+0x67F9
0007FFFFA0B0  000210046832 (000210286019, 0007FFFF9F68, 0007FFFFA0B0, 000000000000) msys-2.0.dll+0x6832
0007FFFFA0B0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA0B0  000210068E24 (0007FFFFA0C0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA390  00021006A225 (0007FFFFA0C0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8B3CB0000 ntdll.dll
7FF8B1E60000 KERNEL32.DLL
7FF8B1460000 KERNELBASE.dll
7FF8B23E0000 USER32.dll
7FF8B1760000 win32u.dll
7FF8B1F30000 GDI32.dll
7FF8B19F0000 gdi32full.dll
7FF8B1350000 msvcp_win.dll
7FF8B18F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8B3500000 advapi32.dll
7FF8B26C0000 msvcrt.dll
7FF8B3BD0000 sechost.dll
7FF8B2580000 RPCRT4.dll
7FF8B1B60000 bcrypt.dll
7FF8B0CF0000 CRYPTBASE.DLL
7FF8B1B90000 bcryptPrimitives.dll
7FF8B2980000 IMM32.DLL
