package com.app.wordifynumbers.util

import kotlin.math.*

/**
 * Utility for evaluating mathematical expressions
 * Supports basic arithmetic, parentheses, and common mathematical functions
 */
object ExpressionEvaluator {
    /**
     * Evaluates a mathematical expression and returns the result
     * @param expression The mathematical expression to evaluate
     * @return The calculated result
     * @throws IllegalArgumentException if the expression is invalid or contains errors
     */
    fun evaluate(expression: String): Double {
        if (expression.isBlank()) {
            throw IllegalArgumentException("Expression cannot be empty")
        }

        // Check for common error patterns
        if (expression.contains("//")) {
            throw ArithmeticException("Invalid division operator: //")
        }
        if (expression.contains("**")) {
            throw IllegalArgumentException("Invalid multiplication operator: **")
        }
        if (expression.contains("++") || expression.contains("--")) {
            throw IllegalArgumentException("Invalid consecutive operators")
        }

        try {
            // Preprocess the expression to handle special cases
            var sanitized = expression
                .replace("×", "*")
                .replace("÷", "/")
                .replace("π", PI.toString())
                .replace("e", E.toString())
                .replace("%", "/100")

            // Handle special functions
            if (sanitized.startsWith("√")) {
                val inner = sanitized.substring(1)
                return sqrt(evaluate(inner))
            }

            // Handle trigonometric functions
            if (sanitized.startsWith("sin(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(4, sanitized.length - 1)
                return sin(evaluate(inner) * PI / 180) // Convert degrees to radians
            }

            if (sanitized.startsWith("cos(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(4, sanitized.length - 1)
                return cos(evaluate(inner) * PI / 180) // Convert degrees to radians
            }

            if (sanitized.startsWith("tan(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(4, sanitized.length - 1)
                return tan(evaluate(inner) * PI / 180) // Convert degrees to radians
            }

            // Handle inverse trigonometric functions
            if (sanitized.startsWith("asin(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(5, sanitized.length - 1)
                return asin(evaluate(inner)) * 180 / PI // Convert radians to degrees
            }

            if (sanitized.startsWith("acos(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(5, sanitized.length - 1)
                return acos(evaluate(inner)) * 180 / PI // Convert radians to degrees
            }

            if (sanitized.startsWith("atan(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(5, sanitized.length - 1)
                return atan(evaluate(inner)) * 180 / PI // Convert radians to degrees
            }

            // Handle logarithmic functions
            if (sanitized.startsWith("log(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(4, sanitized.length - 1)
                return log10(evaluate(inner))
            }

            if (sanitized.startsWith("ln(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(3, sanitized.length - 1)
                return ln(evaluate(inner))
            }

            // Handle other mathematical functions
            if (sanitized.startsWith("sqrt(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(5, sanitized.length - 1)
                return sqrt(evaluate(inner))
            }

            if (sanitized.startsWith("abs(") && sanitized.endsWith(")")) {
                val inner = sanitized.substring(4, sanitized.length - 1)
                return abs(evaluate(inner))
            }

            // Handle power functions
            if (sanitized.contains("^")) {
                val parts = sanitized.split("^", limit = 2)
                if (parts.size == 2) {
                    val base = evaluate(parts[0])
                    val exponent = evaluate(parts[1])
                    return base.pow(exponent)
                }
            }

            // Handle negative numbers at the beginning of the expression
            if (sanitized.startsWith("-")) {
                sanitized = "0" + sanitized
            }

            // Handle consecutive operators
            sanitized = sanitized.replace("--", "+")
            sanitized = sanitized.replace("+-", "-")
            sanitized = sanitized.replace("-+", "-")
            sanitized = sanitized.replace("++", "+")

            // Handle parentheses with implicit multiplication
            sanitized = sanitized.replace(")(", ")*(")

            // Handle implicit multiplication between number and parenthesis
            // e.g., 2(3+4) becomes 2*(3+4)
            sanitized = sanitized.replace(Regex("(\\d)\\("), "$1*(")

            // Handle implicit multiplication between closing parenthesis and number
            // e.g., (3+4)2 becomes (3+4)*2
            sanitized = sanitized.replace(Regex("\\)(\\d)"), ")*$1")

            // Handle negative numbers after operators - fixed implementation
            sanitized = sanitized.replace("*-", "*(-1)*")
            sanitized = sanitized.replace("/-", "/(-1)*")
            sanitized = sanitized.replace("^-", "^(-1)*")
            sanitized = sanitized.replace("+-", "-")
            sanitized = sanitized.replace("--", "+")

            return evaluatePostfix(infixToPostfix(tokenize(sanitized)))
        } catch (e: Exception) {
            when (e) {
                is IllegalArgumentException -> throw e
                is ArithmeticException -> throw e
                else -> throw IllegalArgumentException("Invalid expression: ${e.message}")
            }
        }
    }

    /**
     * Safely evaluates an expression, returning null instead of throwing exceptions
     * @param expression The mathematical expression to evaluate
     * @return The calculated result or null if the expression is invalid
     */
    fun evaluateSafe(expression: String): Double? {
        return try {
            evaluate(expression)
        } catch (e: Exception) {
            null
        }
    }

    private fun tokenize(expression: String): List<String> {
        val tokens = mutableListOf<String>()
        var currentNumber = StringBuilder()
        var previousChar: Char? = null

        fun addNumber() {
            if (currentNumber.isNotEmpty()) {
                tokens.add(currentNumber.toString())
                currentNumber.clear()
            }
        }

        expression.forEach { char ->
            val prev = previousChar
            when {
                char.isDigit() -> currentNumber.append(char)
                char == '.' -> {
                    // Ensure we don't have multiple decimal points in a number
                    if (!currentNumber.contains('.')) {
                        currentNumber.append(char)
                    } else {
                        throw IllegalArgumentException("Invalid number format: multiple decimal points")
                    }
                }
                char.isLetter() -> {
                    addNumber()
                    var functionName = char.toString()
                    if (functionName == "e" || functionName == "π") {
                        tokens.add(functionName)
                    } else {
                        tokens.add("$functionName(")
                    }
                }
                char == '-' && (prev == null || prev in "+(*/^%(") -> {
                    // Handle negative numbers
                    currentNumber.append(char)
                }
                char in "+-*/^%()," -> {
                    addNumber()
                    tokens.add(char.toString())
                }
                !char.isWhitespace() -> throw IllegalArgumentException("Invalid character: $char")
            }
            previousChar = char
        }

        addNumber()
        return tokens
    }

    private fun infixToPostfix(tokens: List<String>): List<String> {
        val output = mutableListOf<String>()
        val operators = ArrayDeque<String>()
        var expectOperand = true

        fun precedence(op: String): Int = when (op) {
            "+", "-" -> 1
            "*", "/", "%" -> 2
            "^" -> 4  // Higher precedence for power operations
            else -> 0
        }

        // Check for right-associativity (only ^ is right-associative)
        fun isRightAssociative(op: String): Boolean = op == "^"

        tokens.forEach { token ->
            when {
                token.toDoubleOrNull() != null -> {
                    output.add(token)
                    expectOperand = false
                }
                token.endsWith("(") -> {
                    operators.addLast(token)
                    expectOperand = true
                }
                token == ")" -> {
                    while (operators.isNotEmpty() && !operators.last().endsWith("(")) {
                        output.add(operators.removeLast())
                    }
                    if (operators.isNotEmpty() && operators.last().endsWith("(")) {
                        val function = operators.removeLast()
                        if (function != "(") {
                            output.add(function.dropLast(1))
                        }
                    } else {
                        throw IllegalArgumentException("Mismatched parentheses")
                    }
                    expectOperand = false
                }
                token in listOf("+", "-", "*", "/", "^", "%") -> {
                    // For right-associative operators like ^, we use > instead of >=
                    // For left-associative operators, we use >= as before
                    while (operators.isNotEmpty() &&
                           !operators.last().endsWith("(") &&
                           ((!isRightAssociative(token) && precedence(operators.last()) >= precedence(token)) ||
                            (isRightAssociative(token) && precedence(operators.last()) > precedence(token)))) {
                        output.add(operators.removeLast())
                    }
                    operators.addLast(token)
                    expectOperand = true
                }
                token in listOf("π", "e") -> {
                    output.add(token)
                    expectOperand = false
                }
                else -> throw IllegalArgumentException("Invalid token: $token")
            }
        }

        while (operators.isNotEmpty()) {
            val op = operators.removeLast()
            if (op.endsWith("(")) throw IllegalArgumentException("Mismatched parentheses")
            output.add(op)
        }

        return output
    }

    private fun evaluatePostfix(tokens: List<String>): Double {
        val stack = ArrayDeque<Double>()

        fun applyOperator(op: String) {
            if (stack.size < 2) throw IllegalArgumentException("Invalid expression")
            val b = stack.removeLast()
            val a = stack.removeLast()

            val result = when (op) {
                "+" -> a + b
                "-" -> a - b
                "*" -> a * b
                "/" -> if (b != 0.0) a / b else throw ArithmeticException("Division by zero")
                "^" -> a.pow(b)
                "%" -> if (b != 0.0) a % b else throw ArithmeticException("Modulo by zero")
                else -> throw IllegalArgumentException("Unknown operator: $op")
            }
            stack.addLast(result)
        }

        fun applyFunction(func: String) {
            if (stack.isEmpty()) throw IllegalArgumentException("Invalid expression")
            val x = stack.removeLast()

            val result = when (func) {
                "sin" -> sin(x * PI / 180) // Convert degrees to radians
                "cos" -> cos(x * PI / 180) // Convert degrees to radians
                "tan" -> tan(x * PI / 180) // Convert degrees to radians
                "asin" -> asin(x) * 180 / PI // Convert radians to degrees
                "acos" -> acos(x) * 180 / PI // Convert radians to degrees
                "atan" -> atan(x) * 180 / PI // Convert radians to degrees
                "log" -> log10(x)
                "ln" -> ln(x)
                "sqrt" -> sqrt(x)
                "abs" -> abs(x)
                "floor" -> floor(x)
                "ceil" -> ceil(x)
                "round" -> round(x).toDouble()
                else -> throw IllegalArgumentException("Unknown function: $func")
            }
            stack.addLast(result)
        }

        tokens.forEach { token ->
            when {
                token.toDoubleOrNull() != null -> stack.addLast(token.toDouble())
                token == "π" -> stack.addLast(PI)
                token == "e" -> stack.addLast(E)
                token.length == 1 && token[0] in "+-*/^%" -> applyOperator(token)
                else -> applyFunction(token)
            }
        }

        if (stack.size != 1) throw IllegalArgumentException("Invalid expression")
        return stack.last()
    }

    fun evaluateWithVariables(expression: String, variables: Map<String, Double>): Double {
        val replaced = expression.replace(Regex("[a-zA-Z][a-zA-Z0-9]*")) { match ->
            variables[match.value]?.toString() ?: throw IllegalArgumentException("Undefined variable: ${match.value}")
        }
        return evaluate(replaced)
    }

    fun validateExpression(expression: String): Boolean {
        return try {
            // Simple validation for basic syntax errors
            var parenCount = 0
            var lastWasOperator = true
            val operators = setOf('+', '-', '×', '÷', '*', '/', '^', '%')

            for (i in expression.indices) {
                val char = expression[i]
                when {
                    char == '(' -> parenCount++
                    char == ')' -> {
                        parenCount--
                        if (parenCount < 0) return false
                    }
                    char in operators -> {
                        // Don't allow consecutive operators except for handling negative numbers
                        if (lastWasOperator && char != '-') return false
                        // Special case for negative numbers after operators
                        if (char == '-' && i > 0 && expression[i-1] in operators) {
                            lastWasOperator = false
                            continue
                        }
                        lastWasOperator = true
                    }
                    else -> lastWasOperator = false
                }
            }

            // Check for unmatched parentheses
            if (parenCount != 0) return false

            // Don't allow expression to end with an operator
            if (expression.isNotEmpty() && expression.last() in operators) return false

            true
        } catch (e: Exception) {
            false
        }
    }
}
