package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.components.NumberValidation

enum class ValidationState {
    VALID,
    INVALID,
    EMPTY
}

private fun isValidRomanNumeral(text: String): Boolean {
    val romanPattern = "^M{0,4}(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})$"
    return text.uppercase().matches(Regex(romanPattern))
}

private fun isValidFraction(text: String): Boolean {
    val fractionPattern = "^\\d+/\\d+$"
    if (!text.matches(Regex(fractionPattern))) return false
    val parts = text.split("/")
    val numerator = parts[0].toIntOrNull() ?: return false
    val denominator = parts[1].toIntOrNull() ?: return false
    return denominator != 0
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NumberInputField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    helpText: String? = null,
    validation: NumberValidation = NumberValidation.ANY,
    isRequired: Boolean = true,
    readOnly: Boolean = false
) {
    var validationState by remember { mutableStateOf(ValidationState.EMPTY) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    fun validate(text: String) {
        if (text.isEmpty()) {
            validationState = if (isRequired) ValidationState.INVALID else ValidationState.EMPTY
            errorMessage = if (isRequired) "This field is required" else null
            return
        }

        when (validation) {
            NumberValidation.ROMAN -> {
                validationState = if (isValidRomanNumeral(text)) {
                    ValidationState.VALID
                } else {
                    errorMessage = "Invalid Roman numeral"
                    ValidationState.INVALID
                }
            }
            NumberValidation.FRACTION -> {
                validationState = if (isValidFraction(text)) {
                    ValidationState.VALID
                } else {
                    errorMessage = "Invalid fraction (use format: n/d)"
                    ValidationState.INVALID
                }
            }
            else -> {
                try {
                    val number = text.toDouble()
                    validationState = when (validation) {
                        NumberValidation.ANY -> ValidationState.VALID
                        NumberValidation.POSITIVE -> if (number > 0) ValidationState.VALID else {
                            errorMessage = "Value must be positive"
                            ValidationState.INVALID
                        }
                        NumberValidation.POSITIVE_INT -> if (number > 0 && number == number.toInt().toDouble()) ValidationState.VALID else {
                            errorMessage = "Value must be a positive integer"
                            ValidationState.INVALID
                        }
                        NumberValidation.NEGATIVE -> if (number < 0) ValidationState.VALID else {
                            errorMessage = "Value must be negative"
                            ValidationState.INVALID
                        }
                        NumberValidation.PERCENTAGE -> if (number in 0.0..100.0) ValidationState.VALID else {
                            errorMessage = "Value must be between 0 and 100"
                            ValidationState.INVALID
                        }
                        NumberValidation.ZERO_TO_ONE -> if (number in 0.0..1.0) ValidationState.VALID else {
                            errorMessage = "Value must be between 0 and 1"
                            ValidationState.INVALID
                        }
                        else -> ValidationState.VALID
                    }
                } catch (e: NumberFormatException) {
                    validationState = ValidationState.INVALID
                    errorMessage = "Invalid number format"
                }
            }
        }
    }

    Column(modifier = modifier) {
        OutlinedTextField(
            value = value,
            onValueChange = {
                onValueChange(it)
                validate(it)
            },
            label = { Text(label) },
            isError = validationState == ValidationState.INVALID,
            enabled = !readOnly,
            singleLine = true,
            keyboardOptions = when (validation) {
                NumberValidation.ROMAN -> KeyboardOptions(keyboardType = KeyboardType.Text)
                NumberValidation.FRACTION -> KeyboardOptions(keyboardType = KeyboardType.Number)
                else -> KeyboardOptions(keyboardType = KeyboardType.Decimal)
            },
            trailingIcon = {
                when (validationState) {
                    ValidationState.VALID -> Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = "Valid input",
                        tint = NeonGreen
                    )
                    ValidationState.INVALID -> Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = "Invalid input",
                        tint = NeonRed
                    )
                    ValidationState.EMPTY -> if (isRequired) Icon(
                        imageVector = Icons.Default.Warning,
                        contentDescription = "Required field",
                        tint = NeonOrange
                    ) else null
                }
            },
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = NeonGlow,
                unfocusedBorderColor = NeonGlow.copy(alpha = 0.5f),
                errorBorderColor = NeonRed,
                focusedLabelColor = NeonGlow,
                unfocusedLabelColor = NeonText.copy(alpha = 0.7f),
                errorLabelColor = NeonRed,
                cursorColor = NeonGlow
            ),
            modifier = Modifier.fillMaxWidth()
        )

        AnimatedVisibility(
            visible = errorMessage != null || helpText != null,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Text(
                text = errorMessage ?: (helpText ?: ""),
                color = when {
                    errorMessage != null -> NeonRed
                    validationState == ValidationState.VALID -> NeonGreen
                    else -> NeonText.copy(alpha = 0.7f)
                },
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            )
        }
    }
}






































// (Removed garbled/duplicate code at end of file)
