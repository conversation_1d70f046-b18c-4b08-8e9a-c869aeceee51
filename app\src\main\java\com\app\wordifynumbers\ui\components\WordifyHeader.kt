package com.app.wordifynumbers.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.theme.*

/**
 * A modern, eye-catching header for the Wordify Numbers app.
 * This header is designed to be used across all screens for consistent branding.
 *
 * @param modifier Modifier for the header
 * @param showScreenTitle Whether to show the screen title below the app name
 * @param screenTitle The title of the current screen
 * @param screenIcon Optional icon for the current screen
 * @param accentColor The accent color for the header
 * @param actions Optional composable for additional action buttons
 */
@Composable
fun WordifyHeader(
    modifier: Modifier = Modifier,
    showScreenTitle: Boolean = true,
    screenTitle: String = "",
    screenIcon: ImageVector? = null,
    accentColor: Color = NeonGlow,
    actions: @Composable (RowScope.() -> Unit)? = null
) {
    // Animated effects
    val infiniteTransition = rememberInfiniteTransition(label = "headerAnimation")

    // Glow animation
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.5f,
        targetValue = 0.9f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    // Pulse animation
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.98f,
        targetValue = 1.02f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseAnimation"
    )

    // Shimmer animation
    val shimmerOffset by infiniteTransition.animateFloat(
        initialValue = -1000f,
        targetValue = 1000f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "shimmerAnimation"
    )

    // Header card with glass effect and glow
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 6.dp)
            .shadow(
                elevation = 10.dp,
                spotColor = accentColor.copy(alpha = 0.3f),
                ambientColor = accentColor.copy(alpha = 0.2f),
                shape = RoundedCornerShape(16.dp)
            ),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = NeonCard.copy(alpha = 0.95f)
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            NeonCard.copy(alpha = 0.95f),
                            NeonCard.copy(alpha = 0.85f)
                        )
                    )
                )
                .drawBehind {
                    // Top glow line
                    drawLine(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color.Transparent,
                                accentColor.copy(alpha = glowAlpha * 0.7f),
                                Color.Transparent
                            )
                        ),
                        start = Offset(size.width * 0.1f, 0f),
                        end = Offset(size.width * 0.9f, 0f),
                        strokeWidth = 2.dp.toPx()
                    )

                    // Shimmer effect
                    drawLine(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color.Transparent,
                                accentColor.copy(alpha = 0.2f),
                                accentColor.copy(alpha = 0.4f),
                                accentColor.copy(alpha = 0.2f),
                                Color.Transparent
                            ),
                            startX = shimmerOffset - 500,
                            endX = shimmerOffset + 500
                        ),
                        start = Offset(0f, size.height - 1.dp.toPx()),
                        end = Offset(size.width, size.height - 1.dp.toPx()),
                        strokeWidth = 2.dp.toPx()
                    )
                }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp, horizontal = 12.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // App name with logo effect
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Left section: App logo and name
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier
                            .weight(1f)
                            .padding(end = 8.dp)
                    ) {
                        // App logo icon with glow effect
                        Box(
                            modifier = Modifier
                                .size(36.dp)
                                .graphicsLayer {
                                    scaleX = pulseScale
                                    scaleY = pulseScale
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Numbers,
                                contentDescription = "Wordify Numbers",
                                tint = accentColor,
                                modifier = Modifier.size(28.dp)
                            )
                        }

                        // App name with stylized text - using a single line with nowrap
                        Text(
                            text = "Wordify Numbers",
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold,
                                letterSpacing = 0.5.sp,
                                shadow = Shadow(
                                    color = accentColor.copy(alpha = glowAlpha * 0.5f),
                                    offset = Offset(0f, 0f),
                                    blurRadius = 8f
                                )
                            ),
                            color = accentColor, // Use accent color instead of NeonText for better visibility
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f, fill = false)
                        )
                    }

                    // Right section: Action buttons
                    if (actions != null) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(4.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            content = actions,
                            modifier = Modifier.padding(start = 4.dp)
                        )
                    }
                }

                // Screen title section (if enabled)
                if (showScreenTitle && screenTitle.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(6.dp))

                    // Divider with glow effect
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(0.95f)
                            .height(1.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        accentColor.copy(alpha = 0.3f),
                                        accentColor.copy(alpha = 0.5f),
                                        accentColor.copy(alpha = 0.3f),
                                        Color.Transparent
                                    )
                                )
                            )
                    )

                    Spacer(modifier = Modifier.height(6.dp))

                    // Screen title with icon in a card for better visibility
                    Card(
                        colors = CardDefaults.cardColors(
                            containerColor = accentColor.copy(alpha = 0.1f)
                        ),
                        shape = RoundedCornerShape(12.dp),
                        modifier = Modifier.padding(horizontal = 8.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 6.dp, horizontal = 12.dp)
                        ) {
                            if (screenIcon != null) {
                                Icon(
                                    imageVector = screenIcon,
                                    contentDescription = null,
                                    tint = accentColor,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                            }

                            Text(
                                text = screenTitle,
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 18.sp
                                ),
                                color = accentColor,
                                maxLines = 1,
                                overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * A standardized action button for the header.
 */
@Composable
fun WordifyHeaderAction(
    icon: ImageVector,
    contentDescription: String,
    accentColor: Color = NeonGlow,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(34.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(accentColor.copy(alpha = 0.1f))
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = accentColor,
            modifier = Modifier.size(20.dp)
        )
    }
}
