package com.app.wordifynumbers.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.doublePreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.app.wordifynumbers.util.CalculationHistoryItem
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.Json
import java.io.IOException

val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "calculator_settings")

class DataStoreManager(private val context: Context) {

    // Preference keys
    private object PreferencesKeys {
        val DECIMAL_PLACES = intPreferencesKey("decimal_places")
        val USE_SCIENTIFIC_NOTATION = booleanPreferencesKey("use_scientific_notation")
        val USE_THOUSANDS_SEPARATOR = booleanPreferencesKey("use_thousands_separator")
        val HISTORY_SIZE = intPreferencesKey("history_size")
        val DEFAULT_MODE = stringPreferencesKey("default_mode")
        val CALCULATION_HISTORY = stringPreferencesKey("calculation_history")
        val MEMORY_VALUE = doublePreferencesKey("memory_value")
    }

    // Get calculator preferences
    val calculatorPreferencesFlow: Flow<CalculatorPreferences> = context.dataStore.data
        .catch<Preferences> { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            CalculatorPreferences(
                decimalPlaces = preferences[PreferencesKeys.DECIMAL_PLACES] ?: 2,
                useScientificNotation = preferences[PreferencesKeys.USE_SCIENTIFIC_NOTATION] ?: false,
                useThousandsSeparator = preferences[PreferencesKeys.USE_THOUSANDS_SEPARATOR] ?: true,
                historySize = preferences[PreferencesKeys.HISTORY_SIZE] ?: 10,
                defaultMode = preferences[PreferencesKeys.DEFAULT_MODE] ?: "STANDARD",
                memoryValue = preferences[PreferencesKeys.MEMORY_VALUE] ?: 0.0
            )
        }

    // Get calculation history
    val calculationHistoryFlow: Flow<List<CalculationHistoryItem>> = context.dataStore.data
        .catch<Preferences> { exception ->
            if (exception is IOException) {
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            val historyJson = preferences[PreferencesKeys.CALCULATION_HISTORY] ?: ""
            if (historyJson.isEmpty()) {
                emptyList()
            } else {
                try {
                    Json.decodeFromString(
                        ListSerializer(CalculationHistoryItem.serializer()),
                        historyJson
                    )
                } catch (e: Exception) {
                    emptyList()
                }
            }
        }

    // Save calculator preferences
    suspend fun saveCalculatorPreferences(preferences: CalculatorPreferences) {
        context.dataStore.edit { prefs ->
            prefs[PreferencesKeys.DECIMAL_PLACES] = preferences.decimalPlaces
            prefs[PreferencesKeys.USE_SCIENTIFIC_NOTATION] = preferences.useScientificNotation
            prefs[PreferencesKeys.USE_THOUSANDS_SEPARATOR] = preferences.useThousandsSeparator
            prefs[PreferencesKeys.HISTORY_SIZE] = preferences.historySize
            prefs[PreferencesKeys.DEFAULT_MODE] = preferences.defaultMode
            prefs[PreferencesKeys.MEMORY_VALUE] = preferences.memoryValue
        }
    }

    // Save calculation history
    suspend fun saveCalculationHistory(history: List<CalculationHistoryItem>) {
        context.dataStore.edit { prefs ->
            val historyJson = Json.encodeToString(
                ListSerializer(CalculationHistoryItem.serializer()),
                history
            )
            prefs[PreferencesKeys.CALCULATION_HISTORY] = historyJson
        }
    }

    // Clear calculation history
    suspend fun clearCalculationHistory() {
        context.dataStore.edit { prefs ->
            prefs[PreferencesKeys.CALCULATION_HISTORY] = ""
        }
    }
}