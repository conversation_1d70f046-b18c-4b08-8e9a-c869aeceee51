package com.app.wordifynumbers.performance

import android.app.Application
import android.content.Context
import android.os.Build
import android.os.StrictMode
import androidx.compose.runtime.*
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import com.app.wordifynumbers.BuildConfig
import kotlinx.coroutines.*
import java.lang.ref.WeakReference
import kotlin.system.measureTimeMillis

/**
 * Performance manager for optimizing app startup, memory usage, and overall performance
 * in compliance with Android performance best practices and Google Play requirements.
 */
object PerformanceManager : DefaultLifecycleObserver {
    
    private var appStartTime: Long = 0
    private var isAppInForeground = false
    private val performanceMetrics = mutableMapOf<String, Long>()
    
    /**
     * Initialize performance monitoring
     */
    fun initialize(application: Application) {
        appStartTime = System.currentTimeMillis()
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        
        // Enable StrictMode in debug builds
        if (BuildConfig.DEBUG) {
            enableStrictMode()
        }
        
        // Setup memory monitoring
        setupMemoryMonitoring(application)
    }
    
    /**
     * Enable StrictMode for development builds
     */
    private fun enableStrictMode() {
        StrictMode.setThreadPolicy(
            StrictMode.ThreadPolicy.Builder()
                .detectDiskReads()
                .detectDiskWrites()
                .detectNetwork()
                .detectCustomSlowCalls()
                .penaltyLog()
                .build()
        )
        
        StrictMode.setVmPolicy(
            StrictMode.VmPolicy.Builder()
                .detectLeakedSqlLiteObjects()
                .detectLeakedClosableObjects()
                .detectLeakedRegistrationObjects()
                .detectActivityLeaks()
                .detectFileUriExposure()
                .penaltyLog()
                .build()
        )
    }
    
    /**
     * Setup memory monitoring
     */
    private fun setupMemoryMonitoring(context: Context) {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        
        performanceMetrics["max_memory"] = maxMemory / 1024 / 1024 // MB
        performanceMetrics["total_memory"] = totalMemory / 1024 / 1024 // MB
        performanceMetrics["free_memory"] = freeMemory / 1024 / 1024 // MB
        performanceMetrics["used_memory"] = (totalMemory - freeMemory) / 1024 / 1024 // MB
    }
    
    /**
     * Measure and log operation performance
     */
    fun <T> measurePerformance(operationName: String, operation: () -> T): T {
        val result: T
        val executionTime = measureTimeMillis {
            result = operation()
        }

        performanceMetrics[operationName] = executionTime

        if (BuildConfig.DEBUG) {
            println("Performance: $operationName took ${executionTime}ms")
        }

        return result
    }
    
    /**
     * Get current memory usage
     */
    fun getCurrentMemoryUsage(): MemoryInfo {
        val runtime = Runtime.getRuntime()
        val maxMemory = runtime.maxMemory()
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        
        return MemoryInfo(
            maxMemoryMB = maxMemory / 1024 / 1024,
            totalMemoryMB = totalMemory / 1024 / 1024,
            freeMemoryMB = freeMemory / 1024 / 1024,
            usedMemoryMB = usedMemory / 1024 / 1024,
            memoryUsagePercentage = (usedMemory.toFloat() / maxMemory.toFloat() * 100).toInt()
        )
    }
    
    /**
     * Force garbage collection if memory usage is high
     */
    fun optimizeMemoryIfNeeded() {
        val memoryInfo = getCurrentMemoryUsage()
        if (memoryInfo.memoryUsagePercentage > 80) {
            System.gc()
            if (BuildConfig.DEBUG) {
                println("Performance: Forced garbage collection due to high memory usage (${memoryInfo.memoryUsagePercentage}%)")
            }
        }
    }
    
    /**
     * Get app startup time
     */
    fun getAppStartupTime(): Long {
        return System.currentTimeMillis() - appStartTime
    }
    
    /**
     * Lifecycle callbacks
     */
    override fun onStart(owner: LifecycleOwner) {
        isAppInForeground = true
        performanceMetrics["app_foreground_time"] = System.currentTimeMillis()
    }
    
    override fun onStop(owner: LifecycleOwner) {
        isAppInForeground = false
        performanceMetrics["app_background_time"] = System.currentTimeMillis()
    }
    
    /**
     * Check if app is in foreground
     */
    fun isAppInForeground(): Boolean = isAppInForeground
    
    /**
     * Record performance metric
     */
    fun recordPerformanceMetric(key: String, value: Long) {
        performanceMetrics[key] = value
    }

    /**
     * Get all performance metrics
     */
    fun getPerformanceMetrics(): Map<String, Long> = performanceMetrics.toMap()
    
    /**
     * Data class for memory information
     */
    data class MemoryInfo(
        val maxMemoryMB: Long,
        val totalMemoryMB: Long,
        val freeMemoryMB: Long,
        val usedMemoryMB: Long,
        val memoryUsagePercentage: Int
    )
}

/**
 * Compose performance utilities
 */
object ComposePerformanceUtils {
    
    /**
     * Remember expensive calculations with proper keys
     */
    @Composable
    fun <T> rememberExpensiveCalculation(
        key: Any?,
        calculation: () -> T
    ): T {
        return remember(key) {
            PerformanceManager.measurePerformance("expensive_calculation") {
                calculation()
            }
        }
    }
    
    /**
     * Lazy loading for heavy content
     */
    @Composable
    fun <T> LazyContent(
        condition: Boolean,
        content: @Composable () -> T
    ): T? {
        return if (condition) {
            content()
        } else {
            null
        }
    }
    
    /**
     * Debounced state for frequent updates
     */
    @Composable
    fun <T> rememberDebouncedState(
        value: T,
        delayMillis: Long = 300L
    ): State<T> {
        val debouncedValue = remember { mutableStateOf(value) }
        
        LaunchedEffect(value) {
            delay(delayMillis)
            debouncedValue.value = value
        }
        
        return debouncedValue
    }
}

/**
 * Background task manager for efficient background processing
 */
object BackgroundTaskManager {
    
    private val backgroundScope = CoroutineScope(
        Dispatchers.IO + SupervisorJob()
    )
    
    /**
     * Execute background task with proper error handling
     */
    fun executeBackgroundTask(
        task: suspend () -> Unit,
        onError: (Throwable) -> Unit = {}
    ) {
        backgroundScope.launch {
            try {
                val executionTime = measureTimeMillis {
                    task()
                }
                PerformanceManager.recordPerformanceMetric("background_task", executionTime)
            } catch (e: Exception) {
                onError(e)
            }
        }
    }
    
    /**
     * Execute background task with result callback
     */
    fun <T> executeBackgroundTaskWithResult(
        task: suspend () -> T,
        onResult: (T) -> Unit,
        onError: (Throwable) -> Unit = {}
    ) {
        backgroundScope.launch {
            try {
                val result: T
                val executionTime = measureTimeMillis {
                    result = task()
                }
                PerformanceManager.recordPerformanceMetric("background_task_with_result", executionTime)
                withContext(Dispatchers.Main) {
                    onResult(result)
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    onError(e)
                }
            }
        }
    }
    
    /**
     * Cancel all background tasks
     */
    fun cancelAllTasks() {
        backgroundScope.coroutineContext.cancelChildren()
    }
}

/**
 * Image loading optimization
 */
object ImageOptimization {
    
    /**
     * Calculate optimal image sample size
     */
    fun calculateInSampleSize(
        originalWidth: Int,
        originalHeight: Int,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        var inSampleSize = 1
        
        if (originalHeight > reqHeight || originalWidth > reqWidth) {
            val halfHeight = originalHeight / 2
            val halfWidth = originalWidth / 2
            
            while ((halfHeight / inSampleSize) >= reqHeight && 
                   (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2
            }
        }
        
        return inSampleSize
    }
}

/**
 * Cache manager for efficient data caching
 */
object CacheManager {
    
    private val memoryCache = mutableMapOf<String, WeakReference<Any>>()
    
    /**
     * Put data in memory cache
     */
    fun <T> put(key: String, value: T) {
        memoryCache[key] = WeakReference(value)
    }
    
    /**
     * Get data from memory cache
     */
    @Suppress("UNCHECKED_CAST")
    fun <T> get(key: String): T? {
        return memoryCache[key]?.get() as? T
    }
    
    /**
     * Clear memory cache
     */
    fun clear() {
        memoryCache.clear()
    }
    
    /**
     * Remove expired entries
     */
    fun cleanup() {
        val iterator = memoryCache.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (entry.value.get() == null) {
                iterator.remove()
            }
        }
    }
}
