package com.app.wordifynumbers.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.app.wordifynumbers.ui.theme.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale

/**
 * ViewModel for the Programmer Calculator screen
 * Handles business logic for number base conversions and bitwise operations
 */
class ProgrammerCalculatorViewModel : ViewModel() {
    
    // Input value
    private val _inputValue = MutableStateFlow("")
    val inputValue: StateFlow<String> = _inputValue.asStateFlow()
    
    // Selected number base
    private val _inputBase = MutableStateFlow(NumberBase.DECIMAL)
    val inputBase: StateFlow<NumberBase> = _inputBase.asStateFlow()
    
    // Conversion results
    private val _binaryValue = MutableStateFlow("")
    val binaryValue: StateFlow<String> = _binaryValue.asStateFlow()
    
    private val _octalValue = MutableStateFlow("")
    val octalValue: StateFlow<String> = _octalValue.asStateFlow()
    
    private val _decimalValue = MutableStateFlow("")
    val decimalValue: StateFlow<String> = _decimalValue.asStateFlow()
    
    private val _hexValue = MutableStateFlow("")
    val hexValue: StateFlow<String> = _hexValue.asStateFlow()
    
    // Error state
    private val _isError = MutableStateFlow(false)
    val isError: StateFlow<Boolean> = _isError.asStateFlow()
    
    // Bitwise operation parameters
    private val _secondNumber = MutableStateFlow("1")
    val secondNumber: StateFlow<String> = _secondNumber.asStateFlow()
    
    private val _shiftAmount = MutableStateFlow("1")
    val shiftAmount: StateFlow<String> = _shiftAmount.asStateFlow()
    
    // Bit representation
    private val _bitRepresentation = MutableStateFlow("")
    val bitRepresentation: StateFlow<String> = _bitRepresentation.asStateFlow()
    
    // Selected locale for number formatting
    private val _selectedLocale = MutableStateFlow(Locale.getDefault())
    val selectedLocale: StateFlow<Locale> = _selectedLocale.asStateFlow()
    
    // Available locales for number formatting
    private val _availableLocales = MutableStateFlow(getCommonLocales())
    val availableLocales: StateFlow<List<Locale>> = _availableLocales.asStateFlow()
    
    // Word representation
    private val _wordRepresentation = MutableStateFlow("")
    val wordRepresentation: StateFlow<String> = _wordRepresentation.asStateFlow()
    
    /**
     * Update the input value
     */
    fun updateInputValue(value: String) {
        // Validate input based on selected base
        val isValid = when (_inputBase.value) {
            NumberBase.BINARY -> value.all { it in "01" }
            NumberBase.OCTAL -> value.all { it in "01234567" }
            NumberBase.DECIMAL -> value.all { it.isDigit() }
            NumberBase.HEXADECIMAL -> value.all { it.isDigit() || it.lowercase() in "abcdef" }
        }
        
        if (isValid) {
            _inputValue.value = value
            convertNumber()
        }
    }
    
    /**
     * Set the input base
     */
    fun setInputBase(base: NumberBase) {
        _inputBase.value = base
        // Clear input when changing base
        _inputValue.value = ""
        convertNumber()
    }
    
    /**
     * Update the second number for bitwise operations
     */
    fun updateSecondNumber(value: String) {
        if (value.all { it.isDigit() }) {
            _secondNumber.value = value
        }
    }
    
    /**
     * Update the shift amount for bitwise shift operations
     */
    fun updateShiftAmount(value: String) {
        if (value.all { it.isDigit() }) {
            _shiftAmount.value = value
        }
    }
    
    /**
     * Set the selected locale for number formatting
     */
    fun setSelectedLocale(locale: Locale) {
        _selectedLocale.value = locale
    }
    
    /**
     * Add a digit to the input value
     */
    fun addDigit(digit: String) {
        _inputValue.update { it + digit }
        convertNumber()
    }
    
    /**
     * Clear the input value
     */
    fun clearInput() {
        _inputValue.value = ""
        convertNumber()
    }
    
    /**
     * Remove the last digit from the input value
     */
    fun backspace() {
        if (_inputValue.value.isNotEmpty()) {
            _inputValue.update { it.dropLast(1) }
            convertNumber()
        }
    }
    
    /**
     * Convert the input number to all bases
     */
    fun convertNumber() {
        viewModelScope.launch {
            try {
                if (_inputValue.value.isEmpty()) {
                    _binaryValue.value = ""
                    _octalValue.value = ""
                    _decimalValue.value = ""
                    _hexValue.value = ""
                    _bitRepresentation.value = ""
                    _wordRepresentation.value = ""
                    _isError.value = false
                    return@launch
                }
                
                val decimalNum = when (_inputBase.value) {
                    NumberBase.BINARY -> _inputValue.value.toLongOrNull(2)
                    NumberBase.OCTAL -> _inputValue.value.toLongOrNull(8)
                    NumberBase.DECIMAL -> _inputValue.value.toLongOrNull(10)
                    NumberBase.HEXADECIMAL -> _inputValue.value.toLongOrNull(16)
                } ?: throw NumberFormatException("Invalid number format")
                
                _binaryValue.value = decimalNum.toString(2)
                _octalValue.value = decimalNum.toString(8)
                _decimalValue.value = decimalNum.toString(10)
                _hexValue.value = decimalNum.toString(16).uppercase()
                
                // Generate bit representation
                _bitRepresentation.value = generateBitRepresentation(decimalNum)
                
                // Generate word representation
                _wordRepresentation.value = generateWordRepresentation(decimalNum)
                
                _isError.value = false
            } catch (e: Exception) {
                _isError.value = true
                _binaryValue.value = ""
                _octalValue.value = ""
                _decimalValue.value = ""
                _hexValue.value = ""
                _bitRepresentation.value = ""
                _wordRepresentation.value = ""
            }
        }
    }
    
    /**
     * Generate a bit representation of the number
     */
    private fun generateBitRepresentation(number: Long): String {
        val binary = number.toString(2)
        val paddedBinary = binary.padStart(((binary.length + 7) / 8) * 8, '0')
        
        return paddedBinary.chunked(8).joinToString(" ")
    }
    
    /**
     * Generate a word representation of the number
     */
    private fun generateWordRepresentation(number: Long): String {
        // Simple implementation - can be expanded with more languages
        return when {
            number == 0L -> "Zero"
            number == 1L -> "One"
            number == 2L -> "Two"
            number == 3L -> "Three"
            number == 4L -> "Four"
            number == 5L -> "Five"
            number == 6L -> "Six"
            number == 7L -> "Seven"
            number == 8L -> "Eight"
            number == 9L -> "Nine"
            number == 10L -> "Ten"
            number < 0 -> "Negative " + generateWordRepresentation(-number)
            number < 20 -> {
                when (number.toInt()) {
                    11 -> "Eleven"
                    12 -> "Twelve"
                    13 -> "Thirteen"
                    14 -> "Fourteen"
                    15 -> "Fifteen"
                    16 -> "Sixteen"
                    17 -> "Seventeen"
                    18 -> "Eighteen"
                    19 -> "Nineteen"
                    else -> ""
                }
            }
            number < 100 -> {
                val tens = when ((number / 10).toInt()) {
                    2 -> "Twenty"
                    3 -> "Thirty"
                    4 -> "Forty"
                    5 -> "Fifty"
                    6 -> "Sixty"
                    7 -> "Seventy"
                    8 -> "Eighty"
                    9 -> "Ninety"
                    else -> ""
                }
                
                val ones = number % 10
                if (ones == 0L) tens else "$tens-${generateWordRepresentation(ones)}"
            }
            number < 1000 -> {
                val hundreds = generateWordRepresentation(number / 100)
                val remainder = number % 100
                
                if (remainder == 0L) {
                    "$hundreds Hundred"
                } else {
                    "$hundreds Hundred and ${generateWordRepresentation(remainder)}"
                }
            }
            number < 1_000_000 -> {
                val thousands = generateWordRepresentation(number / 1000)
                val remainder = number % 1000
                
                if (remainder == 0L) {
                    "$thousands Thousand"
                } else {
                    "$thousands Thousand, ${generateWordRepresentation(remainder)}"
                }
            }
            number < 1_000_000_000 -> {
                val millions = generateWordRepresentation(number / 1_000_000)
                val remainder = number % 1_000_000
                
                if (remainder == 0L) {
                    "$millions Million"
                } else {
                    "$millions Million, ${generateWordRepresentation(remainder)}"
                }
            }
            else -> "Very Large Number"
        }
    }
    
    /**
     * Calculate NOT operation
     */
    fun calculateNOT(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        return decimalLong.inv().toString()
    }
    
    /**
     * Calculate left shift by 1
     */
    fun calculateLeftShift1(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        return (decimalLong shl 1).toString()
    }
    
    /**
     * Calculate right shift by 1
     */
    fun calculateRightShift1(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        return (decimalLong shr 1).toString()
    }
    
    /**
     * Calculate AND operation
     */
    fun calculateAND(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        val secondLong = _secondNumber.value.toLongOrNull() ?: 1L
        return (decimalLong and secondLong).toString()
    }
    
    /**
     * Calculate OR operation
     */
    fun calculateOR(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        val secondLong = _secondNumber.value.toLongOrNull() ?: 1L
        return (decimalLong or secondLong).toString()
    }
    
    /**
     * Calculate XOR operation
     */
    fun calculateXOR(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        val secondLong = _secondNumber.value.toLongOrNull() ?: 1L
        return (decimalLong xor secondLong).toString()
    }
    
    /**
     * Calculate custom left shift
     */
    fun calculateCustomLeftShift(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        val shiftValue = _shiftAmount.value.toIntOrNull() ?: 1
        return (decimalLong shl shiftValue).toString()
    }
    
    /**
     * Calculate custom right shift
     */
    fun calculateCustomRightShift(): String {
        val decimalLong = _decimalValue.value.toLongOrNull() ?: 0L
        val shiftValue = _shiftAmount.value.toIntOrNull() ?: 1
        return (decimalLong shr shiftValue).toString()
    }
    
    /**
     * Format a number according to the selected locale
     */
    fun formatNumber(value: Long): String {
        return try {
            val formatter = NumberFormat.getNumberInstance(_selectedLocale.value)
            formatter.maximumFractionDigits = 0
            
            formatter.format(value)
        } catch (e: Exception) {
            // Fallback to simple formatting if locale-specific formatting fails
            value.toString()
        }
    }
    
    /**
     * Get a list of common locales for number formatting
     */
    private fun getCommonLocales(): List<Locale> {
        return listOf(
            Locale.US,                  // English (US)
            Locale.UK,                  // English (UK)
            Locale.GERMANY,             // German
            Locale.FRANCE,              // French
            Locale.JAPAN,               // Japanese
            Locale.CHINA,               // Chinese
            Locale.KOREA,               // Korean
            Locale("es", "ES"),         // Spanish
            Locale("it", "IT"),         // Italian
            Locale("ru", "RU"),         // Russian
            Locale("ar", "SA"),         // Arabic
            Locale("hi", "IN"),         // Hindi
            Locale("pt", "BR"),         // Portuguese (Brazil)
            Locale("tr", "TR"),         // Turkish
            Locale("nl", "NL"),         // Dutch
            Locale("sv", "SE"),         // Swedish
            Locale("pl", "PL"),         // Polish
            Locale("th", "TH"),         // Thai
            Locale("ur", "PK"),         // Urdu
            Locale("vi", "VN")          // Vietnamese
        )
    }
    
    /**
     * Get the display name for a locale
     */
    fun getLocaleDisplayName(locale: Locale): String {
        return "${locale.getDisplayLanguage(Locale.US)} (${locale.country})"
    }
}

/**
 * Enum representing number bases
 */
enum class NumberBase(val displayName: String, val radix: Int) {
    BINARY("Binary", 2),
    OCTAL("Octal", 8),
    DECIMAL("Decimal", 10),
    HEXADECIMAL("Hexadecimal", 16)
}
