package com.app.wordifynumbers.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.app.wordifynumbers.ui.theme.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.Locale
import kotlin.math.pow

/**
 * ViewModel for the Health Calculator screen
 * Handles business logic for health-related calculations
 */
class HealthCalculatorViewModel : ViewModel() {

    // Weight input
    private val _weight = MutableStateFlow("")
    val weight: StateFlow<String> = _weight.asStateFlow()

    // Height input
    private val _height = MutableStateFlow("")
    val height: StateFlow<String> = _height.asStateFlow()

    // Weight unit (kg or lb)
    private val _weightUnit = MutableStateFlow(WeightUnit.KG)
    val weightUnit: StateFlow<WeightUnit> = _weightUnit.asStateFlow()

    // Height unit (cm or in)
    private val _heightUnit = MutableStateFlow(HeightUnit.CM)
    val heightUnit: StateFlow<HeightUnit> = _heightUnit.asStateFlow()

    // BMI calculation result
    private val _bmiResult = MutableStateFlow<BMIResult?>(null)
    val bmiResult: StateFlow<BMIResult?> = _bmiResult.asStateFlow()

    // Error message
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Selected locale for number formatting
    private val _selectedLocale = MutableStateFlow(Locale.getDefault())
    val selectedLocale: StateFlow<Locale> = _selectedLocale.asStateFlow()

    // Available locales for number formatting
    private val _availableLocales = MutableStateFlow(getCommonLocales())
    val availableLocales: StateFlow<List<Locale>> = _availableLocales.asStateFlow()

    // Selected calculator mode
    private val _calculatorMode = MutableStateFlow(HealthCalculatorMode.BMI)
    val calculatorMode: StateFlow<HealthCalculatorMode> = _calculatorMode.asStateFlow()

    // Age input for other calculators
    private val _age = MutableStateFlow("")
    val age: StateFlow<String> = _age.asStateFlow()

    // Gender selection for other calculators
    private val _gender = MutableStateFlow(Gender.MALE)
    val gender: StateFlow<Gender> = _gender.asStateFlow()

    // Activity level for other calculators
    private val _activityLevel = MutableStateFlow(ActivityLevel.MODERATE)
    val activityLevel: StateFlow<ActivityLevel> = _activityLevel.asStateFlow()

    // Waist circumference for WHR calculator
    private val _waist = MutableStateFlow("")
    val waist: StateFlow<String> = _waist.asStateFlow()

    // Hip circumference for WHR calculator
    private val _hip = MutableStateFlow("")
    val hip: StateFlow<String> = _hip.asStateFlow()

    // Waist and hip unit (cm or in)
    private val _waistHipUnit = MutableStateFlow(HeightUnit.CM)
    val waistHipUnit: StateFlow<HeightUnit> = _waistHipUnit.asStateFlow()

    // WHR calculation result
    private val _whrResult = MutableStateFlow<WHRResult?>(null)
    val whrResult: StateFlow<WHRResult?> = _whrResult.asStateFlow()

    // BMR calculation result
    private val _bmrResult = MutableStateFlow<BMRResult?>(null)
    val bmrResult: StateFlow<BMRResult?> = _bmrResult.asStateFlow()

    /**
     * Update weight input
     */
    fun updateWeight(weight: String) {
        _weight.value = weight
        clearResults()
    }

    /**
     * Update height input
     */
    fun updateHeight(height: String) {
        _height.value = height
        clearResults()
    }

    /**
     * Update age input
     */
    fun updateAge(age: String) {
        _age.value = age
        clearResults()
    }

    /**
     * Update waist circumference input
     */
    fun updateWaist(waist: String) {
        _waist.value = waist
        clearResults()
    }

    /**
     * Update hip circumference input
     */
    fun updateHip(hip: String) {
        _hip.value = hip
        clearResults()
    }

    /**
     * Set weight unit
     */
    fun setWeightUnit(unit: WeightUnit) {
        _weightUnit.value = unit
        clearResults()
    }

    /**
     * Set height unit
     */
    fun setHeightUnit(unit: HeightUnit) {
        _heightUnit.value = unit
        clearResults()
    }

    /**
     * Set waist and hip unit
     */
    fun setWaistHipUnit(unit: HeightUnit) {
        _waistHipUnit.value = unit
        clearResults()
    }

    /**
     * Set gender
     */
    fun setGender(gender: Gender) {
        _gender.value = gender
        clearResults()
    }

    /**
     * Set activity level
     */
    fun setActivityLevel(level: ActivityLevel) {
        _activityLevel.value = level
        clearResults()
    }

    /**
     * Set calculator mode
     */
    fun setCalculatorMode(mode: HealthCalculatorMode) {
        _calculatorMode.value = mode
        clearResults()
    }

    /**
     * Set the selected locale for number formatting
     */
    fun setSelectedLocale(locale: Locale) {
        _selectedLocale.value = locale
    }

    /**
     * Clear all calculation results
     */
    private fun clearResults() {
        _bmiResult.value = null
        _whrResult.value = null
        _bmrResult.value = null
        _error.value = null
    }

    /**
     * Calculate BMI
     */
    fun calculateBMI() {
        viewModelScope.launch {
            try {
                _error.value = null

                // Parse inputs
                val weightValue = _weight.value.replace(",", ".").toFloatOrNull()
                val heightValue = _height.value.replace(",", ".").toFloatOrNull()

                // Validate inputs
                if (weightValue == null || heightValue == null) {
                    _error.value = "Please enter valid numbers for weight and height"
                    return@launch
                }

                if (weightValue <= 0f || heightValue <= 0f) {
                    _error.value = "Please enter positive values for weight and height"
                    return@launch
                }

                // Convert to metric units for calculation
                val weightKg = if (_weightUnit.value == WeightUnit.KG) weightValue else weightValue * 0.453592f
                val heightM = if (_heightUnit.value == HeightUnit.CM) heightValue / 100f else heightValue * 0.0254f

                // Calculate BMI
                val bmi = weightKg / (heightM * heightM)

                // Determine BMI category
                val category = when {
                    bmi < 18.5f -> BMICategory.UNDERWEIGHT
                    bmi < 25f -> BMICategory.NORMAL
                    bmi < 30f -> BMICategory.OVERWEIGHT
                    else -> BMICategory.OBESE
                }

                // Create result
                _bmiResult.value = BMIResult(
                    bmi = bmi,
                    category = category,
                    weightKg = weightKg,
                    heightM = heightM,
                    healthTips = getHealthTipsForBMI(category)
                )
            } catch (e: Exception) {
                _error.value = "Error: ${e.message ?: "Unknown error"}"
                _bmiResult.value = null
            }
        }
    }

    /**
     * Calculate Waist-to-Hip Ratio (WHR)
     */
    fun calculateWHR() {
        viewModelScope.launch {
            try {
                _error.value = null

                // Parse inputs
                val waistValue = _waist.value.replace(",", ".").toFloatOrNull()
                val hipValue = _hip.value.replace(",", ".").toFloatOrNull()

                // Validate inputs
                if (waistValue == null || hipValue == null) {
                    _error.value = "Please enter valid numbers for waist and hip measurements"
                    return@launch
                }

                if (waistValue <= 0f || hipValue <= 0f) {
                    _error.value = "Please enter positive values for waist and hip measurements"
                    return@launch
                }

                // Convert to metric units for consistency (though ratio is unit-independent)
                val waistCm = if (_waistHipUnit.value == HeightUnit.CM) waistValue else waistValue * 2.54f
                val hipCm = if (_waistHipUnit.value == HeightUnit.CM) hipValue else hipValue * 2.54f

                // Calculate WHR
                val whr = waistValue / hipValue

                // Determine WHR category based on gender
                val category = if (_gender.value == Gender.MALE) {
                    when {
                        whr < 0.9f -> WHRCategory.LOW_RISK
                        whr < 1.0f -> WHRCategory.MODERATE_RISK
                        else -> WHRCategory.HIGH_RISK
                    }
                } else {
                    when {
                        whr < 0.8f -> WHRCategory.LOW_RISK
                        whr < 0.85f -> WHRCategory.MODERATE_RISK
                        else -> WHRCategory.HIGH_RISK
                    }
                }

                // Create result
                _whrResult.value = WHRResult(
                    whr = whr,
                    category = category,
                    waistCm = waistCm,
                    hipCm = hipCm,
                    healthTips = getHealthTipsForWHR(category)
                )
            } catch (e: Exception) {
                _error.value = "Error: ${e.message ?: "Unknown error"}"
                _whrResult.value = null
            }
        }
    }

    /**
     * Calculate Basal Metabolic Rate (BMR) and daily calorie needs
     */
    fun calculateBMR() {
        viewModelScope.launch {
            try {
                _error.value = null

                // Parse inputs
                val weightValue = _weight.value.replace(",", ".").toFloatOrNull()
                val heightValue = _height.value.replace(",", ".").toFloatOrNull()
                val ageValue = _age.value.toIntOrNull()

                // Validate inputs
                if (weightValue == null || heightValue == null || ageValue == null) {
                    _error.value = "Please enter valid numbers for weight, height, and age"
                    return@launch
                }

                if (weightValue <= 0f || heightValue <= 0f || ageValue <= 0) {
                    _error.value = "Please enter positive values for weight, height, and age"
                    return@launch
                }

                // Convert to metric units for calculation
                val weightKg = if (_weightUnit.value == WeightUnit.KG) weightValue else weightValue * 0.453592f
                val heightCm = if (_heightUnit.value == HeightUnit.CM) heightValue else heightValue * 2.54f

                // Calculate BMR using Mifflin-St Jeor Equation
                val bmr = if (_gender.value == Gender.MALE) {
                    (10 * weightKg) + (6.25 * heightCm) - (5 * ageValue) + 5
                } else {
                    (10 * weightKg) + (6.25 * heightCm) - (5 * ageValue) - 161
                }

                // Calculate daily calorie needs based on activity level
                val activityMultiplier = when (_activityLevel.value) {
                    ActivityLevel.SEDENTARY -> 1.2f
                    ActivityLevel.LIGHT -> 1.375f
                    ActivityLevel.MODERATE -> 1.55f
                    ActivityLevel.ACTIVE -> 1.725f
                    ActivityLevel.VERY_ACTIVE -> 1.9f
                }

                val dailyCalories = bmr * activityMultiplier

                // Create result
                _bmrResult.value = BMRResult(
                    bmr = bmr.toFloat(),
                    dailyCalories = dailyCalories.toFloat(),
                    weightKg = weightKg,
                    heightCm = heightCm,
                    age = ageValue,
                    gender = _gender.value,
                    activityLevel = _activityLevel.value,
                    healthTips = getHealthTipsForBMR(_activityLevel.value)
                )
            } catch (e: Exception) {
                _error.value = "Error: ${e.message ?: "Unknown error"}"
                _bmrResult.value = null
            }
        }
    }

    /**
     * Format a number according to the selected locale
     */
    fun formatNumber(value: Float, decimalPlaces: Int = 1): String {
        return try {
            if (value.isNaN() || value.isInfinite()) return "N/A"

            val formatter = NumberFormat.getNumberInstance(_selectedLocale.value)
            formatter.maximumFractionDigits = decimalPlaces
            formatter.minimumFractionDigits = 0 // Don't show unnecessary decimal places

            formatter.format(value)
        } catch (e: Exception) {
            // Fallback to simple formatting if locale-specific formatting fails
            String.format("%.${decimalPlaces}f", value)
        }
    }

    /**
     * Get health tips for BMI category
     */
    private fun getHealthTipsForBMI(category: BMICategory): List<String> {
        return when (category) {
            BMICategory.UNDERWEIGHT -> listOf(
                "Consider consulting a nutritionist",
                "Focus on nutrient-rich foods",
                "Regular protein intake is important",
                "Strength training can help build muscle mass",
                "Eat regular meals and healthy snacks"
            )
            BMICategory.NORMAL -> listOf(
                "Maintain your healthy lifestyle",
                "Regular exercise is important",
                "Keep a balanced diet",
                "Stay hydrated with plenty of water",
                "Regular health check-ups are still important"
            )
            BMICategory.OVERWEIGHT -> listOf(
                "Increase physical activity",
                "Monitor portion sizes",
                "Include more vegetables in diet",
                "Reduce processed food consumption",
                "Consider consulting a healthcare provider"
            )
            BMICategory.OBESE -> listOf(
                "Consult healthcare provider",
                "Start with gentle exercise",
                "Make gradual dietary changes",
                "Focus on sustainable lifestyle changes",
                "Consider seeking support from specialists"
            )
        }
    }

    /**
     * Get health tips for WHR category
     */
    private fun getHealthTipsForWHR(category: WHRCategory): List<String> {
        return when (category) {
            WHRCategory.LOW_RISK -> listOf(
                "Maintain your healthy lifestyle",
                "Regular exercise helps maintain body composition",
                "Continue balanced nutrition",
                "Regular health check-ups are still important",
                "Stay active throughout the day"
            )
            WHRCategory.MODERATE_RISK -> listOf(
                "Increase aerobic exercise",
                "Focus on core-strengthening exercises",
                "Monitor dietary fat intake",
                "Reduce processed carbohydrates",
                "Consider consulting a fitness professional"
            )
            WHRCategory.HIGH_RISK -> listOf(
                "Consult healthcare provider",
                "Focus on reducing abdominal fat",
                "Increase physical activity",
                "Monitor blood pressure and cholesterol",
                "Consider a comprehensive health assessment"
            )
        }
    }

    /**
     * Get health tips for BMR and activity level
     */
    private fun getHealthTipsForBMR(activityLevel: ActivityLevel): List<String> {
        val commonTips = listOf(
            "Protein helps maintain muscle mass",
            "Stay hydrated throughout the day",
            "Quality sleep improves metabolism",
            "Regular meals help stabilize energy levels"
        )

        val activitySpecificTips = when (activityLevel) {
            ActivityLevel.SEDENTARY -> listOf(
                "Try to incorporate more movement into your day",
                "Even short walks can improve metabolism",
                "Consider standing rather than sitting when possible",
                "Stretch regularly to maintain flexibility"
            )
            ActivityLevel.LIGHT -> listOf(
                "Gradually increase exercise intensity",
                "Aim for at least 150 minutes of moderate activity weekly",
                "Include both cardio and strength training",
                "Find activities you enjoy to stay motivated"
            )
            ActivityLevel.MODERATE -> listOf(
                "Maintain your good activity level",
                "Consider adding variety to your exercise routine",
                "Recovery days are important for progress",
                "Nutrition should match your activity level"
            )
            ActivityLevel.ACTIVE -> listOf(
                "Ensure adequate nutrition for recovery",
                "Monitor for signs of overtraining",
                "Proper warm-up and cool-down are essential",
                "Consider periodization in your training"
            )
            ActivityLevel.VERY_ACTIVE -> listOf(
                "Recovery nutrition is critical",
                "Listen to your body to prevent injury",
                "Consider working with a sports nutritionist",
                "Balance high-intensity with adequate recovery"
            )
        }

        return activitySpecificTips + commonTips
    }

    /**
     * Get a list of common locales for number formatting
     */
    private fun getCommonLocales(): List<Locale> {
        return listOf(
            Locale.US,                  // English (US)
            Locale.UK,                  // English (UK)
            Locale.GERMANY,             // German
            Locale.FRANCE,              // French
            Locale.JAPAN,               // Japanese
            Locale.CHINA,               // Chinese
            Locale.KOREA,               // Korean
            Locale("es", "ES"),         // Spanish
            Locale("it", "IT"),         // Italian
            Locale("ru", "RU"),         // Russian
            Locale("ar", "SA"),         // Arabic
            Locale("hi", "IN"),         // Hindi
            Locale("pt", "BR"),         // Portuguese (Brazil)
            Locale("tr", "TR"),         // Turkish
            Locale("nl", "NL"),         // Dutch
            Locale("sv", "SE"),         // Swedish
            Locale("pl", "PL"),         // Polish
            Locale("th", "TH"),         // Thai
            Locale("ur", "PK"),         // Urdu
            Locale("vi", "VN")          // Vietnamese
        )
    }

    /**
     * Get the display name for a locale
     */
    fun getLocaleDisplayName(locale: Locale): String {
        return "${locale.getDisplayLanguage(Locale.US)} (${locale.country})"
    }
}

/**
 * Enum representing weight units
 */
enum class WeightUnit(val displayName: String, val symbol: String) {
    KG("Kilograms", "kg"),
    LB("Pounds", "lb")
}

/**
 * Enum representing height units
 */
enum class HeightUnit(val displayName: String, val symbol: String) {
    CM("Centimeters", "cm"),
    IN("Inches", "in")
}

/**
 * Enum representing gender
 */
enum class Gender(val displayName: String) {
    MALE("Male"),
    FEMALE("Female")
}

/**
 * Enum representing activity levels
 */
enum class ActivityLevel(val displayName: String, val description: String) {
    SEDENTARY("Sedentary", "Little or no exercise"),
    LIGHT("Lightly Active", "Light exercise 1-3 days/week"),
    MODERATE("Moderately Active", "Moderate exercise 3-5 days/week"),
    ACTIVE("Active", "Hard exercise 6-7 days/week"),
    VERY_ACTIVE("Very Active", "Very hard exercise & physical job or training twice a day")
}

/**
 * Enum representing BMI categories
 */
enum class BMICategory(
    val displayName: String,
    val description: String,
    val color: androidx.compose.ui.graphics.Color
) {
    UNDERWEIGHT(
        "Underweight",
        "Below healthy weight range",
        NeonCyan
    ),
    NORMAL(
        "Normal Weight",
        "Healthy weight range",
        NeonGreen
    ),
    OVERWEIGHT(
        "Overweight",
        "Above healthy weight range",
        NeonYellow
    ),
    OBESE(
        "Obese",
        "Well above healthy weight range",
        NeonRed
    )
}

/**
 * Enum representing WHR categories
 */
enum class WHRCategory(
    val displayName: String,
    val description: String,
    val color: androidx.compose.ui.graphics.Color
) {
    LOW_RISK(
        "Low Risk",
        "Healthy waist-to-hip ratio",
        NeonGreen
    ),
    MODERATE_RISK(
        "Moderate Risk",
        "Moderate health risk",
        NeonYellow
    ),
    HIGH_RISK(
        "High Risk",
        "Increased health risk",
        NeonRed
    )
}

/**
 * Enum representing health calculator modes
 */
enum class HealthCalculatorMode(val displayName: String) {
    BMI("BMI Calculator"),
    WHR("Waist-Hip Ratio"),
    BMR("Metabolic Rate")
}

/**
 * Data class representing a BMI calculation result
 */
data class BMIResult(
    val bmi: Float,
    val category: BMICategory,
    val weightKg: Float,
    val heightM: Float,
    val healthTips: List<String>
)

/**
 * Data class representing a WHR calculation result
 */
data class WHRResult(
    val whr: Float,
    val category: WHRCategory,
    val waistCm: Float,
    val hipCm: Float,
    val healthTips: List<String>
)

/**
 * Data class representing a BMR calculation result
 */
data class BMRResult(
    val bmr: Float,
    val dailyCalories: Float,
    val weightKg: Float,
    val heightCm: Float,
    val age: Int,
    val gender: Gender,
    val activityLevel: ActivityLevel,
    val healthTips: List<String>
)
