package com.app.wordifynumbers.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*
import kotlin.math.*

@Composable
fun ProbabilityVisualizer(
    data: List<Double>,
    labels: List<String>,
    modifier: Modifier = Modifier,
    animate: Boolean = true
) {
    var animationProgress by remember { mutableStateOf(0f) as MutableState<Float> }
    
    LaunchedEffect(data) {
        if (animate) {
            animationProgress = 0f
            animate(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec = tween(1000, easing = FastOutSlowInEasing)
            ) { value, _ ->
                animationProgress = value
            }
        } else {
            animationProgress = 1f
        }
    }

    Canvas(
        modifier = modifier
    ) {
        val width = size.width
        val height = size.height
        val maxData = data.maxOrNull() ?: 1.0
        val barWidth = width / (data.size * 2)

        // Draw background grid
        val gridLines = 5
        for (i in 0..gridLines) {
            val y = height * (1 - i.toFloat() / gridLines)
            drawLine(
                color = NeonText.copy(alpha = 0.1f),
                start = Offset(0f, y),
                end = Offset(width, y),
                strokeWidth = 1.dp.toPx()
            )
        }

        // Draw probability bars with glow effect
        data.forEachIndexed { index, value ->
            val normalizedValue = (value / maxData * animationProgress).toFloat()
            val x = width * (index + 0.5f) / data.size

            // Glow effect
            drawRect(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        NeonGlow.copy(alpha = 0.3f),
                        NeonGlow.copy(alpha = 0.0f)
                    )
                ),
                topLeft = Offset(x - barWidth / 2, height * (1 - normalizedValue)),
                size = Size(barWidth, height * normalizedValue)
            )

            // Bar outline
            drawRect(
                color = NeonGlow,
                topLeft = Offset(x - barWidth / 2, height * (1 - normalizedValue)),
                size = Size(barWidth, height * normalizedValue),
                style = Stroke(width = 2.dp.toPx())
            )
        }
    }
}

@Composable
fun BellCurveVisualizer(
    mean: Double,
    standardDeviation: Double,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "bell_curve")
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glow"
    )

    Canvas(modifier = modifier) {
        val width = size.width
        val height = size.height
        val points = mutableListOf<Offset>()
        
        // Calculate bell curve points
        for (x in 0..100) {
            val xValue = mean - 4 * standardDeviation + (x / 50.0) * 8 * standardDeviation
            val yValue = (1.0 / (standardDeviation * sqrt(2 * PI))) *
                        exp(-0.5 * ((xValue - mean) / standardDeviation).pow(2.0))
            
            val xPos = (x / 100f) * width
            val yPos = height - (yValue * height * 2).toFloat().coerceIn(0f, height)
            points.add(Offset(xPos, yPos))
        }

        // Draw curve
        val path = Path().apply {
            moveTo(points.first().x, height)
            lineTo(points.first().x, points.first().y)
            for (i in 1 until points.size) {
                lineTo(points[i].x, points[i].y)
            }
            lineTo(points.last().x, height)
            close()
        }

        // Fill with gradient
        drawPath(
            path = path,
            brush = Brush.verticalGradient(
                colors = listOf(
                    NeonGlow.copy(alpha = glowAlpha),
                    NeonGlow.copy(alpha = 0f)
                )
            )
        )

        // Draw curve line
        drawPath(
            path = path,
            color = NeonGlow,
            style = Stroke(width = 2.dp.toPx())
        )
    }
}

@Composable
fun PieChartVisualizer(
    values: List<Double>,
    colors: List<Color> = listOf(NeonBlue, NeonPurple, NeonPink, NeonCyan, NeonGreen),
    modifier: Modifier = Modifier
) {
    var animationProgress by remember { mutableStateOf(0f) as MutableState<Float> }
    
    LaunchedEffect(values) {
        animationProgress = 0f
        animate(
            initialValue = 0f,
            targetValue = 1f,
            animationSpec = tween(1000, easing = FastOutSlowInEasing)
        ) { value, _ ->
            animationProgress = value
        }
    }

    Canvas(modifier = modifier) {
        val total = values.sum()
        var startAngle = -90f
        
        values.forEachIndexed { index, value ->
            val sweepAngle = ((value / total) * 360f * animationProgress).toFloat()
            val color = colors[index % colors.size]
            
            drawArc(
                color = color.copy(alpha = 0.7f),
                startAngle = startAngle.toFloat(),
                sweepAngle = sweepAngle,
                useCenter = true
            )
            
            // Draw outline with glow effect
            drawArc(
                color = color,
                startAngle = startAngle.toFloat(),
                sweepAngle = sweepAngle,
                useCenter = true,
                style = Stroke(width = 2.dp.toPx())
            )
            
            startAngle += sweepAngle
        }
    }
}
