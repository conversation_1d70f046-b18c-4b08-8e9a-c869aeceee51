package com.app.wordifynumbers.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.screens.*
import com.app.wordifynumbers.ui.components.*
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.core.tween
import androidx.compose.animation.togetherWith
import androidx.compose.ui.platform.LocalContext
import androidx.activity.compose.BackHandler
import com.app.wordifynumbers.util.FeedbackUtil

data class NavigationItem(val label: String, val icon: ImageVector)

@Composable
fun MainScreen(calculatorViewModel: CalculatorViewModel) {
    // Main screen setup

    var selectedTab by remember { mutableStateOf(0) }
    var selectedCalculator by remember { mutableStateOf<String?>(null) }
    var showDigitTranslator by remember { mutableStateOf(false) }
    var showDigitDetails by remember { mutableStateOf(false) }
    var showDetailedTranslation by remember { mutableStateOf(false) }
    var selectedLanguage by remember { mutableStateOf("English") }
    val context = LocalContext.current

    // Navigation functions for cross-screen navigation
    val navigateToTab = { tab: Int ->
        selectedTab = tab
        FeedbackUtil.buttonPress(context)
    }

    val navigateToCalculator = { calculatorId: String ->
        selectedTab = 2 // Calculator tab (now index 2)
        selectedCalculator = calculatorId
        FeedbackUtil.buttonPress(context)
    }

    val navigateToWords = {
        selectedTab = 0 // Words tab
        FeedbackUtil.buttonPress(context)
    }

    val navigateToFinance = {
        selectedTab = 1 // Finance tab
        FeedbackUtil.buttonPress(context)
    }

    val navigateToLargeNumbers = {
        selectedTab = 3 // Large Numbers tab (now index 3)
        FeedbackUtil.buttonPress(context)
    }

    // Handle back button press
    BackHandler {
        // Determine what to do based on current screen state
        when {
            // Words tab
            selectedTab == 0 && showDetailedTranslation -> {
                showDetailedTranslation = false
                FeedbackUtil.buttonPress(context)
            }
            selectedTab == 0 && showDigitDetails -> {
                showDigitDetails = false
                FeedbackUtil.buttonPress(context)
            }
            selectedTab == 0 && showDigitTranslator -> {
                showDigitTranslator = false
                FeedbackUtil.buttonPress(context)
            }
            // Calculator tab - go back to calculator selection
            selectedTab == 2 && selectedCalculator != null -> {
                // Return to calculator selection screen
                selectedCalculator = null
                FeedbackUtil.buttonPress(context)
            }

            // If we're on a non-first tab, go back to the first tab
            selectedTab != 0 -> {
                selectedTab = 0
                FeedbackUtil.buttonPress(context)
            }

            // Default case - we're already at the main screen, let system handle back
            // This will close the app as it's the expected behavior when at the root screen
        }
    }

    val navItems = listOf(
        NavigationItem("Words", Icons.Filled.FormatListNumbered),
        NavigationItem("Finance", Icons.Filled.AccountBalance),
        NavigationItem("Calculator", Icons.Filled.Calculate),
        NavigationItem("Large Numbers", Icons.Filled.School)
    )

    Scaffold(
        bottomBar = {
            SimpleNeonBottomNavigation {
                navItems.forEachIndexed { index, item ->
                    NeonNavigationBarItem(
                        selected = selectedTab == index,
                        onClick = {
                            selectedTab = index
                            if (index != 2) selectedCalculator = null // Reset calculator selection when leaving calculator tab
                            FeedbackUtil.buttonPress(context)
                        },
                        icon = {
                            Icon(
                                imageVector = item.icon,
                                contentDescription = item.label
                            )
                        },
                        label = { Text(item.label) }
                    )
                }
            }
        }
    ) { paddingValues ->
        // Main content based on selected tab and calculator
        AnimatedContent(
            targetState = Pair(selectedTab, selectedCalculator),
            transitionSpec = {
                fadeIn(animationSpec = tween(300)) togetherWith
                fadeOut(animationSpec = tween(300))
            },
            label = "Screen Transition"
        ) { (tab, calculator) ->
            when {
                // Words tab
                tab == 0 && showDetailedTranslation -> DetailedTranslationScreen(
                    onBackClick = { showDetailedTranslation = false },
                    modifier = Modifier.padding(paddingValues)
                )
                tab == 0 && showDigitDetails -> DigitToWordDetailsScreen(
                    selectedLanguage = selectedLanguage,
                    onBackClick = { showDigitDetails = false },
                    modifier = Modifier.padding(paddingValues)
                )
                tab == 0 && showDigitTranslator -> DigitTranslatorScreen(
                    onNavigateToDetails = { language ->
                        selectedLanguage = language
                        showDigitDetails = true
                    },
                    modifier = Modifier.padding(paddingValues)
                )
                tab == 0 -> NumberToWordsScreen(
                    onNavigateToDigitTranslator = {
                        showDigitTranslator = true
                        FeedbackUtil.buttonPress(context)
                    },
                    onNavigateToDetailedTranslation = {
                        showDetailedTranslation = true
                        FeedbackUtil.buttonPress(context)
                    },
                    modifier = Modifier.padding(paddingValues),
                    onNavigateToCalculator = navigateToCalculator,
                    onNavigateToLargeNumbers = navigateToLargeNumbers
                )

                // Finance tab
                tab == 1 -> FinanceNotepadScreen(
                    modifier = Modifier.padding(paddingValues),
                    onNavigateToCalculator = navigateToCalculator,
                    onNavigateToWords = navigateToWords,
                    onNavigateToLargeNumbers = navigateToLargeNumbers
                )

                // Calculator tab with specific calculator selected
                tab == 2 && calculator != null -> {
                    when (calculator) {
                        // Math Calculators
                        "basic" -> BasicCalculatorScreen(
                            modifier = Modifier.padding(paddingValues),
                            viewModel = calculatorViewModel
                        )
                        "scientific" -> ScientificCalculatorScreen(
                            modifier = Modifier.padding(paddingValues),
                            viewModel = calculatorViewModel
                        )
                        "complex" -> ModernComplexCalculatorScreen()


                        // Special Calculators
                        "statistics" -> StatisticsCalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )
                        "bmi" -> BMICalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )
                        "programmer" -> ProgrammerCalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )
                        "age" -> AgeCalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )
                        "areaVolume" -> AreaVolumeCalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )

                        // Keep percentage as it's a math calculator
                        "percentage" -> PercentageCalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )
                        // Keep date/time calculators as they're special calculators
                        "date" -> DateTimeCalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )
                        "dateCalculator" -> DateCalculatorScreen(
                            modifier = Modifier.padding(paddingValues)
                        )

                        // Default fallback to calculator selection screen
                        else -> CalculatorScreen(
                            modifier = Modifier.padding(paddingValues),
                            viewModel = calculatorViewModel,
                            onNavigateToCalculator = { calculatorId ->
                                selectedCalculator = calculatorId
                                FeedbackUtil.buttonPress(context)
                            },
                            onNavigateToWords = navigateToWords,
                            onNavigateToLargeNumbers = navigateToLargeNumbers
                        )
                    }
                }

                // Calculator tab with no specific calculator selected
                tab == 2 -> CalculatorScreen(
                    modifier = Modifier.padding(paddingValues),
                    viewModel = calculatorViewModel,
                    onNavigateToCalculator = { calculatorId ->
                        selectedCalculator = calculatorId
                        FeedbackUtil.buttonPress(context)
                    },
                    onNavigateToWords = navigateToWords,
                    onNavigateToLargeNumbers = navigateToLargeNumbers
                )

                // Large Numbers Education tab
                tab == 3 -> LargeNumbersEducationScreen(
                    modifier = Modifier.padding(paddingValues),
                    onNavigateToWords = navigateToWords,
                    onNavigateToCalculator = navigateToCalculator
                )

                // Default fallback
                else -> NumberToWordsScreen(
                    modifier = Modifier.padding(paddingValues),
                    onNavigateToCalculator = navigateToCalculator,
                    onNavigateToLargeNumbers = navigateToLargeNumbers
                )
            }
        }
    }
}
