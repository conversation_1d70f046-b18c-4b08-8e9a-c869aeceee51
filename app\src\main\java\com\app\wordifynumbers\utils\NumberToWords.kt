package com.app.wordifynumbers.utils

import java.text.DecimalFormat
import java.util.*

/**
 * Utility class for converting numbers to words in various languages
 * Supports multiple languages and formats
 */
object NumberToWords {
    // English number words
    private val ones = arrayOf(
        "", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten",
        "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen"
    )

    private val tens = arrayOf(
        "", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety"
    )

    private val scales = arrayOf(
        "", "thousand", "million", "billion", "trillion", "quadrillion", "quintillion"
    )

    // Spanish number words
    private val spanishOnes = arrayOf(
        "", "uno", "dos", "tres", "cuatro", "cinco", "seis", "siete", "ocho", "nueve", "diez",
        "once", "doce", "trece", "catorce", "quince", "dieciséis", "diecisiete", "dieciocho", "diecinueve"
    )

    private val spanishTens = arrayOf(
        "", "", "veinte", "treinta", "cuarenta", "cincuenta", "sesenta", "setenta", "ochenta", "noventa"
    )

    private val spanishScales = arrayOf(
        "", "mil", "millón", "mil millones", "billón", "mil billones", "trillón"
    )

    // French number words
    private val frenchOnes = arrayOf(
        "", "un", "deux", "trois", "quatre", "cinq", "six", "sept", "huit", "neuf", "dix",
        "onze", "douze", "treize", "quatorze", "quinze", "seize", "dix-sept", "dix-huit", "dix-neuf"
    )

    private val frenchTens = arrayOf(
        "", "", "vingt", "trente", "quarante", "cinquante", "soixante", "soixante-dix", "quatre-vingt", "quatre-vingt-dix"
    )

    private val frenchScales = arrayOf(
        "", "mille", "million", "milliard", "billion", "billiard", "trillion"
    )

    // German number words
    private val germanOnes = arrayOf(
        "", "eins", "zwei", "drei", "vier", "fünf", "sechs", "sieben", "acht", "neun", "zehn",
        "elf", "zwölf", "dreizehn", "vierzehn", "fünfzehn", "sechzehn", "siebzehn", "achtzehn", "neunzehn"
    )

    private val germanTens = arrayOf(
        "", "", "zwanzig", "dreißig", "vierzig", "fünfzig", "sechzig", "siebzig", "achtzig", "neunzig"
    )

    private val germanScales = arrayOf(
        "", "tausend", "Million", "Milliarde", "Billion", "Billiarde", "Trillion"
    )

    // Hindi number words
    private val hindiOnes = arrayOf(
        "", "एक", "दो", "तीन", "चार", "पांच", "छह", "सात", "आठ", "नौ", "दस",
        "ग्यारह", "बारह", "तेरह", "चौदह", "पंद्रह", "सोलह", "सत्रह", "अठारह", "उन्नीस"
    )

    private val hindiTens = arrayOf(
        "", "", "बीस", "तीस", "चालीस", "पचास", "साठ", "सत्तर", "अस्सी", "नब्बे"
    )

    private val hindiScales = arrayOf(
        "", "हज़ार", "लाख", "करोड़", "अरब", "खरब", "नील"
    )

    // Urdu number words
    private val urduOnes = arrayOf(
        "", "ایک", "دو", "تین", "چار", "پانچ", "چھ", "سات", "آٹھ", "نو", "دس",
        "گیارہ", "بارہ", "تیرہ", "چودہ", "پندرہ", "سولہ", "سترہ", "اٹھارہ", "انیس"
    )

    private val urduTens = arrayOf(
        "", "", "بیس", "تیس", "چالیس", "پچاس", "ساٹھ", "ستر", "اسی", "نوے"
    )

    private val urduScales = arrayOf(
        "", "ہزار", "لاکھ", "کروڑ", "ارب", "کھرب", "نیل"
    )

    /**
     * Converts a number to words in English
     * @param number The number to convert
     * @return The number in words
     */
    fun convert(number: Int): String = convert(number.toLong())

    fun convert(number: Long): String {
        if (number == 0L) return "Zero"
        if (number < 0) return "Minus " + convert(-number)

        var result = ""
        var position = 0
        var num = number

        while (num > 0) {
            val hundreds = num % 1000
            if (hundreds > 0) {
                val scaleWord = if (position > 0) " ${scales[position]}" else ""
                result = "${convertLessThanOneThousand(hundreds)}$scaleWord $result"
            }
            num /= 1000
            position++
        }

        return toTitleCase(result.trim())
    }

    /**
     * Converts a number less than 1000 to words
     */
    private fun convertLessThanOneThousand(number: Long): String {
        var result = ""

        // Handle hundreds
        val hundred = number / 100
        if (hundred > 0) {
            result += "${ones[hundred.toInt()]} hundred"
        }

        // Handle tens and ones
        val remainder = number % 100
        if (remainder > 0) {
            if (result.isNotEmpty()) result += " "

            if (remainder < 20) {
                result += ones[remainder.toInt()]
            } else {
                val ten = remainder / 10
                val one = remainder % 10

                result += tens[ten.toInt()]
                if (one > 0) {
                    result += "-${ones[one.toInt()]}"
                }
            }
        }

        return result
    }

    /**
     * Converts a number to words based on the specified locale.
     * Supports English, Urdu, and falls back to English for other languages.
     */
    fun convertWithLocale(number: Long, locale: Locale): String {
        return when (locale.language) {
            "ur" -> convertUrdu(number)
            else -> convert(number)
        }
    }

    /**
     * Converts a number to words in Urdu
     * @param number The number to convert
     * @return The number in Urdu words
     */
    fun convertUrdu(number: Long): String {
        if (number == 0L) return "صفر"
        if (number < 0) return "منفی " + convertUrdu(-number)

        var result = ""
        var position = 0
        var num = number

        while (num > 0) {
            val hundreds = num % 1000
            if (hundreds > 0) {
                val scaleWord = if (position > 0) " ${urduScales[position]}" else ""
                result = "${convertUrduLessThanOneThousand(hundreds)}$scaleWord $result"
            }
            num /= 1000
            position++
        }

        return result.trim()
    }

    /**
     * Converts a number less than 1000 to words in Urdu
     * Follows natural Urdu number formation rules
     */
    private fun convertUrduLessThanOneThousand(number: Long): String {
        var result = ""

        // Handle hundreds
        val hundred = number / 100
        if (hundred > 0) {
            // Special case for exactly 100
            if (hundred == 1L && number == 100L) {
                result += "ایک سو"
            } else {
                result += "${urduOnes[hundred.toInt()]} سو"
            }
        }

        // Handle tens and ones
        val remainder = number % 100
        if (remainder > 0) {
            if (result.isNotEmpty()) result += " "

            if (remainder < 20) {
                // Direct lookup for numbers less than 20
                result += urduOnes[remainder.toInt()]
            } else {
                val ten = remainder / 10
                val one = remainder % 10

                // In Urdu, for numbers like 21, 32, etc., the ones digit comes before the tens
                // e.g., "ایک بیس" (one twenty) for 21
                if (one > 0) {
                    result += "${urduOnes[one.toInt()]} ${urduTens[ten.toInt()]}"
                } else {
                    // For numbers like 20, 30, etc., just use the tens word
                    result += urduTens[ten.toInt()]
                }
            }
        }

        return result
    }

    /**
     * Formats a number with proper separators according to locale
     * @param number The number to format
     * @param locale The locale to use for formatting
     * @return The formatted number as a string
     */
    fun formatNumber(number: Double, locale: Locale = Locale.getDefault()): String {
        val formatter = DecimalFormat.getInstance(locale) as DecimalFormat
        formatter.minimumFractionDigits = 0
        formatter.maximumFractionDigits = 2
        return formatter.format(number)
    }

    /**
     * Capitalize each word and hyphenated segment
     */
    private fun toTitleCase(input: String): String =
        input.split(' ').joinToString(" ") { word ->
            word.split('-').joinToString("-") { seg ->
                seg.replaceFirstChar { it.uppercase() }
            }
        }

    /**
     * Format number words to follow a consistent pattern
     * This ensures numbers like "seven million eight hundred sixty-five thousand four hundred thirty-two"
     * are properly formatted
     */
    fun formatNumberWords(input: String): String {
        // Simply return the input with proper capitalization
        // No digit annotations will be added
        return input.trim().split(' ').joinToString(" ") { word ->
            word.split('-').joinToString("-") { seg ->
                seg.replaceFirstChar { it.uppercase() }
            }
        }
    }

    /**
     * Format number words with detailed digit annotations
     * This adds digits in parentheses next to number words
     * Example: "seventy (70) seven (7) million"
     */
    fun formatDetailedNumberWords(input: String): String {
        // For testing, create a simple format with digits in parentheses
        val words = input.split(" ")
        val result = StringBuilder()

        // Add some example digits in parentheses
        for (word in words) {
            when (word.lowercase()) {
                "one" -> result.append("1 (one) ")
                "two" -> result.append("2 (two) ")
                "three" -> result.append("3 (three) ")
                "four" -> result.append("4 (four) ")
                "five" -> result.append("5 (five) ")
                "six" -> result.append("6 (six) ")
                "seven" -> result.append("7 (seven) ")
                "eight" -> result.append("8 (eight) ")
                "nine" -> result.append("9 (nine) ")
                "ten" -> result.append("10 (ten) ")
                "twenty" -> result.append("20 (twenty) ")
                "thirty" -> result.append("30 (thirty) ")
                "forty" -> result.append("40 (forty) ")
                "fifty" -> result.append("50 (fifty) ")
                "sixty" -> result.append("60 (sixty) ")
                "seventy" -> result.append("70 (seventy) ")
                "eighty" -> result.append("80 (eighty) ")
                "ninety" -> result.append("90 (ninety) ")
                "hundred" -> result.append("hundred (100) ")
                "thousand" -> result.append("thousand (1,000) ")
                "million" -> result.append("million (1,000,000) ")
                "billion" -> result.append("billion (1,000,000,000) ")
                else -> result.append("$word ")
            }
        }

        return result.toString().trim()
    }

    /**
     * Check if a language supports detailed formatting with digits in parentheses
     */
    fun supportsDetailedFormat(language: String): Boolean {
        // For testing, always return true
        return true
    }

    /**
     * Enhanced language detection based on common words and character patterns
     */
    fun detectLanguage(text: String): String {
        // Check for non-Latin scripts first (more reliable than word detection)
        if (text.any { it in '\u0600'..'\u06FF' }) {
            // Arabic script (used by Arabic, Urdu, Persian, etc.)
            // Check for specific Urdu characters
            if (text.contains("چ") || text.contains("ٹ") || text.contains("پ") || text.contains("گ") ||
                text.contains("ڑ") || text.contains("ے") || text.contains("ں")) {
                return "urdu"
            }
            return "arabic"
        }

        if (text.any { it in '\u0900'..'\u097F' }) {
            return "hindi" // Devanagari script
        }

        if (text.any { it in '\u0400'..'\u04FF' }) {
            return if (text.contains("ї") || text.contains("є") || text.contains("і")) {
                "ukrainian" // Ukrainian specific characters
            } else {
                "russian" // Cyrillic script
            }
        }

        if (text.any { it in '\u4E00'..'\u9FFF' }) {
            return "chinese" // Chinese characters
        }

        if (text.any { it in '\u3040'..'\u30FF' }) {
            return "japanese" // Japanese kana
        }

        if (text.any { it in '\u1100'..'\u11FF' || it in '\uAC00'..'\uD7AF' }) {
            return "korean" // Korean Hangul
        }

        if (text.any { it in '\u0E00'..'\u0E7F' }) {
            return "thai" // Thai script
        }

        if (text.any { it in '\u0980'..'\u09FF' }) {
            return "bengali" // Bengali script
        }

        // For Latin-based scripts, use word detection
        val languageMarkers = mapOf(
            "english" to listOf("and", "thousand", "million", "hundred", "one", "two", "three", "four", "five"),
            "spanish" to listOf("y", "mil", "millón", "cien", "uno", "dos", "tres", "cuatro", "cinco"),
            "french" to listOf("et", "mille", "million", "cent", "un", "deux", "trois", "quatre", "cinq"),
            "german" to listOf("und", "tausend", "million", "hundert", "ein", "zwei", "drei", "vier", "fünf"),
            "italian" to listOf("e", "mille", "milione", "cento", "uno", "due", "tre", "quattro", "cinque"),
            "portuguese" to listOf("e", "mil", "milhão", "cem", "um", "dois", "três", "quatro", "cinco"),
            "turkish" to listOf("ve", "bin", "milyon", "yüz", "bir", "iki", "üç", "dört", "beş"),
            "polish" to listOf("i", "tysiąc", "milion", "sto", "jeden", "dwa", "trzy", "cztery", "pięć"),
            "dutch" to listOf("en", "duizend", "miljoen", "honderd", "een", "twee", "drie", "vier", "vijf"),
            "vietnamese" to listOf("và", "nghìn", "triệu", "trăm", "một", "hai", "ba", "bốn", "năm"),
            "indonesian" to listOf("dan", "ribu", "juta", "ratus", "satu", "dua", "tiga", "empat", "lima")
        )

        // Special character detection for Latin-based scripts
        if (text.contains("ñ") || text.contains("¿") || text.contains("¡")) {
            return "spanish"
        }

        if (text.contains("ç") || text.contains("é") || text.contains("è") || text.contains("ê") || text.contains("à")) {
            // Could be French or Portuguese, check for specific words
            if (text.contains("et") || text.contains("le") || text.contains("la") || text.contains("les")) {
                return "french"
            }
            if (text.contains("e") || text.contains("o") || text.contains("a") || text.contains("os") || text.contains("as")) {
                return "portuguese"
            }
            return "french" // Default to French if uncertain
        }

        if (text.contains("ß") || text.contains("ä") || text.contains("ö") || text.contains("ü")) {
            return "german"
        }

        if (text.contains("ı") || text.contains("ğ") || text.contains("ş")) {
            return "turkish"
        }

        if (text.contains("ą") || text.contains("ę") || text.contains("ł") || text.contains("ń") || text.contains("ó") || text.contains("ś") || text.contains("ź") || text.contains("ż")) {
            return "polish"
        }

        // Count occurrences of marker words for each language
        val scores = languageMarkers.mapValues { (_, markers) ->
            markers.count { marker ->
                text.contains("\\b$marker\\b".toRegex(RegexOption.IGNORE_CASE))
            }
        }

        // Return the language with the highest score, or "english" if no clear winner
        return scores.entries.maxByOrNull { it.value }?.key ?: "english"
    }
}