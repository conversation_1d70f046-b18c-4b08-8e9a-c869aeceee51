package com.app.wordifynumbers.ui.navigation

import androidx.activity.compose.BackHandler
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.app.wordifynumbers.util.FeedbackUtil

/**
 * A comprehensive back navigation handler that provides standardized back button behavior
 * across all screens in the Wordify Numbers app.
 */

/**
 * Represents the current navigation state and context
 */
data class NavigationState(
    val currentTab: Int = 0,
    val selectedCalculator: String? = null,
    val showModal: Boolean = false,
    val modalType: String? = null,
    val canNavigateBack: Boolean = true,
    val customBackAction: (() -> Unit)? = null
)

/**
 * Standard back navigation handler for calculator screens
 * Handles common scenarios like info dialogs, modal states, etc.
 * Only intercepts back press when there are active modals or custom actions
 */
@Composable
fun CalculatorBackHandler(
    showInfoDialog: Boolean = false,
    onInfoDialogDismiss: () -> Unit = {},
    showModal: Boolean = false,
    onModalDismiss: () -> Unit = {},
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    // Only handle back press if there are active modals or custom actions
    if (showInfoDialog || showModal || customBackAction != null) {
        BackHandler {
            when {
                showInfoDialog -> {
                    onInfoDialogDismiss()
                    FeedbackUtil.buttonPress(context)
                }
                showModal -> {
                    onModalDismiss()
                    FeedbackUtil.buttonPress(context)
                }
                customBackAction != null -> {
                    customBackAction()
                    FeedbackUtil.buttonPress(context)
                }
            }
        }
    }
    // If no modals are active, let MainScreen handle the back press
}

/**
 * Back navigation handler for screens with multiple modal states
 * Handles complex navigation scenarios with multiple overlays
 * Only intercepts back press when there are active modals or custom actions
 */
@Composable
fun MultiModalBackHandler(
    modalStates: List<Pair<Boolean, () -> Unit>>,
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current
    val hasActiveModal = modalStates.any { it.first }

    // Only handle back press if there are active modals or custom actions
    if (hasActiveModal || customBackAction != null) {
        BackHandler {
            // Find the first active modal and close it
            val activeModal = modalStates.firstOrNull { it.first }

            when {
                activeModal != null -> {
                    activeModal.second()
                    FeedbackUtil.buttonPress(context)
                }
                customBackAction != null -> {
                    customBackAction()
                    FeedbackUtil.buttonPress(context)
                }
            }
        }
    }
    // If no modals are active, let MainScreen handle the back press
}



/**
 * Simple back navigation handler for basic screens
 * Only intercepts back press when there's a custom action
 * Otherwise lets MainScreen handle the navigation
 */
@Composable
fun SimpleBackHandler(
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    // Only handle back press if there's a custom action
    if (customBackAction != null) {
        BackHandler {
            customBackAction()
            FeedbackUtil.buttonPress(context)
        }
    }
    // If no custom action, let MainScreen handle the back press
}

/**
 * Back navigation handler for screens with confirmation dialogs
 * Handles scenarios where user needs to confirm before navigating back
 */
@Composable
fun ConfirmationBackHandler(
    showConfirmation: Boolean = false,
    onConfirmationDismiss: () -> Unit = {},
    onConfirmBack: () -> Unit = {},
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    BackHandler {
        when {
            showConfirmation -> {
                onConfirmationDismiss()
                FeedbackUtil.buttonPress(context)
            }
            customBackAction != null -> {
                customBackAction()
                FeedbackUtil.buttonPress(context)
            }
            else -> {
                onConfirmBack()
                FeedbackUtil.buttonPress(context)
            }
        }
    }
}

/**
 * Navigation utilities for common navigation patterns
 */
object NavigationUtils {
    
    /**
     * Creates a standard modal state list for MultiModalBackHandler
     */
    fun createModalStates(vararg modals: Pair<Boolean, () -> Unit>): List<Pair<Boolean, () -> Unit>> {
        return modals.toList()
    }
    
    /**
     * Checks if any modal is currently active
     */
    fun hasActiveModal(vararg modalStates: Boolean): Boolean {
        return modalStates.any { it }
    }
    
    /**
     * Creates a navigation state for complex navigation scenarios
     */
    fun createNavigationState(
        currentTab: Int = 0,
        selectedCalculator: String? = null,
        showModal: Boolean = false,
        modalType: String? = null,
        canNavigateBack: Boolean = true,
        customBackAction: (() -> Unit)? = null
    ): NavigationState {
        return NavigationState(
            currentTab = currentTab,
            selectedCalculator = selectedCalculator,
            showModal = showModal,
            modalType = modalType,
            canNavigateBack = canNavigateBack,
            customBackAction = customBackAction
        )
    }
}
