package com.app.wordifynumbers.security

import android.content.Context
import android.content.SharedPreferences
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec

/**
 * Security manager for handling encryption, secure storage, and data protection
 * in compliance with Android security best practices and Google Play requirements.
 */
object SecurityManager {
    
    private const val KEYSTORE_ALIAS = "WordifyNumbersKey"
    private const val TRANSFORMATION = "AES/GCM/NoPadding"
    private const val ANDROID_KEYSTORE = "AndroidKeyStore"
    
    /**
     * Initialize secure storage using EncryptedSharedPreferences
     */
    fun getSecureSharedPreferences(context: Context): SharedPreferences {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
        
        return EncryptedSharedPreferences.create(
            context,
            "wordify_secure_prefs",
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }
    
    /**
     * Generate or retrieve encryption key from Android Keystore
     */
    private fun getOrCreateSecretKey(): SecretKey {
        val keyStore = KeyStore.getInstance(ANDROID_KEYSTORE)
        keyStore.load(null)
        
        return if (keyStore.containsAlias(KEYSTORE_ALIAS)) {
            keyStore.getKey(KEYSTORE_ALIAS, null) as SecretKey
        } else {
            generateSecretKey()
        }
    }
    
    /**
     * Generate a new secret key in Android Keystore
     */
    private fun generateSecretKey(): SecretKey {
        val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
        val keyGenParameterSpec = KeyGenParameterSpec.Builder(
            KEYSTORE_ALIAS,
            KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
        )
            .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
            .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
            .setUserAuthenticationRequired(false) // Set to true if biometric auth is needed
            .setRandomizedEncryptionRequired(true)
            .build()
        
        keyGenerator.init(keyGenParameterSpec)
        return keyGenerator.generateKey()
    }
    
    /**
     * Encrypt sensitive data using Android Keystore
     */
    fun encryptData(data: String): EncryptedData? {
        return try {
            val secretKey = getOrCreateSecretKey()
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val encryptedBytes = cipher.doFinal(data.toByteArray())
            val iv = cipher.iv
            
            EncryptedData(
                encryptedData = Base64.encodeToString(encryptedBytes, Base64.DEFAULT),
                iv = Base64.encodeToString(iv, Base64.DEFAULT)
            )
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Decrypt sensitive data using Android Keystore
     */
    fun decryptData(encryptedData: EncryptedData): String? {
        return try {
            val secretKey = getOrCreateSecretKey()
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val iv = Base64.decode(encryptedData.iv, Base64.DEFAULT)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, IvParameterSpec(iv))
            
            val encryptedBytes = Base64.decode(encryptedData.encryptedData, Base64.DEFAULT)
            val decryptedBytes = cipher.doFinal(encryptedBytes)
            
            String(decryptedBytes)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    /**
     * Validate input to prevent injection attacks
     */
    fun sanitizeInput(input: String): String {
        return input
            .replace(Regex("[<>\"'&]"), "") // Remove potentially dangerous characters
            .trim()
            .take(1000) // Limit input length
    }
    
    /**
     * Check if the app is running in a secure environment
     */
    fun isSecureEnvironment(context: Context): Boolean {
        // Check for debugging, rooting, etc.
        return !isDebuggingEnabled(context) && !isRooted()
    }
    
    /**
     * Check if debugging is enabled
     */
    private fun isDebuggingEnabled(context: Context): Boolean {
        return (context.applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_DEBUGGABLE) != 0
    }
    
    /**
     * Basic root detection
     */
    private fun isRooted(): Boolean {
        val rootPaths = arrayOf(
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su"
        )
        
        return rootPaths.any { java.io.File(it).exists() }
    }
    
    /**
     * Data class for encrypted data
     */
    data class EncryptedData(
        val encryptedData: String,
        val iv: String
    )
    
    /**
     * Secure data storage operations
     */
    object SecureStorage {
        
        fun storeSecureData(context: Context, key: String, value: String) {
            val securePrefs = getSecureSharedPreferences(context)
            securePrefs.edit().putString(key, value).apply()
        }
        
        fun getSecureData(context: Context, key: String, defaultValue: String = ""): String {
            val securePrefs = getSecureSharedPreferences(context)
            return securePrefs.getString(key, defaultValue) ?: defaultValue
        }
        
        fun removeSecureData(context: Context, key: String) {
            val securePrefs = getSecureSharedPreferences(context)
            securePrefs.edit().remove(key).apply()
        }
        
        fun clearAllSecureData(context: Context) {
            val securePrefs = getSecureSharedPreferences(context)
            securePrefs.edit().clear().apply()
        }
    }
}
