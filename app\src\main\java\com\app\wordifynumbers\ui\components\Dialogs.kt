package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.app.wordifynumbers.ui.theme.*

@Composable
fun NeonDialog(
    onDismissRequest: () -> Unit,
    title: String,
    text: String,
    confirmButton: @Composable () -> Unit,
    dismissButton: @Composable (() -> Unit)? = null,
    properties: DialogProperties = DialogProperties()
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = properties
    ) {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(
                    NeonDeepBlue
                ),
            color = NeonDeepBlue
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineSmall,
                    color = NeonGlow
                )

                Text(
                    text = text,
                    style = MaterialTheme.typography.bodyMedium,
                    color = NeonText
                )

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    dismissButton?.let {
                        Box(modifier = Modifier.padding(end = 8.dp)) {
                            dismissButton()
                        }
                    }
                    confirmButton()
                }
            }
        }
    }
}

@Composable
fun NeonAlertDialog(
    title: String,
    message: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    confirmText: String = "OK",
    dismissText: String = "Cancel"
) {
    NeonDialog(
        onDismissRequest = onDismiss,
        title = title,
        text = message,
        confirmButton = {
            NeonButton(
                onClick = onConfirm
            ) {
                Text(confirmText)
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = NeonText
                )
            ) {
                Text(dismissText)
            }
        }
    )
}

@Composable
fun NeonLoadingDialog(
    message: String,
    onDismissRequest: () -> Unit = {}
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(dismissOnClickOutside = false)
    ) {
        Surface(
            modifier = Modifier
                .clip(RoundedCornerShape(16.dp))
                .background(NeonDeepBlue),
            color = NeonDeepBlue
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                CircularProgressIndicator(
                    color = NeonGlow,
                    modifier = Modifier.size(48.dp)
                )

                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = NeonText
                )
            }
        }
    }
}

@Composable
fun NeonInfoDialog(
    title: String,
    content: String,
    onDismiss: () -> Unit
) {
    NeonDialog(
        onDismissRequest = onDismiss,
        title = title,
        text = content,
        confirmButton = {
            NeonButton(
                onClick = onDismiss
            ) {
                Text("Got it")
            }
        }
    )
}

/**
 * Enhanced AlertDialog with neon styling that can be used as a drop-in replacement
 * for the standard AlertDialog component.
 */
@Composable
fun EnhancedNeonAlertDialog(
    onDismissRequest: () -> Unit,
    title: @Composable () -> Unit,
    text: @Composable (() -> Unit)? = null,
    confirmButton: @Composable () -> Unit,
    dismissButton: @Composable (() -> Unit)? = null,
    icon: @Composable (() -> Unit)? = null,
    accentColor: Color = NeonGlow,
    properties: DialogProperties = DialogProperties()
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = properties
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .shadow(
                    elevation = 16.dp,
                    spotColor = accentColor.copy(alpha = 0.3f),
                    ambientColor = accentColor.copy(alpha = 0.2f),
                    shape = RoundedCornerShape(16.dp)
                ),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = NeonCard.copy(alpha = 0.95f)
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                NeonCard.copy(alpha = 0.95f),
                                NeonCard.copy(alpha = 0.85f)
                            )
                        )
                    )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(24.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Title section with icon
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        if (icon != null) {
                            Box(modifier = Modifier.padding(end = 16.dp)) {
                                icon()
                            }
                        }

                        Box(modifier = Modifier.weight(1f)) {
                            title()
                        }
                    }

                    // Divider with glow effect
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(0.95f)
                            .height(1.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        accentColor.copy(alpha = 0.3f),
                                        accentColor.copy(alpha = 0.5f),
                                        accentColor.copy(alpha = 0.3f),
                                        Color.Transparent
                                    )
                                )
                            )
                    )

                    // Content section
                    if (text != null) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = 400.dp)
                                .verticalScroll(rememberScrollState())
                                .background(
                                    brush = Brush.verticalGradient(
                                        colors = listOf(
                                            NeonCard.copy(alpha = 0.95f),
                                            NeonCard.copy(alpha = 0.9f)
                                        )
                                    )
                                )
                                .padding(vertical = 8.dp)
                        ) {
                            text()
                        }
                    }

                    // Buttons section
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        dismissButton?.let {
                            Box(modifier = Modifier.padding(end = 8.dp)) {
                                dismissButton()
                            }
                        }
                        confirmButton()
                    }
                }
            }
        }
    }
}
