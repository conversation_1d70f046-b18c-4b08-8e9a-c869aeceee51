package com.app.wordifynumbers.ui.components

enum class NumberValidation {
    ANY,             // Any number allowed
    POSITIVE,        // Must be greater than 0
    POSITIVE_INT,    // Must be positive integer
    NEGATIVE,        // Must be less than 0
    ZERO_TO_ONE,    // Must be between 0 and 1
    PERCENTAGE,      // Must be between 0 and 100
    ROMAN,          // Must be valid Roman numeral
    FRACTION        // Must be valid fraction (e.g., "1/2", "3/4")
}
