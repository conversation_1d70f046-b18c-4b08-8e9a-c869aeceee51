#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 68816 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#
#  
#
# Command Line: -XX:+HeapDumpOnOutOfMemoryError -XX:+UseParallelGC -XX:MaxMetaspaceSize=1g --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4g -Dfile.encoding=utf8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Talash Ai\project-gradle-home\wrapper\dists\gradle-8.10-bin\deqhafrv1ntovfmgh0nh3npr9\gradle-8.10\lib\agents\gradle-instrumentation-agent-8.10.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10

Host: Intel(R) Core(TM) i5-6300U CPU @ 2.40GHz, 4 cores, 11G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
Time: Sat Apr 26 05:22:11 2025 Pakistan Standard Time elapsed time: 100.046928 seconds (0d 0h 1m 40s)

---------------  T H R E A D  ---------------

Current thread (0x000001e3ff9e9920):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=11996, stack(0x000000ba62900000,0x000000ba62a00000)]


Current CompileTask:
C2: 100047 9151       4       java.util.WeakHashMap::put (160 bytes)

Stack: [0x000000ba62900000,0x000000ba62a00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x682eb9]
V  [jvm.dll+0x8399c8]
V  [jvm.dll+0x83b473]
V  [jvm.dll+0x83bae3]
V  [jvm.dll+0x24715f]
V  [jvm.dll+0xace44]
V  [jvm.dll+0xad4dc]
V  [jvm.dll+0x365b0f]
V  [jvm.dll+0x1ba6ea]
V  [jvm.dll+0x2182a7]
V  [jvm.dll+0x21758f]
V  [jvm.dll+0x1a3150]
V  [jvm.dll+0x22799c]
V  [jvm.dll+0x225afb]
V  [jvm.dll+0x7ef128]
V  [jvm.dll+0x7e948c]
V  [jvm.dll+0x681d97]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001e3d4db9060, length=47, elements={
0x000001e3f6d40ec0, 0x000001e3ff9d3d30, 0x000001e3ff9d5ad0, 0x000001e3ff9e3bc0,
0x000001e3ff9e4750, 0x000001e3ff9e6390, 0x000001e3ff9e8c70, 0x000001e3ff9e9920,
0x000001e3ff9ece90, 0x000001e3ffa5cf60, 0x000001e3ffa88920, 0x000001e3ccd4d5e0,
0x000001e3d1af98f0, 0x000001e3d2031210, 0x000001e3d1ac77a0, 0x000001e3d202eb60,
0x000001e3d1f29a10, 0x000001e3d1690de0, 0x000001e3d1691d10, 0x000001e3d1694080,
0x000001e3d1692220, 0x000001e3d1692730, 0x000001e3d1692c40, 0x000001e3d1694590,
0x000001e3d1693660, 0x000001e3d1693150, 0x000001e3d16912f0, 0x000001e3d1691800,
0x000001e3d18d31a0, 0x000001e3d18d45e0, 0x000001e3d18d7880, 0x000001e3d18d82a0,
0x000001e3d18d40d0, 0x000001e3d18d7d90, 0x000001e3d18d87b0, 0x000001e3d18d8cc0,
0x000001e3d18d9bf0, 0x000001e3d18d4af0, 0x000001e3d18d5a20, 0x000001e3d18d2780,
0x000001e3d18d3bc0, 0x000001e3d18d6440, 0x000001e3d18d6e60, 0x000001e3d5c6e810,
0x000001e3d5c6c9b0, 0x000001e3d5c6ddf0, 0x000001e3d5c6b060
}

Java Threads: ( => current thread )
  0x000001e3f6d40ec0 JavaThread "main" [_thread_blocked, id=1552, stack(0x000000ba61f00000,0x000000ba62000000)]
  0x000001e3ff9d3d30 JavaThread "Reference Handler" daemon [_thread_blocked, id=5768, stack(0x000000ba62300000,0x000000ba62400000)]
  0x000001e3ff9d5ad0 JavaThread "Finalizer" daemon [_thread_blocked, id=9720, stack(0x000000ba62400000,0x000000ba62500000)]
  0x000001e3ff9e3bc0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=16236, stack(0x000000ba62500000,0x000000ba62600000)]
  0x000001e3ff9e4750 JavaThread "Attach Listener" daemon [_thread_blocked, id=5028, stack(0x000000ba62600000,0x000000ba62700000)]
  0x000001e3ff9e6390 JavaThread "Service Thread" daemon [_thread_blocked, id=13892, stack(0x000000ba62700000,0x000000ba62800000)]
  0x000001e3ff9e8c70 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=16696, stack(0x000000ba62800000,0x000000ba62900000)]
=>0x000001e3ff9e9920 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=11996, stack(0x000000ba62900000,0x000000ba62a00000)]
  0x000001e3ff9ece90 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=13624, stack(0x000000ba62a00000,0x000000ba62b00000)]
  0x000001e3ffa5cf60 JavaThread "Sweeper thread" daemon [_thread_blocked, id=12984, stack(0x000000ba62b00000,0x000000ba62c00000)]
  0x000001e3ffa88920 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=15468, stack(0x000000ba62c00000,0x000000ba62d00000)]
  0x000001e3ccd4d5e0 JavaThread "Notification Thread" daemon [_thread_blocked, id=7500, stack(0x000000ba62d00000,0x000000ba62e00000)]
  0x000001e3d1af98f0 JavaThread "Daemon health stats" [_thread_blocked, id=13484, stack(0x000000ba63200000,0x000000ba63300000)]
  0x000001e3d2031210 JavaThread "Incoming local TCP Connector on port 52696" [_thread_in_native, id=16652, stack(0x000000ba63300000,0x000000ba63400000)]
  0x000001e3d1ac77a0 JavaThread "Daemon periodic checks" [_thread_blocked, id=7100, stack(0x000000ba63400000,0x000000ba63500000)]
  0x000001e3d202eb60 JavaThread "Daemon" [_thread_blocked, id=11316, stack(0x000000ba63500000,0x000000ba63600000)]
  0x000001e3d1f29a10 JavaThread "Handler for socket connection from /127.0.0.1:52696 to /127.0.0.1:52697" [_thread_in_native, id=11240, stack(0x000000ba63600000,0x000000ba63700000)]
  0x000001e3d1690de0 JavaThread "Cancel handler" [_thread_blocked, id=9872, stack(0x000000ba63700000,0x000000ba63800000)]
  0x000001e3d1691d10 JavaThread "Daemon worker" [_thread_in_vm, id=12956, stack(0x000000ba63800000,0x000000ba63900000)]
  0x000001e3d1694080 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:52696 to /127.0.0.1:52697" [_thread_blocked, id=8556, stack(0x000000ba63900000,0x000000ba63a00000)]
  0x000001e3d1692220 JavaThread "Stdin handler" [_thread_blocked, id=2588, stack(0x000000ba63a00000,0x000000ba63b00000)]
  0x000001e3d1692730 JavaThread "Daemon client event forwarder" [_thread_blocked, id=14816, stack(0x000000ba63b00000,0x000000ba63c00000)]
  0x000001e3d1692c40 JavaThread "Cache worker for journal cache (C:\Talash Ai\project-gradle-home\caches\journal-1)" [_thread_blocked, id=1712, stack(0x000000ba63c00000,0x000000ba63d00000)]
  0x000001e3d1694590 JavaThread "File lock request listener" [_thread_in_native, id=4628, stack(0x000000ba63d00000,0x000000ba63e00000)]
  0x000001e3d1693660 JavaThread "Cache worker for file hash cache (C:\Talash Ai\project-gradle-home\caches\8.10\fileHashes)" [_thread_blocked, id=13364, stack(0x000000ba63e00000,0x000000ba63f00000)]
  0x000001e3d1693150 JavaThread "File lock release action executor" [_thread_blocked, id=1320, stack(0x000000ba63f00000,0x000000ba64000000)]
  0x000001e3d16912f0 JavaThread "Cache worker for file hash cache (D:\Wordify Numbers\.gradle\8.10\fileHashes)" [_thread_blocked, id=11260, stack(0x000000ba64000000,0x000000ba64100000)]
  0x000001e3d1691800 JavaThread "Cache worker for Build Output Cleanup Cache (D:\Wordify Numbers\.gradle\buildOutputCleanup)" [_thread_blocked, id=15740, stack(0x000000ba64100000,0x000000ba64200000)]
  0x000001e3d18d31a0 JavaThread "File watcher server" daemon [_thread_in_native, id=3288, stack(0x000000ba64200000,0x000000ba64300000)]
  0x000001e3d18d45e0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=4568, stack(0x000000ba64300000,0x000000ba64400000)]
  0x000001e3d18d7880 JavaThread "jar transforms" [_thread_blocked, id=14180, stack(0x000000ba64400000,0x000000ba64500000)]
  0x000001e3d18d82a0 JavaThread "jar transforms Thread 2" [_thread_blocked, id=4400, stack(0x000000ba64500000,0x000000ba64600000)]
  0x000001e3d18d40d0 JavaThread "jar transforms Thread 3" [_thread_blocked, id=14824, stack(0x000000ba64600000,0x000000ba64700000)]
  0x000001e3d18d7d90 JavaThread "Cache worker for checksums cache (D:\Wordify Numbers\.gradle\8.10\checksums)" [_thread_blocked, id=13832, stack(0x000000ba64700000,0x000000ba64800000)]
  0x000001e3d18d87b0 JavaThread "Cache worker for file content cache (C:\Talash Ai\project-gradle-home\caches\8.10\fileContent)" [_thread_blocked, id=14600, stack(0x000000ba64800000,0x000000ba64900000)]
  0x000001e3d18d8cc0 JavaThread "Cache worker for cache directory md-rule (C:\Talash Ai\project-gradle-home\caches\8.10\md-rule)" [_thread_blocked, id=13468, stack(0x000000ba64900000,0x000000ba64a00000)]
  0x000001e3d18d9bf0 JavaThread "Cache worker for cache directory md-supplier (C:\Talash Ai\project-gradle-home\caches\8.10\md-supplier)" [_thread_blocked, id=10336, stack(0x000000ba64a00000,0x000000ba64b00000)]
  0x000001e3d18d4af0 JavaThread "jar transforms Thread 4" [_thread_blocked, id=15164, stack(0x000000ba64b00000,0x000000ba64c00000)]
  0x000001e3d18d5a20 JavaThread "Unconstrained build operations" [_thread_blocked, id=16740, stack(0x000000ba64e00000,0x000000ba64f00000)]
  0x000001e3d18d2780 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=8008, stack(0x000000ba64f00000,0x000000ba65000000)]
  0x000001e3d18d3bc0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=3868, stack(0x000000ba65000000,0x000000ba65100000)]
  0x000001e3d18d6440 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=17160, stack(0x000000ba65100000,0x000000ba65200000)]
  0x000001e3d18d6e60 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=12308, stack(0x000000ba65200000,0x000000ba65300000)]
  0x000001e3d5c6e810 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=13164, stack(0x000000ba65300000,0x000000ba65400000)]
  0x000001e3d5c6c9b0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=16124, stack(0x000000ba65400000,0x000000ba65500000)]
  0x000001e3d5c6ddf0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=8196, stack(0x000000ba65500000,0x000000ba65600000)]
  0x000001e3d5c6b060 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=4116, stack(0x000000ba65600000,0x000000ba65700000)]

Other Threads:
  0x000001e3f6df6de0 VMThread "VM Thread" [stack: 0x000000ba62200000,0x000000ba62300000] [id=17320]
  0x000001e3ccd4dac0 WatcherThread [stack: 0x000000ba62e00000,0x000000ba62f00000] [id=2172]
  0x000001e3f6d57e60 GCTaskThread "GC Thread#0" [stack: 0x000000ba62100000,0x000000ba62200000] [id=10756]
  0x000001e3d20c3580 GCTaskThread "GC Thread#1" [stack: 0x000000ba62f00000,0x000000ba63000000] [id=3220]
  0x000001e3d20c3840 GCTaskThread "GC Thread#2" [stack: 0x000000ba63000000,0x000000ba63100000] [id=17296]
  0x000001e3d1ea73a0 GCTaskThread "GC Thread#3" [stack: 0x000000ba63100000,0x000000ba63200000] [id=4212]

Threads with active compile tasks:
C2 CompilerThread0   100129 9151       4       java.util.WeakHashMap::put (160 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001e3f4c7d990] Metaspace_lock - owner thread: 0x000001e3d1691d10

Heap address: 0x0000000700000000, size: 4096 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001e397000000-0x000001e397bb0000-0x000001e397bb0000), size 12255232, SharedBaseAddress: 0x000001e397000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001e398000000-0x000001e3cc000000, reserved size: 872415232
Narrow klass base: 0x000001e397000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 11687M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 184M
 Heap Max Capacity: 4G
 Pre-touch: Disabled
 Parallel Workers: 4

Heap:
 PSYoungGen      total 189952K, used 74838K [0x00000007aab00000, 0x00000007b8680000, 0x0000000800000000)
  eden space 174592K, 42% used [0x00000007aab00000,0x00000007af415b98,0x00000007b5580000)
  from space 15360K, 0% used [0x00000007b5580000,0x00000007b5580000,0x00000007b6480000)
  to   space 25088K, 0% used [0x00000007b6e00000,0x00000007b6e00000,0x00000007b8680000)
 ParOldGen       total 125952K, used 59226K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 47% used [0x0000000700000000,0x00000007039d69e8,0x0000000707b00000)
 Metaspace       used 69019K, committed 69696K, reserved 917504K
  class space    used 9838K, committed 10176K, reserved 851968K

Card table byte_map: [0x000001e3fe880000,0x000001e3ff090000] _byte_map_base: 0x000001e3fb080000

Marking Bits: (ParMarkBitMap*) 0x00007ffc88486940
 Begin Bits: [0x000001e38f000000, 0x000001e393000000)
 End Bits:   [0x000001e393000000, 0x000001e397000000)

Polling page: 0x000001e3f6500000

Metaspace:

Usage:
  Non-class:     57.79 MB used.
      Class:      9.61 MB used.
       Both:     67.40 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      58.12 MB ( 91%) committed,  1 nodes.
      Class space:      832.00 MB reserved,       9.94 MB (  1%) committed,  1 nodes.
             Both:      896.00 MB reserved,      68.06 MB (  8%) committed. 

Chunk freelists:
   Non-Class:  5.86 MB
       Class:  5.97 MB
        Both:  11.83 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 828.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1089.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 3531.
num_chunk_merges: 6.
num_chunk_splits: 2432.
num_chunks_enlarged: 1746.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=4712Kb max_used=5319Kb free=115287Kb
 bounds [0x000001e387ad0000, 0x000001e388010000, 0x000001e38f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=13520Kb max_used=15414Kb free=106479Kb
 bounds [0x000001e380000000, 0x000001e380f10000, 0x000001e387530000]
CodeHeap 'non-nmethods': size=5760Kb used=2362Kb max_used=2419Kb free=3397Kb
 bounds [0x000001e387530000, 0x000001e3877b0000, 0x000001e387ad0000]
 total_blobs=8573 nmethods=7680 adapters=808
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 97.008 Thread 0x000001e3ff9ece90 nmethod 9141 0x000001e380d6bc90 code [0x000001e380d6c0a0, 0x000001e380d6e3d8]
Event: 97.201 Thread 0x000001e3ff9e9920 9142   !   4       java.util.concurrent.ConcurrentHashMap::putVal (432 bytes)
Event: 97.249 Thread 0x000001e3ff9e9920 nmethod 9142 0x000001e387e1b510 code [0x000001e387e1b740, 0x000001e387e1cca0]
Event: 97.322 Thread 0x000001e3ff9ece90 9143       3       java.util.ArrayList::addAll (76 bytes)
Event: 97.323 Thread 0x000001e3ff9ece90 nmethod 9143 0x000001e3803ffa90 code [0x000001e3803ffc60, 0x000001e3804000e8]
Event: 97.423 Thread 0x000001e3ff9e9920 9144       4       org.jetbrains.kotlin.it.unimi.dsi.fastutil.HashCommon::mix (12 bytes)
Event: 97.423 Thread 0x000001e3ff9e9920 nmethod 9144 0x000001e387c4da90 code [0x000001e387c4dc00, 0x000001e387c4dc78]
Event: 98.236 Thread 0x000001e3ff9ece90 9145       1       org.jetbrains.kotlin.cli.jvm.config.JvmClasspathRoot::getFile (5 bytes)
Event: 98.236 Thread 0x000001e3ff9ece90 nmethod 9145 0x000001e387c4d690 code [0x000001e387c4d820, 0x000001e387c4d8f8]
Event: 98.786 Thread 0x000001e3ff9ece90 9146       1       java.util.concurrent.atomic.AtomicReference::set (6 bytes)
Event: 98.786 Thread 0x000001e3ff9ece90 nmethod 9146 0x000001e387e51490 code [0x000001e387e51620, 0x000001e387e51718]
Event: 98.806 Thread 0x000001e3ff9ece90 9147       3       sun.invoke.util.VerifyAccess::isTypeVisible (118 bytes)
Event: 98.809 Thread 0x000001e3ff9ece90 nmethod 9147 0x000001e3800f5d90 code [0x000001e3800f61a0, 0x000001e3800f81c8]
Event: 99.707 Thread 0x000001e3ff9ece90 9148       3       java.util.HashMap$Values::spliterator (16 bytes)
Event: 99.707 Thread 0x000001e3ff9ece90 nmethod 9148 0x000001e380687a10 code [0x000001e380687bc0, 0x000001e380687d88]
Event: 99.707 Thread 0x000001e3ff9ece90 9149       3       java.util.HashMap$ValueSpliterator::<init> (12 bytes)
Event: 99.708 Thread 0x000001e3ff9ece90 nmethod 9149 0x000001e3806c2e90 code [0x000001e3806c3040, 0x000001e3806c3238]
Event: 99.805 Thread 0x000001e3ff9ece90 9150       3       org.jetbrains.kotlin.com.intellij.util.text.ByteArrayCharSequence::charAt (17 bytes)
Event: 99.806 Thread 0x000001e3ff9ece90 nmethod 9150 0x000001e38017d210 code [0x000001e38017d3a0, 0x000001e38017d4f8]
Event: 99.887 Thread 0x000001e3ff9e9920 9151       4       java.util.WeakHashMap::put (160 bytes)

GC Heap History (20 events):
Event: 32.916 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 101888K, used 101869K [0x00000007aab00000, 0x00000007b1600000, 0x0000000800000000)
  eden space 94208K, 100% used [0x00000007aab00000,0x00000007b0700000,0x00000007b0700000)
  from space 7680K, 99% used [0x00000007b0e80000,0x00000007b15fb6d8,0x00000007b1600000)
  to   space 7680K, 0% used [0x00000007b0700000,0x00000007b0700000,0x00000007b0e80000)
 ParOldGen       total 125952K, used 5520K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 4% used [0x0000000700000000,0x0000000700564088,0x0000000707b00000)
 Metaspace       used 17948K, committed 18240K, reserved 917504K
  class space    used 2551K, committed 2688K, reserved 851968K
}
Event: 32.934 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 101888K, used 7653K [0x00000007aab00000, 0x00000007b7200000, 0x0000000800000000)
  eden space 94208K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b0700000)
  from space 7680K, 99% used [0x00000007b0700000,0x00000007b0e79708,0x00000007b0e80000)
  to   space 7680K, 0% used [0x00000007b6a80000,0x00000007b6a80000,0x00000007b7200000)
 ParOldGen       total 125952K, used 13543K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 10% used [0x0000000700000000,0x0000000700d39de8,0x0000000707b00000)
 Metaspace       used 17948K, committed 18240K, reserved 917504K
  class space    used 2551K, committed 2688K, reserved 851968K
}
Event: 34.460 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 101888K, used 38713K [0x00000007aab00000, 0x00000007b7200000, 0x0000000800000000)
  eden space 94208K, 32% used [0x00000007aab00000,0x00000007ac955008,0x00000007b0700000)
  from space 7680K, 99% used [0x00000007b0700000,0x00000007b0e79708,0x00000007b0e80000)
  to   space 7680K, 0% used [0x00000007b6a80000,0x00000007b6a80000,0x00000007b7200000)
 ParOldGen       total 125952K, used 13543K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 10% used [0x0000000700000000,0x0000000700d39de8,0x0000000707b00000)
 Metaspace       used 21156K, committed 21504K, reserved 917504K
  class space    used 3067K, committed 3264K, reserved 851968K
}
Event: 34.476 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 196096K, used 7655K [0x00000007aab00000, 0x00000007b7200000, 0x0000000800000000)
  eden space 188416K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b6300000)
  from space 7680K, 99% used [0x00000007b6a80000,0x00000007b71f9c00,0x00000007b7200000)
  to   space 7680K, 0% used [0x00000007b6300000,0x00000007b6300000,0x00000007b6a80000)
 ParOldGen       total 125952K, used 15322K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 12% used [0x0000000700000000,0x0000000700ef6bf0,0x0000000707b00000)
 Metaspace       used 21156K, committed 21504K, reserved 917504K
  class space    used 3067K, committed 3264K, reserved 851968K
}
Event: 34.476 GC heap before
{Heap before GC invocations=5 (full 1):
 PSYoungGen      total 196096K, used 7655K [0x00000007aab00000, 0x00000007b7200000, 0x0000000800000000)
  eden space 188416K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b6300000)
  from space 7680K, 99% used [0x00000007b6a80000,0x00000007b71f9c00,0x00000007b7200000)
  to   space 7680K, 0% used [0x00000007b6300000,0x00000007b6300000,0x00000007b6a80000)
 ParOldGen       total 125952K, used 15322K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 12% used [0x0000000700000000,0x0000000700ef6bf0,0x0000000707b00000)
 Metaspace       used 21156K, committed 21504K, reserved 917504K
  class space    used 3067K, committed 3264K, reserved 851968K
}
Event: 34.538 GC heap after
{Heap after GC invocations=5 (full 1):
 PSYoungGen      total 196096K, used 0K [0x00000007aab00000, 0x00000007b7200000, 0x0000000800000000)
  eden space 188416K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b6300000)
  from space 7680K, 0% used [0x00000007b6a80000,0x00000007b6a80000,0x00000007b7200000)
  to   space 7680K, 0% used [0x00000007b6300000,0x00000007b6300000,0x00000007b6a80000)
 ParOldGen       total 125952K, used 22424K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 17% used [0x0000000700000000,0x00000007015e6260,0x0000000707b00000)
 Metaspace       used 21156K, committed 21504K, reserved 917504K
  class space    used 3067K, committed 3264K, reserved 851968K
}
Event: 42.257 GC heap before
{Heap before GC invocations=6 (full 1):
 PSYoungGen      total 196096K, used 113836K [0x00000007aab00000, 0x00000007b7200000, 0x0000000800000000)
  eden space 188416K, 60% used [0x00000007aab00000,0x00000007b1a2b118,0x00000007b6300000)
  from space 7680K, 0% used [0x00000007b6a80000,0x00000007b6a80000,0x00000007b7200000)
  to   space 7680K, 0% used [0x00000007b6300000,0x00000007b6300000,0x00000007b6a80000)
 ParOldGen       total 125952K, used 22424K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 17% used [0x0000000700000000,0x00000007015e6260,0x0000000707b00000)
 Metaspace       used 35387K, committed 35840K, reserved 917504K
  class space    used 5025K, committed 5248K, reserved 851968K
}
Event: 42.269 GC heap after
{Heap after GC invocations=6 (full 1):
 PSYoungGen      total 196096K, used 6865K [0x00000007aab00000, 0x00000007b8300000, 0x0000000800000000)
  eden space 188416K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b6300000)
  from space 7680K, 89% used [0x00000007b6300000,0x00000007b69b46e8,0x00000007b6a80000)
  to   space 16384K, 0% used [0x00000007b7300000,0x00000007b7300000,0x00000007b8300000)
 ParOldGen       total 125952K, used 22432K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 17% used [0x0000000700000000,0x00000007015e8260,0x0000000707b00000)
 Metaspace       used 35387K, committed 35840K, reserved 917504K
  class space    used 5025K, committed 5248K, reserved 851968K
}
Event: 42.269 GC heap before
{Heap before GC invocations=7 (full 2):
 PSYoungGen      total 196096K, used 6865K [0x00000007aab00000, 0x00000007b8300000, 0x0000000800000000)
  eden space 188416K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b6300000)
  from space 7680K, 89% used [0x00000007b6300000,0x00000007b69b46e8,0x00000007b6a80000)
  to   space 16384K, 0% used [0x00000007b7300000,0x00000007b7300000,0x00000007b8300000)
 ParOldGen       total 125952K, used 22432K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 17% used [0x0000000700000000,0x00000007015e8260,0x0000000707b00000)
 Metaspace       used 35387K, committed 35840K, reserved 917504K
  class space    used 5025K, committed 5248K, reserved 851968K
}
Event: 42.340 GC heap after
{Heap after GC invocations=7 (full 2):
 PSYoungGen      total 196096K, used 0K [0x00000007aab00000, 0x00000007b8300000, 0x0000000800000000)
  eden space 188416K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b6300000)
  from space 7680K, 0% used [0x00000007b6300000,0x00000007b6300000,0x00000007b6a80000)
  to   space 16384K, 0% used [0x00000007b7300000,0x00000007b7300000,0x00000007b8300000)
 ParOldGen       total 125952K, used 29009K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 23% used [0x0000000700000000,0x0000000701c54778,0x0000000707b00000)
 Metaspace       used 35387K, committed 35840K, reserved 917504K
  class space    used 5025K, committed 5248K, reserved 851968K
}
Event: 53.067 GC heap before
{Heap before GC invocations=8 (full 2):
 PSYoungGen      total 196096K, used 188416K [0x00000007aab00000, 0x00000007b8300000, 0x0000000800000000)
  eden space 188416K, 100% used [0x00000007aab00000,0x00000007b6300000,0x00000007b6300000)
  from space 7680K, 0% used [0x00000007b6300000,0x00000007b6300000,0x00000007b6a80000)
  to   space 16384K, 0% used [0x00000007b7300000,0x00000007b7300000,0x00000007b8300000)
 ParOldGen       total 125952K, used 29009K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 23% used [0x0000000700000000,0x0000000701c54778,0x0000000707b00000)
 Metaspace       used 51248K, committed 51840K, reserved 917504K
  class space    used 7132K, committed 7424K, reserved 851968K
}
Event: 53.090 GC heap after
{Heap after GC invocations=8 (full 2):
 PSYoungGen      total 205312K, used 9357K [0x00000007aab00000, 0x00000007c3a00000, 0x0000000800000000)
  eden space 188928K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b6380000)
  from space 16384K, 57% used [0x00000007b7300000,0x00000007b7c23448,0x00000007b8300000)
  to   space 15872K, 0% used [0x00000007b6380000,0x00000007b6380000,0x00000007b7300000)
 ParOldGen       total 125952K, used 29017K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 23% used [0x0000000700000000,0x0000000701c56778,0x0000000707b00000)
 Metaspace       used 51248K, committed 51840K, reserved 917504K
  class space    used 7132K, committed 7424K, reserved 851968K
}
Event: 58.806 GC heap before
{Heap before GC invocations=9 (full 2):
 PSYoungGen      total 205312K, used 198285K [0x00000007aab00000, 0x00000007c3a00000, 0x0000000800000000)
  eden space 188928K, 100% used [0x00000007aab00000,0x00000007b6380000,0x00000007b6380000)
  from space 16384K, 57% used [0x00000007b7300000,0x00000007b7c23448,0x00000007b8300000)
  to   space 15872K, 0% used [0x00000007b6380000,0x00000007b6380000,0x00000007b7300000)
 ParOldGen       total 125952K, used 29017K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 23% used [0x0000000700000000,0x0000000701c56778,0x0000000707b00000)
 Metaspace       used 55911K, committed 56576K, reserved 917504K
  class space    used 7709K, committed 8000K, reserved 851968K
}
Event: 58.838 GC heap after
{Heap after GC invocations=9 (full 2):
 PSYoungGen      total 197632K, used 15840K [0x00000007aab00000, 0x00000007b8380000, 0x0000000800000000)
  eden space 181760K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b5c80000)
  from space 15872K, 99% used [0x00000007b6380000,0x00000007b72f8020,0x00000007b7300000)
  to   space 16896K, 0% used [0x00000007b7300000,0x00000007b7300000,0x00000007b8380000)
 ParOldGen       total 125952K, used 33241K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 26% used [0x0000000700000000,0x0000000702076638,0x0000000707b00000)
 Metaspace       used 55911K, committed 56576K, reserved 917504K
  class space    used 7709K, committed 8000K, reserved 851968K
}
Event: 71.555 GC heap before
{Heap before GC invocations=10 (full 2):
 PSYoungGen      total 197632K, used 197178K [0x00000007aab00000, 0x00000007b8380000, 0x0000000800000000)
  eden space 181760K, 99% used [0x00000007aab00000,0x00000007b5c16820,0x00000007b5c80000)
  from space 15872K, 99% used [0x00000007b6380000,0x00000007b72f8020,0x00000007b7300000)
  to   space 16896K, 0% used [0x00000007b7300000,0x00000007b7300000,0x00000007b8380000)
 ParOldGen       total 125952K, used 33241K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 26% used [0x0000000700000000,0x0000000702076638,0x0000000707b00000)
 Metaspace       used 56684K, committed 57408K, reserved 917504K
  class space    used 7799K, committed 8128K, reserved 851968K
}
Event: 71.593 GC heap after
{Heap after GC invocations=10 (full 2):
 PSYoungGen      total 191488K, used 16865K [0x00000007aab00000, 0x00000007b8780000, 0x0000000800000000)
  eden space 174592K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b5580000)
  from space 16896K, 99% used [0x00000007b7300000,0x00000007b83786e0,0x00000007b8380000)
  to   space 25600K, 0% used [0x00000007b5580000,0x00000007b5580000,0x00000007b6e80000)
 ParOldGen       total 125952K, used 43283K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 34% used [0x0000000700000000,0x0000000702a44c90,0x0000000707b00000)
 Metaspace       used 56684K, committed 57408K, reserved 917504K
  class space    used 7799K, committed 8128K, reserved 851968K
}
Event: 83.885 GC heap before
{Heap before GC invocations=11 (full 2):
 PSYoungGen      total 191488K, used 66918K [0x00000007aab00000, 0x00000007b8780000, 0x0000000800000000)
  eden space 174592K, 28% used [0x00000007aab00000,0x00000007adbe1348,0x00000007b5580000)
  from space 16896K, 99% used [0x00000007b7300000,0x00000007b83786e0,0x00000007b8380000)
  to   space 25600K, 0% used [0x00000007b5580000,0x00000007b5580000,0x00000007b6e80000)
 ParOldGen       total 125952K, used 43283K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 34% used [0x0000000700000000,0x0000000702a44c90,0x0000000707b00000)
 Metaspace       used 59108K, committed 59776K, reserved 917504K
  class space    used 8298K, committed 8640K, reserved 851968K
}
Event: 83.896 GC heap after
{Heap after GC invocations=11 (full 2):
 PSYoungGen      total 189952K, used 15272K [0x00000007aab00000, 0x00000007b8680000, 0x0000000800000000)
  eden space 174592K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b5580000)
  from space 15360K, 99% used [0x00000007b5580000,0x00000007b646a0e0,0x00000007b6480000)
  to   space 25088K, 0% used [0x00000007b6e00000,0x00000007b6e00000,0x00000007b8680000)
 ParOldGen       total 125952K, used 45462K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 36% used [0x0000000700000000,0x0000000702c658c0,0x0000000707b00000)
 Metaspace       used 59108K, committed 59776K, reserved 917504K
  class space    used 8298K, committed 8640K, reserved 851968K
}
Event: 83.896 GC heap before
{Heap before GC invocations=12 (full 3):
 PSYoungGen      total 189952K, used 15272K [0x00000007aab00000, 0x00000007b8680000, 0x0000000800000000)
  eden space 174592K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b5580000)
  from space 15360K, 99% used [0x00000007b5580000,0x00000007b646a0e0,0x00000007b6480000)
  to   space 25088K, 0% used [0x00000007b6e00000,0x00000007b6e00000,0x00000007b8680000)
 ParOldGen       total 125952K, used 45462K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 36% used [0x0000000700000000,0x0000000702c658c0,0x0000000707b00000)
 Metaspace       used 59108K, committed 59776K, reserved 917504K
  class space    used 8298K, committed 8640K, reserved 851968K
}
Event: 84.057 GC heap after
{Heap after GC invocations=12 (full 3):
 PSYoungGen      total 189952K, used 0K [0x00000007aab00000, 0x00000007b8680000, 0x0000000800000000)
  eden space 174592K, 0% used [0x00000007aab00000,0x00000007aab00000,0x00000007b5580000)
  from space 15360K, 0% used [0x00000007b5580000,0x00000007b5580000,0x00000007b6480000)
  to   space 25088K, 0% used [0x00000007b6e00000,0x00000007b6e00000,0x00000007b8680000)
 ParOldGen       total 125952K, used 59226K [0x0000000700000000, 0x0000000707b00000, 0x00000007aab00000)
  object space 125952K, 47% used [0x0000000700000000,0x00000007039d69e8,0x0000000707b00000)
 Metaspace       used 59108K, committed 59776K, reserved 917504K
  class space    used 8298K, committed 8640K, reserved 851968K
}

Dll operation events (15 events):
Event: 0.034 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.dll
Event: 0.108 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\jsvml.dll
Event: 0.583 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\zip.dll
Event: 0.588 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\instrument.dll
Event: 0.601 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\net.dll
Event: 0.692 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\nio.dll
Event: 0.697 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\zip.dll
Event: 2.803 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\jimage.dll
Event: 3.481 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\verify.dll
Event: 4.970 Loaded shared library C:\Talash Ai\project-gradle-home\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
Event: 5.002 Loaded shared library C:\Talash Ai\project-gradle-home\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
Event: 16.415 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\management.dll
Event: 16.424 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\management_ext.dll
Event: 18.701 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\extnet.dll
Event: 23.054 Loaded shared library C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 90.628 Thread 0x000001e3d1691d10 DEOPT PACKING pc=0x000001e387aebee8 sp=0x000000ba638f3b60
Event: 90.628 Thread 0x000001e3d1691d10 DEOPT UNPACKING pc=0x000001e3875866a3 sp=0x000000ba638f3ae0 mode 2
Event: 93.177 Thread 0x000001e3d1691d10 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001e387b5c900 relative=0x0000000000000a80
Event: 93.177 Thread 0x000001e3d1691d10 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001e387b5c900 method=java.util.Collections$SetFromMap.remove(Ljava/lang/Object;)Z @ 5 c2
Event: 93.177 Thread 0x000001e3d1691d10 DEOPT PACKING pc=0x000001e387b5c900 sp=0x000000ba638f5170
Event: 93.177 Thread 0x000001e3d1691d10 DEOPT UNPACKING pc=0x000001e3875866a3 sp=0x000000ba638f5168 mode 2
Event: 93.182 Thread 0x000001e3d1691d10 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001e387b5c900 relative=0x0000000000000a80
Event: 93.182 Thread 0x000001e3d1691d10 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001e387b5c900 method=java.util.Collections$SetFromMap.remove(Ljava/lang/Object;)Z @ 5 c2
Event: 93.182 Thread 0x000001e3d1691d10 DEOPT PACKING pc=0x000001e387b5c900 sp=0x000000ba638f4db0
Event: 93.182 Thread 0x000001e3d1691d10 DEOPT UNPACKING pc=0x000001e3875866a3 sp=0x000000ba638f4da8 mode 2
Event: 93.889 Thread 0x000001e3d1691d10 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001e387e04b60 relative=0x00000000000001a0
Event: 93.889 Thread 0x000001e3d1691d10 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e387e04b60 method=sun.invoke.util.VerifyAccess.isTypeVisible(Ljava/lang/Class;Ljava/lang/Class;)Z @ 77 c2
Event: 93.889 Thread 0x000001e3d1691d10 DEOPT PACKING pc=0x000001e387e04b60 sp=0x000000ba638f36b0
Event: 93.889 Thread 0x000001e3d1691d10 DEOPT UNPACKING pc=0x000001e3875866a3 sp=0x000000ba638f3630 mode 2
Event: 98.484 Thread 0x000001e3d1691d10 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000001e387b5c900 relative=0x0000000000000a80
Event: 98.484 Thread 0x000001e3d1691d10 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001e387b5c900 method=java.util.Collections$SetFromMap.remove(Ljava/lang/Object;)Z @ 5 c2
Event: 98.484 Thread 0x000001e3d1691d10 DEOPT PACKING pc=0x000001e387b5c900 sp=0x000000ba638f4350
Event: 98.484 Thread 0x000001e3d1691d10 DEOPT UNPACKING pc=0x000001e3875866a3 sp=0x000000ba638f4348 mode 2
Event: 99.641 Thread 0x000001e3d1692730 DEOPT PACKING pc=0x000001e3805cc39c sp=0x000000ba63bff410
Event: 99.641 Thread 0x000001e3d1692730 DEOPT UNPACKING pc=0x000001e387586e43 sp=0x000000ba63bfe938 mode 0

Classes loaded (20 events):
Event: 93.551 Loading class javax/xml/stream/XMLStreamConstants
Event: 93.551 Loading class javax/xml/stream/XMLStreamConstants done
Event: 93.564 Loading class javax/xml/stream/XMLStreamReader
Event: 93.564 Loading class javax/xml/stream/XMLStreamReader done
Event: 99.079 Loading class java/nio/charset/IllegalCharsetNameException
Event: 99.200 Loading class java/nio/charset/IllegalCharsetNameException done
Event: 99.200 Loading class java/nio/charset/UnsupportedCharsetException
Event: 99.200 Loading class java/nio/charset/UnsupportedCharsetException done
Event: 99.201 Loading class sun/nio/cs/UTF_32BE
Event: 99.283 Loading class sun/nio/cs/UTF_32BE done
Event: 99.283 Loading class sun/nio/cs/UTF_32LE
Event: 99.297 Loading class sun/nio/cs/UTF_32LE done
Event: 99.297 Loading class sun/nio/cs/MS1251
Event: 99.301 Loading class sun/nio/cs/MS1251 done
Event: 99.724 Loading class java/beans/PropertyChangeSupport
Event: 99.739 Loading class java/beans/PropertyChangeSupport done
Event: 99.739 Loading class java/beans/PropertyChangeSupport$PropertyChangeListenerMap
Event: 99.748 Loading class java/beans/ChangeListenerMap
Event: 99.756 Loading class java/beans/ChangeListenerMap done
Event: 99.756 Loading class java/beans/PropertyChangeSupport$PropertyChangeListenerMap done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 60.813 Thread 0x000001e3d1692730 Exception <a 'java/lang/NoSuchMethodError'{0x00000007b108d3c8}: static Lorg/gradle/internal/build/event/types/AbstractProjectConfigurationResult;.<clinit>()V> (0x00000007b108d3c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 60.815 Thread 0x000001e3d1692730 Exception <a 'java/lang/NoSuchMethodError'{0x00000007b1096e90}: static Lorg/gradle/internal/build/event/types/DefaultPluginApplicationResult;.<clinit>()V> (0x00000007b1096e90) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 60.818 Thread 0x000001e3d1692730 Exception <a 'java/lang/NoSuchMethodError'{0x00000007b10a1290}: static Lorg/gradle/internal/build/event/types/DefaultBinaryPluginIdentifier;.<clinit>()V> (0x00000007b10a1290) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 60.840 Thread 0x000001e3d1692730 Exception <a 'java/lang/NoSuchMethodError'{0x00000007b10b5e20}: static Lorg/gradle/internal/build/event/types/DefaultScriptPluginIdentifier;.<clinit>()V> (0x00000007b10b5e20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 70.857 Thread 0x000001e3d1691d10 Implicit null exception at 0x000001e387f84600 to 0x000001e387f84fa0
Event: 76.474 Thread 0x000001e3d1691d10 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000007acc6b1d8}: Found class java.lang.Object, but interface was expected> (0x00000007acc6b1d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 77.574 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007acd871c0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000007acd871c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 78.418 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ad141c68}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int)'> (0x00000007ad141c68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 82.504 Thread 0x000001e3d1691d10 Implicit null exception at 0x000001e387ce0293 to 0x000001e387ce03f0
Event: 90.601 Thread 0x000001e3d1691d10 Exception <a 'sun/nio/fs/WindowsException'{0x00000007abdb7a38}> (0x00000007abdb7a38) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 90.602 Thread 0x000001e3d1691d10 Exception <a 'sun/nio/fs/WindowsException'{0x00000007abdb9830}> (0x00000007abdb9830) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 91.645 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ac8761d8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object)'> (0x00000007ac8761d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 91.646 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ac87b020}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x00000007ac87b020) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 92.047 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007acdd1d50}: 'long java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007acdd1d50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 92.220 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007acddb3a0}: 'int java.lang.invoke.Invokers$Holder.invoke_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007acddb3a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 92.223 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007acde8340}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000007acde8340) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 92.226 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007acdfbe58}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x00000007acdfbe58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 92.228 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ace0c140}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int, int)'> (0x00000007ace0c140) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 92.230 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ace1c1d0}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, long, long)'> (0x00000007ace1c1d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 92.232 Thread 0x000001e3d1691d10 Exception <a 'java/lang/NoSuchMethodError'{0x00000007ace2bf40}: 'int java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, long, int)'> (0x00000007ace2bf40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 87.085 Executing VM operation: Cleanup
Event: 87.227 Executing VM operation: Cleanup done
Event: 89.240 Executing VM operation: Cleanup
Event: 89.256 Executing VM operation: Cleanup done
Event: 89.649 Executing VM operation: HandshakeAllThreads
Event: 89.649 Executing VM operation: HandshakeAllThreads done
Event: 90.655 Executing VM operation: Cleanup
Event: 90.812 Executing VM operation: Cleanup done
Event: 91.658 Executing VM operation: HandshakeAllThreads
Event: 91.658 Executing VM operation: HandshakeAllThreads done
Event: 92.661 Executing VM operation: Cleanup
Event: 92.661 Executing VM operation: Cleanup done
Event: 93.669 Executing VM operation: Cleanup
Event: 93.685 Executing VM operation: Cleanup done
Event: 95.709 Executing VM operation: Cleanup
Event: 95.741 Executing VM operation: Cleanup done
Event: 98.770 Executing VM operation: Cleanup
Event: 98.786 Executing VM operation: Cleanup done
Event: 99.595 Executing VM operation: HandshakeAllThreads
Event: 99.595 Executing VM operation: HandshakeAllThreads done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e55c10
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e57910
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e59b90
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e5a010
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e5a490
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e5a910
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e65910
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e71d10
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e72e10
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e81c10
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e8dc90
Event: 76.590 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380e90110
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380ec7110
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380ec7510
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380ec9d10
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380ecac10
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380ed6f90
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380edf610
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380ee0110
Event: 76.597 Thread 0x000001e3ffa5cf60 flushing  nmethod 0x000001e380ee8410

Events (20 events):
Event: 38.330 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d7d90
Event: 38.510 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d87b0
Event: 39.435 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d8cc0
Event: 39.865 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d9bf0
Event: 40.743 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d4af0
Event: 46.115 Thread 0x000001e3ff9ece90 Thread added: 0x000001e3d27b3a00
Event: 47.758 Thread 0x000001e3d27b3a00 Thread exited: 0x000001e3d27b3a00
Event: 56.178 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d5a20
Event: 56.179 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d2780
Event: 56.180 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d3bc0
Event: 56.742 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d6440
Event: 56.755 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d18d6e60
Event: 56.758 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d5c6e810
Event: 57.872 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d5c6c9b0
Event: 57.873 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d5c6ddf0
Event: 57.874 Thread 0x000001e3d1691d10 Thread added: 0x000001e3d5c6b060
Event: 60.103 Thread 0x000001e3ff9e9920 Thread added: 0x000001e3f4b9bc80
Event: 60.658 Thread 0x000001e3f4b9bc80 Thread exited: 0x000001e3f4b9bc80
Event: 60.750 Thread 0x000001e3ff9ece90 Thread added: 0x000001e3d4842450
Event: 61.511 Thread 0x000001e3d4842450 Thread exited: 0x000001e3d4842450


Dynamic libraries:
0x00007ff79c290000 - 0x00007ff79c29e000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.exe
0x00007ffd2e250000 - 0x00007ffd2e448000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd2c2c0000 - 0x00007ffd2c382000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd2b8d0000 - 0x00007ffd2bbc6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd2bc00000 - 0x00007ffd2bd00000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd264f0000 - 0x00007ffd26507000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\jli.dll
0x00007ffd2c3f0000 - 0x00007ffd2c58d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd2bd00000 - 0x00007ffd2bd22000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd2d090000 - 0x00007ffd2d0bb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd2bde0000 - 0x00007ffd2befa000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd2c060000 - 0x00007ffd2c0fd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd24950000 - 0x00007ffd2496e000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffd19070000 - 0x00007ffd1930a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16\COMCTL32.dll
0x00007ffd2c5f0000 - 0x00007ffd2c68e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd2df70000 - 0x00007ffd2df9f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd26eb0000 - 0x00007ffd26ebc000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffcff5c0000 - 0x00007ffcff64d000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffc878e0000 - 0x00007ffc8854b000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffd2c690000 - 0x00007ffd2c741000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd2dc00000 - 0x00007ffd2dc9f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd2dfb0000 - 0x00007ffd2e0d3000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd2bbd0000 - 0x00007ffd2bbf7000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffd2d400000 - 0x00007ffd2d46b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd2b730000 - 0x00007ffd2b77b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd1c7a0000 - 0x00007ffd1c7c7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd241a0000 - 0x00007ffd241aa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd2b710000 - 0x00007ffd2b722000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd29750000 - 0x00007ffd29762000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd24940000 - 0x00007ffd2494a000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\jimage.dll
0x00007ffd19330000 - 0x00007ffd19514000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd0b090000 - 0x00007ffd0b0c4000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd2bf50000 - 0x00007ffd2bfd2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd24930000 - 0x00007ffd2493e000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\instrument.dll
0x00007ffd1c420000 - 0x00007ffd1c445000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\java.dll
0x00007ffcfbf20000 - 0x00007ffcfbff7000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffd2d470000 - 0x00007ffd2dbdf000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd29950000 - 0x00007ffd2a0f3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd2ccb0000 - 0x00007ffd2d005000 	C:\WINDOWS\System32\combase.dll
0x00007ffd2b230000 - 0x00007ffd2b25b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffd2d0c0000 - 0x00007ffd2d18d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffd2d190000 - 0x00007ffd2d23d000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd2c260000 - 0x00007ffd2c2b5000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd2b800000 - 0x00007ffd2b825000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd24910000 - 0x00007ffd24928000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\zip.dll
0x00007ffd1b940000 - 0x00007ffd1b959000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\net.dll
0x00007ffd1d6b0000 - 0x00007ffd1d7ba000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd2af90000 - 0x00007ffd2affc000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffd1b880000 - 0x00007ffd1b896000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\nio.dll
0x00007ffd24880000 - 0x00007ffd24890000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\verify.dll
0x00007ffcffe10000 - 0x00007ffcffe37000 	C:\Talash Ai\project-gradle-home\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64\native-platform.dll
0x00007ffca7530000 - 0x00007ffca7674000 	C:\Talash Ai\project-gradle-home\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64\native-platform-file-events.dll
0x00007ffd248e0000 - 0x00007ffd248ea000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\management.dll
0x00007ffd241c0000 - 0x00007ffd241cb000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffd2dfa0000 - 0x00007ffd2dfa8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd2b180000 - 0x00007ffd2b198000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd2a8b0000 - 0x00007ffd2a8e8000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd2b780000 - 0x00007ffd2b7ae000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd2b1a0000 - 0x00007ffd2b1ac000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffd2ac70000 - 0x00007ffd2acab000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd2dbe0000 - 0x00007ffd2dbe8000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd21d60000 - 0x00007ffd21d77000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd22930000 - 0x00007ffd2294d000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffd2acb0000 - 0x00007ffd2ad7a000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffd1c670000 - 0x00007ffd1c679000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\extnet.dll
0x00007ffd1b730000 - 0x00007ffd1b73e000 	C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffd2c100000 - 0x00007ffd2c25c000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffd2b2a0000 - 0x00007ffd2b2c7000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffd2b260000 - 0x00007ffd2b29b000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffcd7950000 - 0x00007ffcd7957000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.4355_none_60b8b9eb71f62e16;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin\server;C:\Talash Ai\project-gradle-home\native\c067742578af261105cb4f569cf0c3c89f3d7b1fecec35dd04571415982c5e48\windows-amd64;C:\Talash Ai\project-gradle-home\native\100fb08df4bc3b14c8652ba06237920a3bd2aa13389f12d3474272988ae205f9\windows-amd64

VM Arguments:
jvm_args: -XX:+HeapDumpOnOutOfMemoryError -XX:+UseParallelGC -XX:MaxMetaspaceSize=1g --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED -Xmx4g -Dfile.encoding=utf8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Talash Ai\project-gradle-home\wrapper\dists\gradle-8.10-bin\deqhafrv1ntovfmgh0nh3npr9\gradle-8.10\lib\agents\gradle-instrumentation-agent-8.10.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.10
java_class_path (initial): C:\Talash Ai\project-gradle-home\wrapper\dists\gradle-8.10-bin\deqhafrv1ntovfmgh0nh3npr9\gradle-8.10\lib\gradle-daemon-main-8.10.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 192937984                                 {product} {ergonomic}
   size_t MaxHeapSize                              = 4294967296                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 1431306240                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
   size_t NewSize                                  = 63963136                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
   size_t OldSize                                  = 128974848                                 {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4294967296                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
PATH=C:\Program Files\Google\Chrome\Application;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Users\<USER>\Desktop\coursera\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-17;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;d:\trae\Trae\resources\app\bin\lib
USERNAME=sohaib
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 78 Stepping 3, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5678)
OS uptime: 0 days 3:14 hours

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 78 stepping 3 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt
Processor Information for all 4 processors :
  Max Mhz: 2496, Current Mhz: 2396, Mhz Limit: 2396

Memory: 4k page, system-wide physical 11687M (425M free)
TotalPageFile size 26897M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 418M, peak: 466M
current process commit charge ("private bytes"): 673M, peak: 846M

vm_info: OpenJDK 64-Bit Server VM (17.0.14+7) for windows-amd64 JRE (17.0.14+7), built on Jan 21 2025 23:40:15 by "admin" with MS VC++ 17.7 (VS2022)

END.
