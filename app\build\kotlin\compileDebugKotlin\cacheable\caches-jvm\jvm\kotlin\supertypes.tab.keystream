#com.app.wordifynumbers.MainActivity/com.app.wordifynumbers.NumberConverterViewModel6com.app.wordifynumbers.NumberConverterViewModelFactorycom.app.wordifynumbers.TestApp=com.app.wordifynumbers.data.CalculatorPreferences.$serializer;com.app.wordifynumbers.data.CalculatorPreferencesSerializer(com.app.wordifynumbers.data.NumberFormat%com.app.wordifynumbers.data.EntryType1com.app.wordifynumbers.data.SimpleFinanceDatabase"com.app.wordifynumbers.data.Entity&com.app.wordifynumbers.data.PrimaryKeycom.app.wordifynumbers.data.Dao$com.app.wordifynumbers.data.Database*com.app.wordifynumbers.data.TypeConverters"com.app.wordifynumbers.data.Insert"com.app.wordifynumbers.data.Update"com.app.wordifynumbers.data.Delete!com.app.wordifynumbers.data.Query)com.app.wordifynumbers.data.TypeConverter5com.app.wordifynumbers.data.SimpleFinanceDatabaseStub5com.app.wordifynumbers.data.SimpleFinanceEntryDaoStub(com.app.wordifynumbers.model.NumberScale5com.app.wordifynumbers.performance.PerformanceManagerCcom.app.wordifynumbers.privacy.PrivacyManager.DataCollectionPurpose.com.app.wordifynumbers.ui.components.AngleUnit/com.app.wordifynumbers.ui.components.ButtonType4com.app.wordifynumbers.ui.components.NeonRippleThemeHcom.app.wordifynumbers.ui.components.UICalculatorPreferences.$serializer1com.app.wordifynumbers.ui.components.NumberFormat4com.app.wordifynumbers.ui.components.ValidationState5com.app.wordifynumbers.ui.components.NumberValidation6com.app.wordifynumbers.ui.components.VisualizationType/com.app.wordifynumbers.ui.screens.ShapeCategory1com.app.wordifynumbers.ui.screens.MeasurementUnit4com.app.wordifynumbers.ui.screens.CalculatorCategory6com.app.wordifynumbers.ui.screens.DateDifferenceResult5com.app.wordifynumbers.ui.screens.DateOperationResult0com.app.wordifynumbers.ui.screens.PercentageMode4com.app.wordifynumbers.ui.screens.PercentageCategory7com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel?com.app.wordifynumbers.ui.viewmodel.DateTimeCalculatorViewModel,com.app.wordifynumbers.ui.viewmodel.DateMode,com.app.wordifynumbers.ui.viewmodel.TimeMode2com.app.wordifynumbers.ui.viewmodel.CalculatorMode=com.app.wordifynumbers.ui.viewmodel.HealthCalculatorViewModel.com.app.wordifynumbers.ui.viewmodel.WeightUnit.com.app.wordifynumbers.ui.viewmodel.HeightUnit*com.app.wordifynumbers.ui.viewmodel.Gender1com.app.wordifynumbers.ui.viewmodel.ActivityLevel/com.app.wordifynumbers.ui.viewmodel.BMICategory/com.app.wordifynumbers.ui.viewmodel.WHRCategory8com.app.wordifynumbers.ui.viewmodel.HealthCalculatorMode9com.app.wordifynumbers.ui.viewmodel.LargeNumbersViewModelAcom.app.wordifynumbers.ui.viewmodel.ProgrammerCalculatorViewModel.com.app.wordifynumbers.ui.viewmodel.NumberBaseAcom.app.wordifynumbers.ui.viewmodel.StatisticsCalculatorViewModel5com.app.wordifynumbers.ui.viewmodel.VisualizationType>com.app.wordifynumbers.util.CalculationHistoryItem.$serializer+com.app.wordifynumbers.util.CalculationType,com.app.wordifynumbers.util.ComplexOperation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                