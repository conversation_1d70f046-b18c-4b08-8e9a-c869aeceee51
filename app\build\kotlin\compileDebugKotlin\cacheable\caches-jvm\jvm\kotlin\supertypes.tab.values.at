/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory$ #androidx.activity.ComponentActivity3 2kotlinx.serialization.internal.GeneratedSerializer# "androidx.datastore.core.Serializer kotlin.Enum kotlin.Enum) (com.app.wordifynumbers.data.RoomDatabase kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation2 1com.app.wordifynumbers.data.SimpleFinanceDatabase2 1com.app.wordifynumbers.data.SimpleFinanceEntryDao kotlin.Enum, +androidx.lifecycle.DefaultLifecycleObserver kotlin.Enum kotlin.Enum kotlin.Enum- ,androidx.compose.material.ripple.RippleTheme3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum- ,com.app.wordifynumbers.ui.screens.DateResult- ,com.app.wordifynumbers.ui.screens.DateResult kotlin.Enum kotlin.Enum$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum