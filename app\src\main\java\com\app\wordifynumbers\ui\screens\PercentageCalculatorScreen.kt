package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.navigation.CalculatorBackHandler
import com.app.wordifynumbers.util.FeedbackUtil
import kotlin.math.*

/**
 * Professional Percentage Calculator with comprehensive calculation modes
 * Features: Basic percentages, financial calculations, visual representations
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PercentageCalculatorScreen(modifier: Modifier = Modifier) {
    var selectedMode by remember { mutableStateOf(PercentageMode.BASIC_PERCENT) }
    var input1 by remember { mutableStateOf("") }
    var input2 by remember { mutableStateOf("") }
    var input3 by remember { mutableStateOf("") }
    var result by remember { mutableStateOf<PercentageResult?>(null) }
    var showSteps by remember { mutableStateOf(false) }
    var showInfoDialog by remember { mutableStateOf(false) }

    val context = LocalContext.current
    val accentColor = NeonCyan

    // Handle back button press using standardized handler
    CalculatorBackHandler(
        showInfoDialog = showInfoDialog,
        onInfoDialogDismiss = { showInfoDialog = false }
    )

    // Calculate result whenever inputs change
    LaunchedEffect(selectedMode, input1, input2, input3) {
        result = calculatePercentage(selectedMode, input1, input2, input3)
    }

    // Info Dialog
    if (showInfoDialog) {
        PercentageInfoDialog(
            onDismiss = { showInfoDialog = false }
        )
    }

    StandardCalculatorLayout(
        title = "Professional Percentage Calculator",
        icon = Icons.Default.Percent,
        accentColor = accentColor,
        showInfoButton = true,
        onInfoClick = {
            showInfoDialog = true
            FeedbackUtil.buttonPress(context)
        },

        // Input Section
        inputSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // Mode Selection with Categories
                PercentageModeSelector(
                    selectedMode = selectedMode,
                    onModeSelected = { mode ->
                        selectedMode = mode
                        input1 = ""
                        input2 = ""
                        input3 = ""
                        result = null
                        FeedbackUtil.buttonPress(context)
                    },
                    accentColor = accentColor
                )

                // Input Fields based on selected mode
                PercentageInputSection(
                    mode = selectedMode,
                    input1 = input1,
                    input2 = input2,
                    input3 = input3,
                    onInput1Change = { input1 = it },
                    onInput2Change = { input2 = it },
                    onInput3Change = { input3 = it },
                    accentColor = accentColor
                )
            }
        },

        // Action Buttons
        actionButtons = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                CalculatorActionButton(
                    text = "Clear All",
                    onClick = {
                        input1 = ""
                        input2 = ""
                        input3 = ""
                        result = null
                        FeedbackUtil.buttonPress(context)
                    },
                    accentColor = NeonRed,
                    modifier = Modifier.weight(1f),
                    icon = Icons.Default.Clear
                )

                CalculatorActionButton(
                    text = if (showSteps) "Hide Steps" else "Show Steps",
                    onClick = {
                        showSteps = !showSteps
                        FeedbackUtil.buttonPress(context)
                    },
                    accentColor = NeonPurple,
                    modifier = Modifier.weight(1f),
                    icon = if (showSteps) Icons.Default.VisibilityOff else Icons.Default.Visibility
                )
            }
        },

        // Result Section
        resultSection = {
            result?.let { percentResult ->
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Main Result Display
                    PercentageResultDisplay(
                        result = percentResult,
                        accentColor = accentColor,
                        showSteps = showSteps
                    )

                    // Visual Representation
                    PercentageVisualization(
                        result = percentResult,
                        accentColor = accentColor
                    )

                    // Quick Actions
                    PercentageQuickActions(
                        result = percentResult,
                        onActionClick = { action ->
                            // Handle quick actions like copy, share, etc.
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }
            } ?: run {
                // Empty state
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Percent,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = accentColor.copy(alpha = 0.5f)
                    )
                    Text(
                        text = "Enter values to calculate percentages",
                        style = MaterialTheme.typography.bodyLarge,
                        color = NeonText.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "Choose a calculation mode and enter your values",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText.copy(alpha = 0.5f),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    )
}

// ================================================================================================
// PERCENTAGE CALCULATION MODES AND DATA CLASSES
// ================================================================================================

/**
 * Comprehensive percentage calculation modes for professional use
 */
enum class PercentageMode(
    val displayName: String,
    val description: String,
    val category: PercentageCategory,
    val icon: ImageVector,
    val formula: String
) {
    // Basic Percentage Calculations
    BASIC_PERCENT(
        "What is X% of Y?",
        "Calculate percentage of a value",
        PercentageCategory.BASIC,
        Icons.Default.Calculate,
        "Result = (X ÷ 100) × Y"
    ),
    PERCENT_OF_TOTAL(
        "X is what % of Y?",
        "Find what percentage one value is of another",
        PercentageCategory.BASIC,
        Icons.Default.Percent,
        "Result = (X ÷ Y) × 100"
    ),
    FIND_TOTAL(
        "X is Y% of what?",
        "Find the total when you know the part and percentage",
        PercentageCategory.BASIC,
        Icons.Default.Search,
        "Result = X ÷ (Y ÷ 100)"
    ),

    // Percentage Change
    PERCENT_INCREASE(
        "Percentage Increase",
        "Calculate percentage increase between two values",
        PercentageCategory.CHANGE,
        Icons.Default.TrendingUp,
        "Result = ((New - Old) ÷ Old) × 100"
    ),
    PERCENT_DECREASE(
        "Percentage Decrease",
        "Calculate percentage decrease between two values",
        PercentageCategory.CHANGE,
        Icons.Default.TrendingDown,
        "Result = ((Old - New) ÷ Old) × 100"
    ),
    PERCENT_DIFFERENCE(
        "Percentage Difference",
        "Calculate absolute percentage difference",
        PercentageCategory.CHANGE,
        Icons.Default.CompareArrows,
        "Result = |A - B| ÷ ((A + B) ÷ 2) × 100"
    ),

    // Financial Calculations
    MARKUP(
        "Markup Calculation",
        "Calculate markup percentage and selling price",
        PercentageCategory.FINANCIAL,
        Icons.Default.AttachMoney,
        "Markup% = ((Selling - Cost) ÷ Cost) × 100"
    ),
    DISCOUNT(
        "Discount Calculation",
        "Calculate discount amount and final price",
        PercentageCategory.FINANCIAL,
        Icons.Default.LocalOffer,
        "Discount = Original × (Discount% ÷ 100)"
    ),
    TAX_CALCULATION(
        "Tax Calculation",
        "Calculate tax amount and total with tax",
        PercentageCategory.FINANCIAL,
        Icons.Default.Receipt,
        "Tax = Amount × (Tax% ÷ 100)"
    ),
    COMMISSION(
        "Commission Calculation",
        "Calculate commission based on sales",
        PercentageCategory.FINANCIAL,
        Icons.Default.Handshake,
        "Commission = Sales × (Rate% ÷ 100)"
    ),

    // Advanced Calculations
    COMPOUND_PERCENTAGE(
        "Compound Percentage",
        "Calculate compound percentage changes",
        PercentageCategory.ADVANCED,
        Icons.Default.Timeline,
        "Result = Initial × (1 + Rate%)^n"
    ),
    PERCENTAGE_POINTS(
        "Percentage Points",
        "Calculate difference in percentage points",
        PercentageCategory.ADVANCED,
        Icons.Default.LinearScale,
        "Points = New% - Old%"
    ),
    WEIGHTED_AVERAGE(
        "Weighted Percentage",
        "Calculate weighted average percentage",
        PercentageCategory.ADVANCED,
        Icons.Default.Balance,
        "Result = Σ(Value × Weight) ÷ Σ(Weight)"
    )
}

enum class PercentageCategory(val displayName: String, val color: Color) {
    BASIC("Basic", NeonBlue),
    CHANGE("Change", NeonGreen),
    FINANCIAL("Financial", NeonOrange),
    ADVANCED("Advanced", NeonPurple)
}

/**
 * Comprehensive result data class for percentage calculations
 */
data class PercentageResult(
    val mode: PercentageMode,
    val mainResult: String,
    val formattedResult: String,
    val numericResult: Double,
    val details: List<String>,
    val steps: List<CalculationStep>,
    val visualData: VisualData?,
    val additionalInfo: Map<String, String> = emptyMap()
)

data class CalculationStep(
    val stepNumber: Int,
    val description: String,
    val formula: String,
    val calculation: String,
    val result: String
)

data class VisualData(
    val percentage: Double,
    val segments: List<PercentageSegment> = emptyList(),
    val showAsBar: Boolean = true,
    val showAsPie: Boolean = false
)

data class PercentageSegment(
    val label: String,
    val value: Double,
    val percentage: Double,
    val color: Color
)

// ================================================================================================
// CALCULATION LOGIC
// ================================================================================================

/**
 * Main calculation function that handles all percentage calculation modes
 */
fun calculatePercentage(
    mode: PercentageMode,
    input1: String,
    input2: String,
    input3: String = ""
): PercentageResult? {
    return try {
        when (mode) {
            PercentageMode.BASIC_PERCENT -> calculateBasicPercent(input1, input2)
            PercentageMode.PERCENT_OF_TOTAL -> calculatePercentOfTotal(input1, input2)
            PercentageMode.FIND_TOTAL -> calculateFindTotal(input1, input2)
            PercentageMode.PERCENT_INCREASE -> calculatePercentIncrease(input1, input2)
            PercentageMode.PERCENT_DECREASE -> calculatePercentDecrease(input1, input2)
            PercentageMode.PERCENT_DIFFERENCE -> calculatePercentDifference(input1, input2)
            PercentageMode.MARKUP -> calculateMarkup(input1, input2)
            PercentageMode.DISCOUNT -> calculateDiscount(input1, input2)
            PercentageMode.TAX_CALCULATION -> calculateTax(input1, input2)
            PercentageMode.COMMISSION -> calculateCommission(input1, input2)
            PercentageMode.COMPOUND_PERCENTAGE -> calculateCompoundPercentage(input1, input2, input3)
            PercentageMode.PERCENTAGE_POINTS -> calculatePercentagePoints(input1, input2)
            PercentageMode.WEIGHTED_AVERAGE -> calculateWeightedAverage(input1, input2, input3)
        }
    } catch (e: Exception) {
        null
    }
}

private fun calculateBasicPercent(percentStr: String, valueStr: String): PercentageResult? {
    val percent = percentStr.toDoubleOrNull() ?: return null
    val value = valueStr.toDoubleOrNull() ?: return null

    val result = (percent / 100.0) * value

    return PercentageResult(
        mode = PercentageMode.BASIC_PERCENT,
        mainResult = "%.2f".format(result),
        formattedResult = "%.2f".format(result),
        numericResult = result,
        details = listOf(
            "Calculation: $percent% of $value",
            "Formula: ($percent ÷ 100) × $value",
            "Decimal equivalent: ${percent / 100.0}",
            "Result: $result"
        ),
        steps = listOf(
            CalculationStep(1, "Convert percentage to decimal", "$percent% ÷ 100", "$percent ÷ 100", "${percent / 100.0}"),
            CalculationStep(2, "Multiply by the value", "${percent / 100.0} × $value", "${percent / 100.0} × $value", "$result")
        ),
        visualData = VisualData(
            percentage = percent,
            segments = listOf(
                PercentageSegment("Result", result, percent, NeonCyan),
                PercentageSegment("Remaining", value - result, 100 - percent, NeonCard)
            )
        )
    )
}

private fun calculatePercentOfTotal(partStr: String, totalStr: String): PercentageResult? {
    val part = partStr.toDoubleOrNull() ?: return null
    val total = totalStr.toDoubleOrNull() ?: return null

    if (total == 0.0) return null

    val percentage = (part / total) * 100.0

    return PercentageResult(
        mode = PercentageMode.PERCENT_OF_TOTAL,
        mainResult = "%.2f%%".format(percentage),
        formattedResult = "%.2f%%".format(percentage),
        numericResult = percentage,
        details = listOf(
            "Calculation: $part is what % of $total",
            "Formula: ($part ÷ $total) × 100",
            "Fraction: $part/$total",
            "Result: $percentage%"
        ),
        steps = listOf(
            CalculationStep(1, "Divide part by total", "$part ÷ $total", "$part ÷ $total", "${part / total}"),
            CalculationStep(2, "Convert to percentage", "${part / total} × 100", "${part / total} × 100", "$percentage%")
        ),
        visualData = VisualData(
            percentage = percentage,
            segments = listOf(
                PercentageSegment("Part", part, percentage, NeonGreen),
                PercentageSegment("Remaining", total - part, 100 - percentage, NeonCard)
            )
        )
    )
}

private fun calculateFindTotal(partStr: String, percentStr: String): PercentageResult? {
    val part = partStr.toDoubleOrNull() ?: return null
    val percent = percentStr.toDoubleOrNull() ?: return null

    if (percent == 0.0) return null

    val total = part / (percent / 100.0)

    return PercentageResult(
        mode = PercentageMode.FIND_TOTAL,
        mainResult = "%.2f".format(total),
        formattedResult = "%.2f".format(total),
        numericResult = total,
        details = listOf(
            "Calculation: $part is $percent% of what?",
            "Formula: $part ÷ ($percent ÷ 100)",
            "Total value: $total",
            "Verification: ${percent}% of $total = ${(percent / 100.0) * total}"
        ),
        steps = listOf(
            CalculationStep(1, "Convert percentage to decimal", "$percent% ÷ 100", "$percent ÷ 100", "${percent / 100.0}"),
            CalculationStep(2, "Divide part by decimal", "$part ÷ ${percent / 100.0}", "$part ÷ ${percent / 100.0}", "$total")
        ),
        visualData = VisualData(
            percentage = percent,
            segments = listOf(
                PercentageSegment("Known Part", part, percent, NeonBlue),
                PercentageSegment("Unknown Part", total - part, 100 - percent, NeonCard)
            )
        )
    )
}

private fun calculatePercentIncrease(oldStr: String, newStr: String): PercentageResult? {
    val oldValue = oldStr.toDoubleOrNull() ?: return null
    val newValue = newStr.toDoubleOrNull() ?: return null

    if (oldValue == 0.0) return null

    val increase = newValue - oldValue
    val percentage = (increase / oldValue) * 100.0

    return PercentageResult(
        mode = PercentageMode.PERCENT_INCREASE,
        mainResult = "%.2f%%".format(percentage),
        formattedResult = "%.2f%% increase".format(percentage),
        numericResult = percentage,
        details = listOf(
            "Original value: $oldValue",
            "New value: $newValue",
            "Increase amount: $increase",
            "Percentage increase: $percentage%"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate increase", "$newValue - $oldValue", "$newValue - $oldValue", "$increase"),
            CalculationStep(2, "Divide by original", "$increase ÷ $oldValue", "$increase ÷ $oldValue", "${increase / oldValue}"),
            CalculationStep(3, "Convert to percentage", "${increase / oldValue} × 100", "${increase / oldValue} × 100", "$percentage%")
        ),
        visualData = VisualData(
            percentage = abs(percentage),
            segments = listOf(
                PercentageSegment("Original", oldValue, 100.0, NeonBlue),
                PercentageSegment("Increase", increase, percentage, NeonGreen)
            )
        ),
        additionalInfo = mapOf(
            "Change Type" to "Increase",
            "Multiplier" to "%.3f".format(newValue / oldValue)
        )
    )
}

private fun calculatePercentDecrease(oldStr: String, newStr: String): PercentageResult? {
    val oldValue = oldStr.toDoubleOrNull() ?: return null
    val newValue = newStr.toDoubleOrNull() ?: return null

    if (oldValue == 0.0) return null

    val decrease = oldValue - newValue
    val percentage = (decrease / oldValue) * 100.0

    return PercentageResult(
        mode = PercentageMode.PERCENT_DECREASE,
        mainResult = "%.2f%%".format(percentage),
        formattedResult = "%.2f%% decrease".format(percentage),
        numericResult = percentage,
        details = listOf(
            "Original value: $oldValue",
            "New value: $newValue",
            "Decrease amount: $decrease",
            "Percentage decrease: $percentage%"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate decrease", "$oldValue - $newValue", "$oldValue - $newValue", "$decrease"),
            CalculationStep(2, "Divide by original", "$decrease ÷ $oldValue", "$decrease ÷ $oldValue", "${decrease / oldValue}"),
            CalculationStep(3, "Convert to percentage", "${decrease / oldValue} × 100", "${decrease / oldValue} × 100", "$percentage%")
        ),
        visualData = VisualData(
            percentage = abs(percentage),
            segments = listOf(
                PercentageSegment("Remaining", newValue, 100 - percentage, NeonBlue),
                PercentageSegment("Decrease", decrease, percentage, NeonRed)
            )
        ),
        additionalInfo = mapOf(
            "Change Type" to "Decrease",
            "Multiplier" to "%.3f".format(newValue / oldValue)
        )
    )
}

private fun calculatePercentDifference(value1Str: String, value2Str: String): PercentageResult? {
    val value1 = value1Str.toDoubleOrNull() ?: return null
    val value2 = value2Str.toDoubleOrNull() ?: return null

    val average = (value1 + value2) / 2.0
    if (average == 0.0) return null

    val difference = abs(value1 - value2)
    val percentage = (difference / average) * 100.0

    return PercentageResult(
        mode = PercentageMode.PERCENT_DIFFERENCE,
        mainResult = "%.2f%%".format(percentage),
        formattedResult = "%.2f%% difference".format(percentage),
        numericResult = percentage,
        details = listOf(
            "Value 1: $value1",
            "Value 2: $value2",
            "Absolute difference: $difference",
            "Average: $average",
            "Percentage difference: $percentage%"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate absolute difference", "|$value1 - $value2|", "|$value1 - $value2|", "$difference"),
            CalculationStep(2, "Calculate average", "($value1 + $value2) ÷ 2", "($value1 + $value2) ÷ 2", "$average"),
            CalculationStep(3, "Calculate percentage", "$difference ÷ $average × 100", "$difference ÷ $average × 100", "$percentage%")
        ),
        visualData = VisualData(
            percentage = percentage,
            segments = listOf(
                PercentageSegment("Value 1", value1, value1 / (value1 + value2) * 100, NeonBlue),
                PercentageSegment("Value 2", value2, value2 / (value1 + value2) * 100, NeonGreen)
            )
        )
    )
}

private fun calculateMarkup(costStr: String, markupPercentStr: String): PercentageResult? {
    val cost = costStr.toDoubleOrNull() ?: return null
    val markupPercent = markupPercentStr.toDoubleOrNull() ?: return null

    val markupAmount = cost * (markupPercent / 100.0)
    val sellingPrice = cost + markupAmount

    return PercentageResult(
        mode = PercentageMode.MARKUP,
        mainResult = "%.2f".format(sellingPrice),
        formattedResult = "Selling Price: %.2f".format(sellingPrice),
        numericResult = sellingPrice,
        details = listOf(
            "Cost price: $cost",
            "Markup percentage: $markupPercent%",
            "Markup amount: $markupAmount",
            "Selling price: $sellingPrice",
            "Profit margin: ${markupPercent / (100 + markupPercent) * 100}%"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate markup amount", "$cost × ($markupPercent ÷ 100)", "$cost × ${markupPercent / 100.0}", "$markupAmount"),
            CalculationStep(2, "Add to cost price", "$cost + $markupAmount", "$cost + $markupAmount", "$sellingPrice")
        ),
        visualData = VisualData(
            percentage = markupPercent,
            segments = listOf(
                PercentageSegment("Cost", cost, 100.0, NeonBlue),
                PercentageSegment("Markup", markupAmount, markupPercent, NeonGreen)
            )
        ),
        additionalInfo = mapOf(
            "Markup Amount" to "%.2f".format(markupAmount),
            "Profit Margin" to "%.2f%%".format(markupPercent / (100 + markupPercent) * 100)
        )
    )
}

private fun calculateDiscount(originalStr: String, discountPercentStr: String): PercentageResult? {
    val original = originalStr.toDoubleOrNull() ?: return null
    val discountPercent = discountPercentStr.toDoubleOrNull() ?: return null

    val discountAmount = original * (discountPercent / 100.0)
    val finalPrice = original - discountAmount

    return PercentageResult(
        mode = PercentageMode.DISCOUNT,
        mainResult = "%.2f".format(finalPrice),
        formattedResult = "Final Price: %.2f".format(finalPrice),
        numericResult = finalPrice,
        details = listOf(
            "Original price: $original",
            "Discount percentage: $discountPercent%",
            "Discount amount: $discountAmount",
            "Final price: $finalPrice",
            "You save: $discountAmount"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate discount amount", "$original × ($discountPercent ÷ 100)", "$original × ${discountPercent / 100.0}", "$discountAmount"),
            CalculationStep(2, "Subtract from original", "$original - $discountAmount", "$original - $discountAmount", "$finalPrice")
        ),
        visualData = VisualData(
            percentage = discountPercent,
            segments = listOf(
                PercentageSegment("Final Price", finalPrice, 100 - discountPercent, NeonGreen),
                PercentageSegment("Discount", discountAmount, discountPercent, NeonRed)
            )
        ),
        additionalInfo = mapOf(
            "Savings" to "%.2f".format(discountAmount),
            "Savings Percentage" to "$discountPercent%"
        )
    )
}

private fun calculateTax(amountStr: String, taxPercentStr: String): PercentageResult? {
    val amount = amountStr.toDoubleOrNull() ?: return null
    val taxPercent = taxPercentStr.toDoubleOrNull() ?: return null

    val taxAmount = amount * (taxPercent / 100.0)
    val totalWithTax = amount + taxAmount

    return PercentageResult(
        mode = PercentageMode.TAX_CALCULATION,
        mainResult = "%.2f".format(totalWithTax),
        formattedResult = "Total with Tax: %.2f".format(totalWithTax),
        numericResult = totalWithTax,
        details = listOf(
            "Amount before tax: $amount",
            "Tax rate: $taxPercent%",
            "Tax amount: $taxAmount",
            "Total with tax: $totalWithTax"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate tax amount", "$amount × ($taxPercent ÷ 100)", "$amount × ${taxPercent / 100.0}", "$taxAmount"),
            CalculationStep(2, "Add to original amount", "$amount + $taxAmount", "$amount + $taxAmount", "$totalWithTax")
        ),
        visualData = VisualData(
            percentage = taxPercent,
            segments = listOf(
                PercentageSegment("Original Amount", amount, 100.0, NeonBlue),
                PercentageSegment("Tax", taxAmount, taxPercent, NeonOrange)
            )
        ),
        additionalInfo = mapOf(
            "Tax Amount" to "%.2f".format(taxAmount),
            "Effective Rate" to "$taxPercent%"
        )
    )
}

private fun calculateCommission(salesStr: String, rateStr: String): PercentageResult? {
    val sales = salesStr.toDoubleOrNull() ?: return null
    val rate = rateStr.toDoubleOrNull() ?: return null

    val commission = sales * (rate / 100.0)
    val netAmount = sales - commission

    return PercentageResult(
        mode = PercentageMode.COMMISSION,
        mainResult = "%.2f".format(commission),
        formattedResult = "Commission: %.2f".format(commission),
        numericResult = commission,
        details = listOf(
            "Total sales: $sales",
            "Commission rate: $rate%",
            "Commission amount: $commission",
            "Net amount: $netAmount"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate commission", "$sales × ($rate ÷ 100)", "$sales × ${rate / 100.0}", "$commission")
        ),
        visualData = VisualData(
            percentage = rate,
            segments = listOf(
                PercentageSegment("Net Amount", netAmount, 100 - rate, NeonBlue),
                PercentageSegment("Commission", commission, rate, NeonGreen)
            )
        ),
        additionalInfo = mapOf(
            "Net Amount" to "%.2f".format(netAmount),
            "Commission Rate" to "$rate%"
        )
    )
}

// Placeholder functions for advanced calculations
private fun calculateCompoundPercentage(initialStr: String, rateStr: String, periodsStr: String): PercentageResult? {
    val initial = initialStr.toDoubleOrNull() ?: return null
    val rate = rateStr.toDoubleOrNull() ?: return null
    val periods = periodsStr.toDoubleOrNull() ?: return null

    val final = initial * (1 + rate / 100.0).pow(periods)
    val totalGrowth = final - initial
    val totalPercentage = (totalGrowth / initial) * 100.0

    return PercentageResult(
        mode = PercentageMode.COMPOUND_PERCENTAGE,
        mainResult = "%.2f".format(final),
        formattedResult = "Final Amount: %.2f".format(final),
        numericResult = final,
        details = listOf(
            "Initial amount: $initial",
            "Growth rate: $rate% per period",
            "Number of periods: $periods",
            "Final amount: $final",
            "Total growth: $totalGrowth ($totalPercentage%)"
        ),
        steps = listOf(
            CalculationStep(1, "Apply compound formula", "$initial × (1 + $rate/100)^$periods", "$initial × ${(1 + rate / 100.0).pow(periods)}", "$final")
        ),
        visualData = VisualData(
            percentage = totalPercentage,
            segments = listOf(
                PercentageSegment("Initial", initial, 100.0, NeonBlue),
                PercentageSegment("Growth", totalGrowth, totalPercentage, NeonGreen)
            )
        )
    )
}

private fun calculatePercentagePoints(oldPercentStr: String, newPercentStr: String): PercentageResult? {
    val oldPercent = oldPercentStr.toDoubleOrNull() ?: return null
    val newPercent = newPercentStr.toDoubleOrNull() ?: return null

    val points = newPercent - oldPercent
    val relativeChange = if (oldPercent != 0.0) (points / oldPercent) * 100.0 else 0.0

    return PercentageResult(
        mode = PercentageMode.PERCENTAGE_POINTS,
        mainResult = "%.2f points".format(points),
        formattedResult = "%.2f percentage points".format(points),
        numericResult = points,
        details = listOf(
            "Old percentage: $oldPercent%",
            "New percentage: $newPercent%",
            "Difference: $points percentage points",
            "Relative change: $relativeChange%"
        ),
        steps = listOf(
            CalculationStep(1, "Calculate difference", "$newPercent% - $oldPercent%", "$newPercent - $oldPercent", "${points} points")
        ),
        visualData = VisualData(
            percentage = abs(points),
            segments = listOf(
                PercentageSegment("Old", oldPercent, oldPercent, NeonBlue),
                PercentageSegment("Change", abs(points), abs(points), if (points >= 0) NeonGreen else NeonRed)
            )
        )
    )
}

private fun calculateWeightedAverage(valuesStr: String, weightsStr: String, unused: String): PercentageResult? {
    // Simplified implementation - in real app, would parse comma-separated values
    return null
}

// ================================================================================================
// UI COMPONENTS
// ================================================================================================

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PercentageModeSelector(
    selectedMode: PercentageMode,
    onModeSelected: (PercentageMode) -> Unit,
    accentColor: Color
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Calculation Type",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = accentColor
        )

        // Category tabs
        val categories = PercentageCategory.values()
        var selectedCategory by remember { mutableStateOf(selectedMode.category) }

        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp)
        ) {
            items(categories) { category ->
                FilterChip(
                    onClick = { selectedCategory = category },
                    label = { Text(category.displayName) },
                    selected = selectedCategory == category,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = category.color.copy(alpha = 0.2f),
                        selectedLabelColor = category.color,
                        labelColor = NeonText.copy(alpha = 0.7f)
                    ),
                    border = FilterChipDefaults.filterChipBorder(
                        selectedBorderColor = category.color,
                        borderColor = NeonText.copy(alpha = 0.3f)
                    )
                )
            }
        }

        // Mode selection for selected category
        val modesInCategory = PercentageMode.values().filter { it.category == selectedCategory }

        LazyColumn(
            modifier = Modifier.height(200.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(modesInCategory) { mode ->
                PercentageModeCard(
                    mode = mode,
                    isSelected = mode == selectedMode,
                    onClick = { onModeSelected(mode) }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PercentageModeCard(
    mode: PercentageMode,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) mode.category.color.copy(alpha = 0.1f) else NeonCard
        ),
        border = BorderStroke(
            width = if (isSelected) 2.dp else 1.dp,
            color = if (isSelected) mode.category.color else NeonText.copy(alpha = 0.2f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = mode.icon,
                contentDescription = null,
                tint = if (isSelected) mode.category.color else NeonText.copy(alpha = 0.7f),
                modifier = Modifier.size(24.dp)
            )

            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = mode.displayName,
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = if (isSelected) mode.category.color else NeonText
                )
                Text(
                    text = mode.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = NeonText.copy(alpha = 0.7f)
                )
                Text(
                    text = mode.formula,
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    ),
                    color = if (isSelected) mode.category.color.copy(alpha = 0.8f) else NeonText.copy(alpha = 0.5f)
                )
            }

            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Selected",
                    tint = mode.category.color,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
private fun PercentageInputSection(
    mode: PercentageMode,
    input1: String,
    input2: String,
    input3: String,
    onInput1Change: (String) -> Unit,
    onInput2Change: (String) -> Unit,
    onInput3Change: (String) -> Unit,
    accentColor: Color
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "Enter Values",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = accentColor
        )

        when (mode) {
            PercentageMode.BASIC_PERCENT -> {
                CalculatorInputField(
                    label = "Percentage (%)",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the percentage value") }
                )
                CalculatorInputField(
                    label = "Value",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the total value") }
                )
            }
            PercentageMode.PERCENT_OF_TOTAL -> {
                CalculatorInputField(
                    label = "Part",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the part value") }
                )
                CalculatorInputField(
                    label = "Total",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the total value") }
                )
            }
            PercentageMode.FIND_TOTAL -> {
                CalculatorInputField(
                    label = "Part",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the known part") }
                )
                CalculatorInputField(
                    label = "Percentage (%)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the percentage") }
                )
            }
            PercentageMode.PERCENT_INCREASE, PercentageMode.PERCENT_DECREASE -> {
                CalculatorInputField(
                    label = "Original Value",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the original value") }
                )
                CalculatorInputField(
                    label = "New Value",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the new value") }
                )
            }
            PercentageMode.PERCENT_DIFFERENCE -> {
                CalculatorInputField(
                    label = "Value 1",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the first value") }
                )
                CalculatorInputField(
                    label = "Value 2",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the second value") }
                )
            }
            PercentageMode.MARKUP -> {
                CalculatorInputField(
                    label = "Cost Price",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the cost price") }
                )
                CalculatorInputField(
                    label = "Markup (%)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the markup percentage") }
                )
            }
            PercentageMode.DISCOUNT -> {
                CalculatorInputField(
                    label = "Original Price",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the original price") }
                )
                CalculatorInputField(
                    label = "Discount (%)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the discount percentage") }
                )
            }
            PercentageMode.TAX_CALCULATION -> {
                CalculatorInputField(
                    label = "Amount",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the amount before tax") }
                )
                CalculatorInputField(
                    label = "Tax Rate (%)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the tax rate") }
                )
            }
            PercentageMode.COMMISSION -> {
                CalculatorInputField(
                    label = "Sales Amount",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the total sales") }
                )
                CalculatorInputField(
                    label = "Commission Rate (%)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the commission rate") }
                )
            }
            PercentageMode.COMPOUND_PERCENTAGE -> {
                CalculatorInputField(
                    label = "Initial Amount",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the initial amount") }
                )
                CalculatorInputField(
                    label = "Growth Rate (%)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the growth rate per period") }
                )
                CalculatorInputField(
                    label = "Number of Periods",
                    value = input3,
                    onValueChange = onInput3Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the number of periods") }
                )
            }
            PercentageMode.PERCENTAGE_POINTS -> {
                CalculatorInputField(
                    label = "Old Percentage (%)",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the old percentage") }
                )
                CalculatorInputField(
                    label = "New Percentage (%)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter the new percentage") }
                )
            }
            PercentageMode.WEIGHTED_AVERAGE -> {
                CalculatorInputField(
                    label = "Values (comma-separated)",
                    value = input1,
                    onValueChange = onInput1Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter values separated by commas") }
                )
                CalculatorInputField(
                    label = "Weights (comma-separated)",
                    value = input2,
                    onValueChange = onInput2Change,
                    accentColor = accentColor,
                    supportingText = { Text("Enter weights separated by commas") }
                )
            }
        }
    }
}

@Composable
private fun PercentageResultDisplay(
    result: PercentageResult,
    accentColor: Color,
    showSteps: Boolean
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Main Result Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = accentColor.copy(alpha = 0.1f)
            ),
            border = BorderStroke(2.dp, accentColor)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "Result",
                    style = MaterialTheme.typography.titleMedium,
                    color = accentColor
                )
                Text(
                    text = result.formattedResult,
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = accentColor,
                    textAlign = TextAlign.Center
                )
                Text(
                    text = result.mode.displayName,
                    style = MaterialTheme.typography.bodyMedium,
                    color = NeonText.copy(alpha = 0.7f)
                )
            }
        }

        // Details
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = NeonCard)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "Details",
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = accentColor
                )

                result.details.forEach { detail ->
                    Text(
                        text = "• $detail",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }

                // Additional info
                if (result.additionalInfo.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    result.additionalInfo.forEach { (key, value) ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = "$key:",
                                style = MaterialTheme.typography.bodyMedium.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = NeonText.copy(alpha = 0.8f)
                            )
                            Text(
                                text = value,
                                style = MaterialTheme.typography.bodyMedium,
                                color = accentColor
                            )
                        }
                    }
                }
            }
        }

        // Step-by-step calculation
        if (showSteps && result.steps.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = NeonCard)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        text = "Step-by-Step Calculation",
                        style = MaterialTheme.typography.titleSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = accentColor
                    )

                    result.steps.forEach { step ->
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = accentColor.copy(alpha = 0.05f)
                            )
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(12.dp),
                                verticalArrangement = Arrangement.spacedBy(4.dp)
                            ) {
                                Text(
                                    text = "Step ${step.stepNumber}: ${step.description}",
                                    style = MaterialTheme.typography.bodyMedium.copy(
                                        fontWeight = FontWeight.Medium
                                    ),
                                    color = accentColor
                                )
                                Text(
                                    text = step.formula,
                                    style = MaterialTheme.typography.bodySmall.copy(
                                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                                    ),
                                    color = NeonText.copy(alpha = 0.8f)
                                )
                                Text(
                                    text = "${step.calculation} = ${step.result}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonText
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PercentageVisualization(
    result: PercentageResult,
    accentColor: Color
) {
    result.visualData?.let { visualData ->
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = NeonCard)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = "Visual Representation",
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = accentColor
                )

                // Animated percentage bar
                PercentageBar(
                    segments = visualData.segments,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                )

                // Legend
                if (visualData.segments.isNotEmpty()) {
                    LazyRow(
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        contentPadding = PaddingValues(horizontal = 4.dp)
                    ) {
                        items(visualData.segments) { segment ->
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Box(
                                    modifier = Modifier
                                        .size(12.dp)
                                        .background(segment.color, CircleShape)
                                )
                                Text(
                                    text = "${segment.label}: %.1f%%".format(segment.percentage),
                                    style = MaterialTheme.typography.bodySmall,
                                    color = NeonText
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun PercentageBar(
    segments: List<PercentageSegment>,
    modifier: Modifier = Modifier
) {
    val animatedSegments = segments.map { segment ->
        val animatedPercentage by animateFloatAsState(
            targetValue = segment.percentage.toFloat(),
            animationSpec = tween(1000, easing = EaseOutCubic),
            label = "segment_${segment.label}"
        )
        segment.copy(percentage = animatedPercentage.toDouble())
    }

    Canvas(modifier = modifier) {
        val totalWidth = size.width
        val height = size.height
        var currentX = 0f

        animatedSegments.forEach { segment ->
            val segmentWidth = (segment.percentage / 100.0 * totalWidth).toFloat()

            drawRect(
                color = segment.color,
                topLeft = Offset(currentX, 0f),
                size = Size(segmentWidth, height)
            )

            currentX += segmentWidth
        }

        // Draw border
        drawRect(
            color = NeonText.copy(alpha = 0.3f),
            topLeft = Offset.Zero,
            size = Size(totalWidth, height),
            style = Stroke(width = 2.dp.toPx())
        )
    }
}

@Composable
private fun PercentageQuickActions(
    result: PercentageResult,
    onActionClick: (String) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        OutlinedButton(
            onClick = { onActionClick("copy") },
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = NeonCyan
            ),
            border = BorderStroke(1.dp, NeonCyan)
        ) {
            Icon(
                imageVector = Icons.Default.ContentCopy,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text("Copy")
        }

        OutlinedButton(
            onClick = { onActionClick("share") },
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = NeonGreen
            ),
            border = BorderStroke(1.dp, NeonGreen)
        ) {
            Icon(
                imageVector = Icons.Default.Share,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text("Share")
        }
    }
}

@Composable
private fun PercentageInfoDialog(
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Professional Percentage Calculator",
                style = MaterialTheme.typography.titleLarge,
                color = NeonCyan
            )
        },
        text = {
            LazyColumn(
                modifier = Modifier.height(400.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                item {
                    Text(
                        text = "This calculator provides comprehensive percentage calculations for various scenarios:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }

                items(PercentageCategory.values()) { category ->
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Text(
                            text = category.displayName,
                            style = MaterialTheme.typography.titleSmall.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = category.color
                        )

                        PercentageMode.values().filter { it.category == category }.forEach { mode ->
                            Text(
                                text = "• ${mode.displayName}: ${mode.description}",
                                style = MaterialTheme.typography.bodySmall,
                                color = NeonText.copy(alpha = 0.8f)
                            )
                        }
                    }
                }

                item {
                    Text(
                        text = "\nFeatures:",
                        style = MaterialTheme.typography.titleSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = NeonCyan
                    )
                    Text(
                        text = "• Real-time calculations\n• Step-by-step solutions\n• Visual representations\n• Professional formulas\n• Copy and share results",
                        style = MaterialTheme.typography.bodySmall,
                        color = NeonText.copy(alpha = 0.8f)
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = onDismiss,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = NeonCyan
                )
            ) {
                Text("Got it")
            }
        },
        containerColor = NeonCard,
        titleContentColor = NeonCyan,
        textContentColor = NeonText
    )
}
