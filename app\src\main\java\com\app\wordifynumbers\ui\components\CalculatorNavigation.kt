package com.app.wordifynumbers.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.*

import androidx.compose.ui.graphics.Color

data class CalculatorNavItem(
    val id: String,
    val title: String,
    val icon: ImageVector,
    val description: String,
    val category: Any? = null,  // Using Any to make it compatible with both String and enum
    val accentColor: Color = NeonGlow
)

@Composable
fun CalculatorNavigation(
    selectedCalculator: String?,
    onCalculatorSelected: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // Category Tabs
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 16.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(calculatorNavItems.groupBy { getCalculatorCategory(it.id) }.keys.toList()) { category ->
                CategoryChip(
                    category = category,
                    isSelected = selectedCalculator?.let {
                        getCalculatorCategory(it) == category
                    } ?: false,
                    onClick = {
                        // Select first calculator in category if none selected
                        calculatorNavItems.find { getCalculatorCategory(it.id) == category }?.let {
                            onCalculatorSelected(it.id)
                        }
                    }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Calculator Selection
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 16.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            val currentCategory = selectedCalculator?.let { getCalculatorCategory(it) }
            items(calculatorNavItems.filter {
                getCalculatorCategory(it.id) == currentCategory
            }) { calculator ->
                CalculatorChip(
                    calculator = calculator,
                    isSelected = calculator.id == selectedCalculator,
                    onClick = { onCalculatorSelected(calculator.id) }
                )
            }
        }
    }
}

@Composable
private fun CategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        onClick = onClick,
        color = if (isSelected) NeonCard else NeonBackground,
        border = BorderStroke(
            width = NeonBorder.Thin,
            color = if (isSelected) NeonGlow else NeonText.copy(alpha = 0.3f)
        ),
        shape = MaterialTheme.shapes.medium
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = getCategoryIcon(category),
                contentDescription = null,
                tint = if (isSelected) NeonGlow else NeonText.copy(alpha = 0.7f),
                modifier = Modifier.size(18.dp)
            )
            Text(
                text = category,
                style = MaterialTheme.typography.labelMedium,
                color = if (isSelected) NeonGlow else NeonText.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun CalculatorChip(
    calculator: CalculatorNavItem,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Surface(
        onClick = onClick,
        color = if (isSelected) NeonCard else NeonBackground,
        border = BorderStroke(
            width = NeonBorder.Thin,
            color = if (isSelected) NeonGlow else NeonText.copy(alpha = 0.3f)
        ),
        shape = MaterialTheme.shapes.medium
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = calculator.icon,
                contentDescription = null,
                tint = if (isSelected) NeonGlow else NeonText.copy(alpha = 0.7f),
                modifier = Modifier.size(24.dp)
            )
            Column {
                Text(
                    text = calculator.title,
                    style = MaterialTheme.typography.labelLarge,
                    color = if (isSelected) NeonGlow else NeonText
                )
                Text(
                    text = calculator.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = NeonText.copy(alpha = 0.7f)
                )
            }
        }
    }
}

private fun getCalculatorCategory(id: String): String = when (id) {
    "basic", "percentage", "complex", "areaVolume" -> "Math"
    "financial", "investment", "loan", "tax", "retirement", "tip", "basicLoan", "bmi" -> "Finance"
    "unitConverter", "currencyConverter", "dateCalculator", "romanNumeral" -> "Conversion"
    "statistics", "programmer", "age" -> "Special"
    else -> "Utility"
}

private fun getCategoryIcon(category: String): ImageVector = when (category) {
    "Math" -> Icons.Default.Calculate
    "Finance" -> Icons.Default.AccountBalance
    "Conversion" -> Icons.Default.Autorenew
    "Analysis" -> Icons.Default.Insights
    else -> Icons.Default.Build
}

val calculatorNavItems = listOf(
    CalculatorNavItem(
        id = "basic",
        title = "Basic",
        icon = Icons.Default.Calculate,
        description = "Standard arithmetic operations",
        category = "Math",
        accentColor = NeonBlue
    ),
    CalculatorNavItem(
        id = "percentage",
        title = "Percentage Calculator",
        icon = Icons.Default.Percent,
        description = "Calculate percentages, increases, and decreases",
        category = "Math",
        accentColor = NeonCyan
    ),
    CalculatorNavItem(
        id = "complex",
        title = "Complex Numbers",
        icon = Icons.Default.Architecture,
        description = "Complex number operations",
        category = "Math",
        accentColor = NeonPurple
    ),
    CalculatorNavItem(
        id = "areaVolume",
        title = "Area/Volume Calculator",
        icon = Icons.Default.Info,
        description = "Calculate area and volume of shapes",
        category = "Math",
        accentColor = NeonBlue
    ),
    CalculatorNavItem(
        id = "financial",
        title = "Financial Calculator",
        icon = Icons.Default.AttachMoney,
        description = "Compound interest, investments, and financial planning",
        category = "Finance",
        accentColor = NeonGreen
    ),
    CalculatorNavItem(
        id = "loan",
        title = "Loan Calculator",
        icon = Icons.Default.CreditCard,
        description = "EMI & loan calculations",
        category = "Finance",
        accentColor = NeonGreen
    ),
    CalculatorNavItem(
        id = "tax",
        title = "Tax Calculator",
        icon = Icons.Default.Calculate,
        description = "Income, sales, VAT & GST calculations",
        category = "Finance",
        accentColor = NeonPurple
    ),
    CalculatorNavItem(
        id = "retirement",
        title = "Retirement Planner",
        icon = Icons.Default.Elderly,
        description = "Plan your retirement and calculate required savings",
        category = "Finance",
        accentColor = NeonGreen
    ),
    CalculatorNavItem(
        id = "tip",
        title = "Tip Calculator",
        icon = Icons.Default.RestaurantMenu,
        description = "Calculate tip and split the bill",
        category = "Finance",
        accentColor = NeonGreen
    ),
    CalculatorNavItem(
        id = "basicLoan",
        title = "Basic Loan Calculator",
        icon = Icons.Default.CreditCard,
        description = "Basic loan calculations",
        category = "Finance",
        accentColor = NeonGreen
    ),
    CalculatorNavItem(
        id = "unitConverter",
        title = "Unit Converter",
        icon = Icons.Default.SwapHoriz,
        description = "Convert between different units",
        category = "Conversion",
        accentColor = NeonOrange
    ),
    CalculatorNavItem(
        id = "currencyConverter",
        title = "Currency Converter",
        icon = Icons.Default.AttachMoney,
        description = "Convert between different currencies",
        category = "Conversion",
        accentColor = NeonOrange
    ),
    CalculatorNavItem(
        id = "dateCalculator",
        title = "Date Calculator",
        icon = Icons.Default.CalendarToday,
        description = "Calculate the difference between two dates",
        category = "Conversion",
        accentColor = NeonOrange
    ),
    CalculatorNavItem(
        id = "romanNumeral",
        title = "Roman Numeral Converter",
        icon = Icons.Default.FormatListNumbered,
        description = "Convert between Roman and Arabic numerals",
        category = "Conversion",
        accentColor = NeonOrange
    ),
    CalculatorNavItem(
        id = "statistics",
        title = "Statistics",
        icon = Icons.Default.Timeline,
        description = "Statistical analysis with international standards",
        category = "Special",
        accentColor = NeonPink
    ),
    CalculatorNavItem(
        id = "bmi",
        title = "BMI Calculator",
        icon = Icons.Default.MonitorHeart,
        description = "Calculate Body Mass Index",
        category = "Special",
        accentColor = NeonPink
    ),
    CalculatorNavItem(
        id = "programmer",
        title = "Programmer",
        icon = Icons.Default.Code,
        description = "Binary, hex & octal conversions",
        category = "Special",
        accentColor = NeonPink
    ),
    CalculatorNavItem(
        id = "age",
        title = "Age Calculator",
        icon = Icons.Default.Info,
        description = "Calculate age from date of birth",
        category = "Special",
        accentColor = NeonPink
    ),
    CalculatorNavItem(
        id = "unitConverter",
        title = "Unit Converter",
        icon = Icons.Default.Settings,
        description = "Convert between different units",
        category = "Conversion",
        accentColor = NeonOrange
    ),
    CalculatorNavItem(
        id = "currencyConverter",
        title = "Currency Converter",
        icon = Icons.Default.AttachMoney,
        description = "Convert between different currencies",
        category = "Conversion",
        accentColor = NeonOrange
    ),
    CalculatorNavItem(
        id = "dateCalculator",
        title = "Date Calculator",
        icon = Icons.Default.Today,
        description = "Calculate the difference between two dates",
        category = "Conversion",
        accentColor = NeonOrange
    ),
    CalculatorNavItem(
        id = "bmi",
        title = "BMI Calculator",
        icon = Icons.Default.MonitorHeart,
        description = "Calculate Body Mass Index",
        category = "Special",
        accentColor = NeonPink
    ),
    CalculatorNavItem(
        id = "loan",
        title = "Loan Calculator",
        icon = Icons.Default.CreditCard,
        description = "Calculate loan payments",
        category = "Finance",
        accentColor = NeonGreen
    )
)
