@file:OptIn(kotlinx.serialization.InternalSerializationApi::class)

package com.app.wordifynumbers.ui.components

import kotlinx.serialization.Serializable
import com.app.wordifynumbers.data.CalculatorPreferences

/**
 * UI representation of calculator preferences
 * This is a wrapper around the data layer CalculatorPreferences
 * with additional UI-specific properties
 */
@Serializable
data class UICalculatorPreferences(
    val decimalPlaces: Int = 2,
    val useScientificNotation: <PERSON><PERSON><PERSON> = false,
    val useThousandsSeparator: Boolean = true,
    val angleUnit: AngleUnit = AngleUnit.DEGREES,
    val numberFormat: NumberFormat = NumberFormat.DECIMAL,
    val autoCalculate: Boolean = true,
    val historySize: Int = 10,
    val defaultMode: String = "STANDARD",
    val memoryValue: Double = 0.0
) {
    // Convert from data layer preferences
    // Added conversion methods to bridge between UI and data layer
    constructor(prefs: CalculatorPreferences) : this(
        decimalPlaces = prefs.decimalPlaces,
        useScientificNotation = prefs.useScientificNotation,
        useThousandsSeparator = prefs.useThousandsSeparator,
        historySize = prefs.historySize,
        defaultMode = prefs.defaultMode,
        memoryValue = prefs.memoryValue
    )

    fun toDataPreferences(): CalculatorPreferences = CalculatorPreferences(
        decimalPlaces = decimalPlaces,
        useScientificNotation = useScientificNotation,
        useThousandsSeparator = useThousandsSeparator,
        historySize = historySize,
        defaultMode = defaultMode,
        memoryValue = memoryValue
    )
}
