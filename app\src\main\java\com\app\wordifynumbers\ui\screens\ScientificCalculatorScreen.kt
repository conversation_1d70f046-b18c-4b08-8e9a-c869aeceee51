package com.app.wordifynumbers.ui.screens

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Functions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel
import com.app.wordifynumbers.util.FeedbackUtil

@Composable
fun ScientificCalculatorScreen(
    modifier: Modifier = Modifier,
    viewModel: CalculatorViewModel
) {
    var showScientificPad by remember { mutableStateOf(true) }
    val context = LocalContext.current

    // Information dialog
    var showInfoDialog by remember { mutableStateOf(false) }

    if (showInfoDialog) {
        AlertDialog(
            onDismissRequest = { showInfoDialog = false },
            title = { Text("Scientific Calculator") },
            text = {
                Column {
                    Text("This calculator supports advanced mathematical functions:")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("• Basic operations: +, -, ×, ÷")
                    Text("• Trigonometric functions: sin, cos, tan")
                    Text("• Logarithmic functions: log, ln")
                    Text("• Powers and roots: x², √x, xʸ")
                    Text("• Constants: π, e")
                    Text("• Memory functions: MC, MR, M+, M-, MS")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text("All calculations follow international mathematical standards.")
                }
            },
            confirmButton = {
                TextButton(onClick = { showInfoDialog = false }) {
                    Text("Got it")
                }
            },
            containerColor = NeonCard,
            titleContentColor = NeonCyan,
            textContentColor = NeonText
        )
    }

    StandardCalculatorLayout(
        title = "Scientific Calculator",
        icon = Icons.Default.Functions,
        accentColor = NeonCyan,
        showInfoButton = true,
        onInfoClick = {
            showInfoDialog = true
            FeedbackUtil.buttonPress(context)
        },

        // Input Section - Calculator Display
        inputSection = {
            CalculatorDisplay(
                expression = viewModel.state.collectAsState().value.display,
                result = viewModel.state.collectAsState().value.result,
                isError = viewModel.state.collectAsState().value.isError,
                onExpressionChange = viewModel::onInput,
                accentColor = NeonCyan
            )
        },

        // Action Buttons - Toggle Scientific Pad
        actionButtons = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.End
            ) {
                Surface(
                    onClick = { showScientificPad = !showScientificPad },
                    modifier = Modifier
                        .shadow(
                            elevation = 8.dp,
                            spotColor = NeonCyan.copy(alpha = 0.2f),
                            ambientColor = NeonCyan.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(20.dp)
                        ),
                    shape = RoundedCornerShape(20.dp),
                    color = if (showScientificPad) NeonCyan.copy(alpha = 0.2f) else NeonCard.copy(alpha = 0.7f),
                    border = BorderStroke(1.dp, SolidColor(NeonCyan.copy(alpha = 0.3f)))
                ) {
                    Text(
                        text = if (showScientificPad) "Hide Scientific" else "Show Scientific",
                        style = MaterialTheme.typography.labelLarge,
                        color = NeonCyan,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                    )
                }
            }
        },

        // Result Section - Calculator Pads
        resultSection = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Scientific Pad (conditionally shown)
                if (showScientificPad) {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = NeonCard.copy(alpha = 0.9f)
                        ),
                        border = BorderStroke(1.dp, SolidColor(NeonCyan.copy(alpha = 0.3f))),
                        shape = RoundedCornerShape(24.dp)
                    ) {
                        // New Professional Scientific Calculator Pad
                        ProfessionalScientificPad(
                            onFunctionClick = viewModel::onScientificFunction,
                            onNumberClick = viewModel::onNumber,
                            onOperatorClick = viewModel::onOperator,
                            onDeleteClick = viewModel::onDelete,
                            onClearClick = viewModel::onClear,
                            onEqualsClick = viewModel::onEquals,
                            accentColor = NeonCyan,
                            calculatorViewModel = viewModel,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(12.dp)
                        )
                    }
                } else {
                    // Basic Calculator Pad (only shown when scientific pad is hidden)
                    CalculatorPad(
                        onNumberClick = viewModel::onNumber,
                        onOperatorClick = viewModel::onOperator,
                        onDeleteClick = viewModel::onDelete,
                        onClearClick = viewModel::onClear,
                        onEqualsClick = viewModel::onEquals,
                        accentColor = NeonCyan,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        },

        // Additional Content - History
        additionalContent = {
            val history = viewModel.history.collectAsState().value

            if (history.isNotEmpty()) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    ),
                    border = BorderStroke(1.dp, SolidColor(NeonCyan.copy(alpha = 0.3f))),
                    shape = RoundedCornerShape(24.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Text(
                            text = "Recent Calculations",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonCyan
                        )

                        Divider(
                            color = NeonCyan.copy(alpha = 0.3f),
                            modifier = Modifier.padding(vertical = 4.dp)
                        )

                        history.take(5).forEach { item ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = item.input,
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonText.copy(alpha = 0.8f)
                                )

                                Text(
                                    text = "= ${item.result}",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = NeonCyan
                                )
                            }
                        }
                    }
                }
            }
        }
    )
}

/**
 * Professional Scientific Calculator Pad inspired by Casio calculators
 * Features a grid layout with properly aligned buttons
 */
@Composable
fun ProfessionalScientificPad(
    onFunctionClick: (String) -> Unit,
    onNumberClick: (String) -> Unit,
    onOperatorClick: (String) -> Unit,
    onDeleteClick: () -> Unit,
    onClearClick: () -> Unit,
    onEqualsClick: () -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonCyan,
    calculatorViewModel: CalculatorViewModel? = null
) {
    val context = LocalContext.current

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // Memory and special function row with memory indicator
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Memory indicator
            val memoryValue = calculatorViewModel?.preferences?.collectAsState()?.value?.memoryValue ?: 0.0
            val hasMemory = memoryValue != 0.0

            Box(
                modifier = Modifier
                    .size(24.dp)
                    .background(
                        color = if (hasMemory) accentColor.copy(alpha = 0.2f) else Color.Transparent,
                        shape = CircleShape
                    )
                    .border(
                        width = 1.dp,
                        color = if (hasMemory) accentColor else accentColor.copy(alpha = 0.3f),
                        shape = CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "M",
                    style = MaterialTheme.typography.labelSmall,
                    color = if (hasMemory) accentColor else accentColor.copy(alpha = 0.5f)
                )
            }

            Spacer(modifier = Modifier.width(4.dp))

            // Memory function buttons
            listOf("MC", "MR", "M+", "M-", "MS").forEach { symbol ->
                ScientificButton(
                    text = symbol,
                    type = ButtonType.FUNCTION,
                    accentColor = accentColor,
                    modifier = Modifier
                        .weight(1f)
                        .height(48.dp),
                    onClick = {
                        onFunctionClick(symbol)
                        FeedbackUtil.buttonPress(context)
                    }
                )
            }
        }

        // Main calculator grid
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Left side: Scientific functions in a grid
            Column(
                modifier = Modifier.weight(1.2f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Row 1: Trigonometric functions
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("sin", "cos", "tan").forEach { symbol ->
                        ScientificButton(
                            text = symbol,
                            type = ButtonType.SCIENTIFIC,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f),
                            onClick = {
                                onFunctionClick("$symbol(")
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }
                }

                // Row 2: Inverse trigonometric functions
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("asin", "acos", "atan").forEach { symbol ->
                        ScientificButton(
                            text = symbol,
                            type = ButtonType.SCIENTIFIC,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f),
                            onClick = {
                                onFunctionClick("$symbol(")
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }
                }

                // Row 3: Logarithmic functions
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("log", "ln", "10^x").forEach { symbol ->
                        ScientificButton(
                            text = symbol,
                            type = ButtonType.SCIENTIFIC,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f),
                            onClick = {
                                val func = when(symbol) {
                                    "10^x" -> "10^"
                                    else -> "$symbol("
                                }
                                onFunctionClick(func)
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }
                }

                // Row 4: Power functions
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("x²", "x^y", "√").forEach { symbol ->
                        ScientificButton(
                            text = symbol,
                            type = ButtonType.SCIENTIFIC,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f),
                            onClick = {
                                val func = when(symbol) {
                                    "x²" -> "^2"
                                    "x^y" -> "^"
                                    "√" -> "sqrt("
                                    else -> symbol
                                }
                                onFunctionClick(func)
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }
                }

                // Row 5: Constants and misc
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("π", "e", "abs").forEach { symbol ->
                        ScientificButton(
                            text = symbol,
                            type = ButtonType.SCIENTIFIC,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1.5f),
                            onClick = {
                                val func = when(symbol) {
                                    "abs" -> "abs("
                                    else -> symbol
                                }
                                onFunctionClick(func)
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }
                }
            }

            // Right side: Basic calculator pad
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Row 1: Clear and parentheses
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    ScientificButton(
                        text = "C",
                        type = ButtonType.FUNCTION,
                        accentColor = NeonRed,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onClearClick()
                            FeedbackUtil.buttonPress(context)
                        }
                    )

                    ScientificButton(
                        text = "⌫",
                        type = ButtonType.FUNCTION,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onDeleteClick()
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }

                // Row 2: Parentheses and division
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    ScientificButton(
                        text = "(",
                        type = ButtonType.FUNCTION,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onOperatorClick("(")
                            FeedbackUtil.buttonPress(context)
                        }
                    )

                    ScientificButton(
                        text = ")",
                        type = ButtonType.FUNCTION,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onOperatorClick(")")
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }

                // Row 3: Numbers 7-9 and multiplication
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("7", "8", "9").forEach { number ->
                        ScientificButton(
                            text = number,
                            type = ButtonType.NUMBER,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1f),
                            onClick = {
                                onNumberClick(number)
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }

                    ScientificButton(
                        text = "×",
                        type = ButtonType.OPERATOR,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onOperatorClick("*")
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }

                // Row 4: Numbers 4-6 and subtraction
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("4", "5", "6").forEach { number ->
                        ScientificButton(
                            text = number,
                            type = ButtonType.NUMBER,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1f),
                            onClick = {
                                onNumberClick(number)
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }

                    ScientificButton(
                        text = "-",
                        type = ButtonType.OPERATOR,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onOperatorClick("-")
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }

                // Row 5: Numbers 1-3 and addition
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    listOf("1", "2", "3").forEach { number ->
                        ScientificButton(
                            text = number,
                            type = ButtonType.NUMBER,
                            accentColor = accentColor,
                            modifier = Modifier
                                .weight(1f)
                                .aspectRatio(1f),
                            onClick = {
                                onNumberClick(number)
                                FeedbackUtil.buttonPress(context)
                            }
                        )
                    }

                    ScientificButton(
                        text = "+",
                        type = ButtonType.OPERATOR,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onOperatorClick("+")
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }

                // Row 6: Zero, decimal, and equals
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    ScientificButton(
                        text = "0",
                        type = ButtonType.NUMBER,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onNumberClick("0")
                            FeedbackUtil.buttonPress(context)
                        }
                    )

                    ScientificButton(
                        text = ".",
                        type = ButtonType.NUMBER,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f),
                        onClick = {
                            onNumberClick(".")
                            FeedbackUtil.buttonPress(context)
                        }
                    )

                    ScientificButton(
                        text = "=",
                        type = ButtonType.EQUALS,
                        accentColor = accentColor,
                        modifier = Modifier
                            .weight(2f)
                            .aspectRatio(2f),
                        onClick = {
                            onEqualsClick()
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun ScientificButton(
    text: String,
    type: ButtonType,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonCyan,
    icon: ImageVector? = null
) {
    var isPressed by remember { mutableStateOf(false) }

    // Button colors based on type - inspired by professional calculators
    val buttonColor = when (type) {
        ButtonType.NUMBER -> Color(0xFF2A2D3E) // Darker background for numbers
        ButtonType.OPERATOR -> Color(0xFF1E3A5F) // Blue-tinted background for operators
        ButtonType.FUNCTION -> Color(0xFF3D2E4F) // Purple-tinted background for functions
        ButtonType.EQUALS -> Color(0xFF1F4F3D) // Green-tinted background for equals
        ButtonType.SCIENTIFIC -> Color(0xFF3D3A2E) // Gold-tinted background for scientific
    }

    val contentColor = when (type) {
        ButtonType.NUMBER -> Color.White
        ButtonType.OPERATOR -> Color(0xFF64B5F6) // Light blue for operators
        ButtonType.FUNCTION -> Color(0xFFCE93D8) // Light purple for functions
        ButtonType.EQUALS -> Color(0xFF81C784) // Light green for equals
        ButtonType.SCIENTIFIC -> Color(0xFFFFD54F) // Gold for scientific
    }

    // Border color based on type
    val borderColor = when (type) {
        ButtonType.NUMBER -> Color(0xFF3F4259).copy(alpha = 0.5f)
        ButtonType.OPERATOR -> Color(0xFF2E5C8F).copy(alpha = 0.5f)
        ButtonType.FUNCTION -> Color(0xFF5D4E6F).copy(alpha = 0.5f)
        ButtonType.EQUALS -> Color(0xFF2F7F5D).copy(alpha = 0.5f)
        ButtonType.SCIENTIFIC -> Color(0xFF5D5A4E).copy(alpha = 0.5f)
    }

    // Button surface
    Surface(
        onClick = {
            isPressed = true
            onClick()
        },
        shape = RoundedCornerShape(8.dp), // Less rounded for professional look
        color = buttonColor,
        border = BorderStroke(1.dp, SolidColor(borderColor)), // Always show border
        tonalElevation = 2.dp, // Subtle elevation
        shadowElevation = 2.dp, // Subtle shadow
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    if (isPressed) contentColor.copy(alpha = 0.1f) else Color.Transparent
                ),
            contentAlignment = Alignment.Center
        ) {
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = text,
                    tint = contentColor,
                    modifier = Modifier.size(24.dp) // Consistent icon size
                )
            } else {
                Text(
                    text = text,
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = if (type == ButtonType.EQUALS) FontWeight.Bold else FontWeight.Medium,
                        fontSize = when (type) {
                            ButtonType.EQUALS -> 22.sp
                            ButtonType.OPERATOR -> 20.sp
                            ButtonType.SCIENTIFIC -> 16.sp
                            else -> 18.sp
                        }
                    ),
                    color = contentColor
                )
            }
        }
    }

    // Reset pressed state after animation
    if (isPressed) {
        LaunchedEffect(isPressed) {
            kotlinx.coroutines.delay(100)
            isPressed = false
        }
    }
}
