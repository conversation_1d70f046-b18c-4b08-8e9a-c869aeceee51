package com.app.wordifynumbers.model

import android.content.SharedPreferences
import android.os.Build
import androidx.annotation.RequiresApi

data class ConversionProfile(
    val name: String,
    
    val language: String = "English",
    val lastInput: String = ""
) {
    companion object {
        fun loadProfiles(prefs: SharedPreferences): List<ConversionProfile> {
            val set = prefs.getStringSet("profiles", emptySet()) ?: emptySet()
            return set.mapNotNull {
                val parts = it.split("|")
                if (parts.size >= 3) ConversionProfile(parts[0], parts[1], parts[2]) else null
            }
        }
        @RequiresApi(Build.VERSION_CODES.N)
        fun saveProfile(prefs: SharedPreferences, profile: ConversionProfile) {
            val set = prefs.getStringSet("profiles", emptySet())?.toMutableSet() ?: mutableSetOf()
            set.removeIf { it.startsWith(profile.name + "|") }
            set.add("${profile.name}|${profile.language}|${profile.lastInput}")
            prefs.edit().putStringSet("profiles", set).apply()
        }
    }
}
