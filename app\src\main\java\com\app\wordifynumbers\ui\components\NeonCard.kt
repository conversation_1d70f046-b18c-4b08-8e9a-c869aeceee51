package com.app.wordifynumbers.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.NeonBorderConstants
import com.app.wordifynumbers.ui.theme.NeonCard
import com.app.wordifynumbers.ui.theme.NeonCyan

/**
 * A card with neon styling
 */
@Composable
fun NeonCardWithBorder(
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(16.dp),
    accentColor: Color = NeonCyan,
    backgroundColor: Color = NeonCard,
    borderWidth: Dp = NeonBorderConstants.Medium,
    elevation: Dp = 8.dp,
    contentPadding: Dp = 16.dp,
    content: @Composable BoxScope.() -> Unit
) {
    Box(
        modifier = modifier
            .shadow(
                elevation = elevation,
                spotColor = accentColor.copy(alpha = 0.5f),
                ambientColor = accentColor.copy(alpha = 0.25f),
                shape = shape
            )
            .clip(shape)
            .background(backgroundColor)
            .padding(2.dp) // Space for the glow effect
    ) {
        Card(
            shape = shape,
            colors = CardDefaults.cardColors(
                containerColor = backgroundColor,
                contentColor = Color.White
            ),
            border = BorderStroke(
                width = borderWidth,
                brush = SolidColor(accentColor.copy(alpha = 0.7f))
            ),
            modifier = Modifier.matchParentSize()
        ) {
            Box(
                modifier = Modifier.padding(contentPadding),
                content = content
            )
        }
    }
}
