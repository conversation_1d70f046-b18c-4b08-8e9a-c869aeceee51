package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.NumberConverterViewModel
import com.app.wordifynumbers.NumberConverterViewModelFactory
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.FeedbackUtil

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DigitTranslatorScreen(
    onNavigateToDetails: (String) -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val viewModel: NumberConverterViewModel = viewModel(factory = NumberConverterViewModelFactory(context))
    val selectedLanguage by viewModel.selectedLanguage.collectAsState()
    val digitTranslations by viewModel.digitTranslations.collectAsState()
    val languages by viewModel.languages.collectAsState()
    val currentFact by viewModel.currentFact.collectAsState()
    val numberInput by viewModel.numberInput.collectAsState()
    var expanded by remember { mutableStateOf(false) }

    // Search state for language filter
    var searchQuery by remember { mutableStateOf("") }
    val filteredLanguages = remember(searchQuery, languages) {
        if (searchQuery.isBlank()) {
            languages
        } else {
            languages.filter { it.contains(searchQuery, ignoreCase = true) }
        }
    }

    LaunchedEffect(Unit) {
        if (currentFact.isBlank()) viewModel.nextFact()
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        NeonBackground,
                        NeonBackground.copy(alpha = 0.9f)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            // Custom header with app name on top (matching Finance Notepad)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp, vertical = 6.dp)
                    .shadow(
                        elevation = 10.dp,
                        spotColor = NeonGold.copy(alpha = 0.3f),
                        ambientColor = NeonGold.copy(alpha = 0.2f),
                        shape = RoundedCornerShape(16.dp)
                    ),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.95f)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp, horizontal = 12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // App name at the top
                    Text(
                        text = "Wordify Numbers",
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 0.5.sp
                        ),
                        color = NeonGold,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Divider with glow effect
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(0.95f)
                            .height(1.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        NeonGold.copy(alpha = 0.3f),
                                        NeonGold.copy(alpha = 0.5f),
                                        NeonGold.copy(alpha = 0.3f),
                                        Color.Transparent
                                    )
                                )
                            )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Screen title and actions in a row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Screen title with icon
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Translate,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(24.dp)
                            )

                            Text(
                                text = "Digit Translator",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold,
                                    fontSize = 18.sp
                                ),
                                color = NeonGold
                            )
                        }

                        // Action buttons
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            WordifyHeaderAction(
                                icon = Icons.Default.Refresh,
                                contentDescription = "New Fact",
                                accentColor = NeonGold,
                                onClick = {
                                    viewModel.nextFact()
                                    FeedbackUtil.buttonPress(context)
                                }
                            )

                            WordifyHeaderAction(
                                icon = Icons.Default.Info,
                                contentDescription = "Detailed View",
                                accentColor = NeonGold,
                                onClick = {
                                    onNavigateToDetails(selectedLanguage)
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Main content column
            Column(
                modifier = modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // SECTION 1: LANGUAGE SELECTION
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = NeonGold.copy(alpha = 0.2f),
                            ambientColor = NeonGold.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Language,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(24.dp)
                            )

                            Text(
                                text = "Select Your Language",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = NeonGold
                            )
                        }

                        Divider(
                            color = NeonGold.copy(alpha = 0.3f),
                            thickness = 1.dp,
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Language dropdown with search
                        ExposedDropdownMenuBox(
                            expanded = expanded,
                            onExpandedChange = {
                                expanded = !expanded
                                if (!expanded) {
                                    // Reset search when closing
                                    searchQuery = ""
                                }
                                FeedbackUtil.buttonPress(context)
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                        ) {
                    OutlinedTextField(
                        value = selectedLanguage,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Choose from 20 languages") },
                        trailingIcon = {
                            Icon(
                                Icons.Default.ArrowDropDown,
                                contentDescription = "Select language",
                                tint = NeonGold
                            )
                        },
                        modifier = Modifier
                            .menuAnchor()
                            .fillMaxWidth()
                            .height(80.dp)
                            .shadow(
                                elevation = 12.dp,
                                spotColor = NeonGold.copy(alpha = 0.3f),
                                ambientColor = NeonGold.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(16.dp)
                            ),
                        textStyle = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = NeonGold,
                            unfocusedBorderColor = NeonGold.copy(alpha = 0.5f),
                            focusedLabelColor = NeonGold,
                            unfocusedLabelColor = NeonGold.copy(alpha = 0.7f),
                            cursorColor = NeonGold,
                            focusedTextColor = NeonText,
                            unfocusedTextColor = NeonText,
                            // Enhanced container colors
                            focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                            unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                        ),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Language,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(32.dp)
                            )
                        }
                    )

                    ExposedDropdownMenu(
                        expanded = expanded,
                        onDismissRequest = {
                            expanded = false
                            searchQuery = ""
                        },
                        modifier = Modifier
                            .background(NeonCard)
                            .heightIn(max = 400.dp) // Limit height for scrolling
                    ) {
                        // Search field
                        OutlinedTextField(
                            value = searchQuery,
                            onValueChange = { searchQuery = it },
                            placeholder = { Text("Search languages", color = NeonText.copy(alpha = 0.7f)) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp, vertical = 8.dp),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = NeonGold,
                                unfocusedBorderColor = NeonGold.copy(alpha = 0.5f),
                                focusedLabelColor = NeonGold,
                                unfocusedLabelColor = NeonGold.copy(alpha = 0.7f),
                                cursorColor = NeonGold,
                                focusedTextColor = NeonText,
                                unfocusedTextColor = NeonText,
                                focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                                unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                            ),
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Search,
                                    contentDescription = "Search",
                                    tint = NeonGold
                                )
                            },
                            singleLine = true
                        )

                        // Language list
                        filteredLanguages.forEach { language ->
                            val isSelected = language == selectedLanguage
                            val isUrdu = language == "Urdu" // Highlight Urdu

                            DropdownMenuItem(
                                text = {
                                    Text(
                                        text = language,
                                        color = when {
                                            isUrdu -> NeonGlow // Highlight Urdu with special color
                                            isSelected -> NeonGold
                                            else -> NeonText
                                        },
                                        fontWeight = if (isSelected || isUrdu) FontWeight.Bold else FontWeight.Normal
                                    )
                                },
                                onClick = {
                                    viewModel.onLanguageSelected(language)
                                    expanded = false
                                    searchQuery = ""
                                    FeedbackUtil.buttonPress(context)
                                },
                                colors = MenuDefaults.itemColors(
                                    textColor = NeonText,
                                ),
                                leadingIcon = {
                                    Icon(
                                        imageVector = Icons.Default.Check,
                                        contentDescription = null,
                                        tint = if (isSelected) NeonGold else Color.Transparent
                                    )
                                },
                                trailingIcon = {
                                    if (isUrdu) {
                                        Icon(
                                            imageVector = Icons.Default.Star,
                                            contentDescription = "Featured language",
                                            tint = NeonGlow
                                        )
                                    }
                                }
                            )
                        }

                        // Show message if no languages match search
                        if (filteredLanguages.isEmpty()) {
                            Text(
                                text = "No languages match your search",
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                textAlign = TextAlign.Center,
                                color = NeonText.copy(alpha = 0.7f)
                            )
                                }
                            }
                        }
                    }
                }

                // SECTION 2: DIGIT INPUT
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 8.dp,
                            spotColor = NeonGold.copy(alpha = 0.2f),
                            ambientColor = NeonGold.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Numbers,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(24.dp)
                            )

                            Text(
                                text = "Enter Your Digit",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = NeonGold
                            )
                        }

                        Divider(
                            color = NeonGold.copy(alpha = 0.3f),
                            thickness = 1.dp,
                            modifier = Modifier.fillMaxWidth()
                        )

                        // Large digit input field
                        OutlinedTextField(
                            value = numberInput,
                            onValueChange = {
                                // Only allow single digit input (0-9)
                                if (it.isEmpty() || (it.length == 1 && it[0].isDigit())) {
                                    viewModel.onNumberInputChange(it)
                                    FeedbackUtil.buttonPress(context)
                                }
                            },
                            label = { Text("Enter a digit (0-9)") },
                            placeholder = { Text("Example: 7") },
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(100.dp), // Extra large height for prominence
                            singleLine = true,
                            maxLines = 1,
                            textStyle = MaterialTheme.typography.displayMedium.copy(
                                fontWeight = FontWeight.Bold,
                                textAlign = TextAlign.Center
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = NeonGold,
                                unfocusedBorderColor = NeonGold.copy(alpha = 0.7f), // More visible border
                                focusedLabelColor = NeonGold,
                                unfocusedLabelColor = NeonGold.copy(alpha = 0.8f),
                                cursorColor = NeonGold,
                                focusedTextColor = NeonGold, // Highlighted text color
                                unfocusedTextColor = NeonGold.copy(alpha = 0.9f),
                                focusedPlaceholderColor = NeonText.copy(alpha = 0.7f),
                                unfocusedPlaceholderColor = NeonText.copy(alpha = 0.5f),
                                // Enhanced container colors
                                focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                                unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                            ),
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Numbers,
                                    contentDescription = null,
                                    tint = NeonGold,
                                    modifier = Modifier.size(40.dp) // Extra large icon
                                )
                            }
                        )
                    }
                }

                // SECTION 3: NUMBER FACTS CARD
                AnimatedVisibility(
                    visible = currentFact.isNotBlank(),
                    enter = fadeIn() + expandVertically(),
                    exit = fadeOut() + shrinkVertically()
                ) {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .shadow(
                                elevation = 8.dp,
                                spotColor = NeonGold.copy(alpha = 0.2f),
                                ambientColor = NeonGold.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(16.dp)
                            ),
                        shape = RoundedCornerShape(16.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = NeonCard.copy(alpha = 0.9f)
                        ),
                        border = BorderStroke(1.dp, SolidColor(NeonGold.copy(alpha = 0.3f)))
                    ) {
                        Row(
                            modifier = Modifier
                                .padding(16.dp)
                                .fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.Lightbulb,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(24.dp)
                            )

                            Text(
                                text = currentFact,
                                style = MaterialTheme.typography.bodyLarge.copy(
                                    fontWeight = FontWeight.Medium
                                ),
                                color = NeonText,
                                modifier = Modifier.weight(1f)
                            )

                            IconButton(
                                onClick = {
                                    viewModel.nextFact()
                                    FeedbackUtil.buttonPress(context)
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Refresh,
                                    contentDescription = "Next fact",
                                    tint = NeonGold
                                )
                            }
                        }
                    }
                }

                // SECTION 4: DIGIT TRANSLATIONS GRID
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .shadow(
                            elevation = 8.dp,
                            spotColor = NeonGold.copy(alpha = 0.2f),
                            ambientColor = NeonGold.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(16.dp)
                        ),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.9f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Translate,
                                contentDescription = null,
                                tint = NeonGold,
                                modifier = Modifier.size(24.dp)
                            )

                            Text(
                                text = "Digit Translations",
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = FontWeight.Bold
                                ),
                                color = NeonGold
                            )

                            // Show language badge for Urdu
                            if (selectedLanguage == "Urdu") {
                                Spacer(modifier = Modifier.weight(1f))
                                Card(
                                    shape = RoundedCornerShape(16.dp),
                                    colors = CardDefaults.cardColors(
                                        containerColor = NeonGold.copy(alpha = 0.2f)
                                    ),
                                    border = BorderStroke(1.dp, SolidColor(NeonGold.copy(alpha = 0.5f))),
                                    modifier = Modifier.padding(start = 8.dp)
                                ) {
                                    Text(
                                        text = "Urdu",
                                        style = MaterialTheme.typography.labelMedium.copy(
                                            fontWeight = FontWeight.Bold
                                        ),
                                        color = NeonGold,
                                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                                    )
                                }
                            }
                        }

                        Divider(
                            color = NeonGold.copy(alpha = 0.3f),
                            thickness = 1.dp,
                            modifier = Modifier.fillMaxWidth()
                        )

                        LazyVerticalGrid(
                            columns = GridCells.Fixed(2),
                            contentPadding = PaddingValues(8.dp),
                            horizontalArrangement = Arrangement.spacedBy(12.dp),
                            verticalArrangement = Arrangement.spacedBy(12.dp),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            items(digitTranslations) { (digit, translation) ->
                                DigitCard(
                                    digit = digit,
                                    translation = translation,
                                    accentColor = NeonGold
                                )
                            }
                        }
                    }
                }

                // Language count indicator
                Text(
                    text = "20 languages available",
                    style = MaterialTheme.typography.labelMedium,
                    color = NeonText.copy(alpha = 0.7f),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 16.dp),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun DigitCard(
    digit: String,
    translation: String,
    accentColor: Color = NeonGold,
    modifier: Modifier = Modifier
) {
    // Animated glow effect
    val infiniteTransition = rememberInfiniteTransition(label = "digitGlow")
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.7f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .shadow(
                elevation = 6.dp,
                spotColor = accentColor.copy(alpha = 0.2f),
                ambientColor = accentColor.copy(alpha = 0.1f),
                shape = RoundedCornerShape(12.dp)
            ),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = NeonCard.copy(alpha = 0.8f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp, horizontal = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Digit with background
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .background(
                        color = accentColor.copy(alpha = 0.2f),
                        shape = RoundedCornerShape(8.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = digit,
                    style = MaterialTheme.typography.headlineLarge.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = accentColor.copy(alpha = glowAlpha),
                    textAlign = TextAlign.Center
                )
            }

            // Translation
            Text(
                text = translation,
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = NeonText,
                textAlign = TextAlign.Center
            )
        }
    }
}
