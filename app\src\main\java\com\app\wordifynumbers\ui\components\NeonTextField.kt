package com.app.wordifynumbers.ui.components

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.NeonBorderConstants
import com.app.wordifynumbers.ui.theme.NeonCard
import com.app.wordifynumbers.ui.theme.NeonCyan
import com.app.wordifynumbers.ui.theme.NeonHint
import com.app.wordifynumbers.ui.theme.NeonText

/**
 * Custom TextField with neon styling
 */
@Composable
fun NeonTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    label: @Composable (() -> Unit)? = null,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    supportingText: @Composable (() -> Unit)? = null,
    isError: Boolean = false,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    singleLine: Boolean = false,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    textStyle: TextStyle = LocalTextStyle.current,
    accentColor: Color = NeonCyan
) {
    val borderColor = when {
        isError -> MaterialTheme.colorScheme.error
        else -> accentColor
    }

    Column(modifier = modifier) {
        // Optional label
        if (label != null) {
            Box(
                modifier = Modifier.padding(bottom = 8.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                label()
            }
        }

        // Text field with border and glow effect
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(NeonCard)
                .border(
                    BorderStroke(NeonBorderConstants.Medium, SolidColor(borderColor.copy(alpha = 0.7f))),
                    RoundedCornerShape(12.dp)
                )
                .padding(2.dp) // Add padding inside the border for the glow effect
        ) {
            BasicTextField(
                value = value,
                onValueChange = onValueChange,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(
                        start = if (leadingIcon != null) 48.dp else 16.dp,
                        end = if (trailingIcon != null) 48.dp else 16.dp,
                        top = 16.dp,
                        bottom = 16.dp
                    ),
                textStyle = textStyle.copy(
                    color = Color.White, // Brighter text for better visibility
                    fontSize = MaterialTheme.typography.bodyLarge.fontSize
                ),
                cursorBrush = SolidColor(accentColor),
                visualTransformation = visualTransformation,
                keyboardOptions = keyboardOptions,
                keyboardActions = keyboardActions,
                singleLine = singleLine,
                maxLines = maxLines,
                minLines = minLines,
                decorationBox = { innerTextField ->
                    Box(
                        contentAlignment = Alignment.CenterStart,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        // Placeholder
                        if (value.isEmpty() && placeholder != null) {
                            Box(
                                modifier = Modifier.fillMaxWidth(),
                                contentAlignment = Alignment.CenterStart
                            ) {
                                placeholder()
                            }
                        }

                        // Actual text field
                        innerTextField()
                    }
                }
            )

            // Leading icon with better positioning
            if (leadingIcon != null) {
                Box(
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .padding(start = 16.dp)
                ) {
                    leadingIcon()
                }
            }

            // Trailing icon with better positioning
            if (trailingIcon != null) {
                Box(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = 16.dp)
                ) {
                    trailingIcon()
                }
            }
        }

        // Error message or supporting text
        if (isError) {
            Text(
                text = "Invalid input",
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            )
        } else if (supportingText != null) {
            Box(
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            ) {
                supportingText()
            }
        }
    }
}
