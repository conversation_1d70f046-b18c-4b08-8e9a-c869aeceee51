package com.app.wordifynumbers.ui.components

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.NeonCard

/**
 * An enhanced card component with neon glow effects
 *
 * @param modifier Modifier for the card
 * @param accentColor The accent color for the card's glow effects
 * @param elevation The elevation of the card
 * @param cornerRadius The corner radius of the card
 * @param content The content of the card
 */
@Composable
fun EnhancedNeonCard(
    modifier: Modifier = Modifier,
    accentColor: Color,
    elevation: Dp = 8.dp,
    cornerRadius: Dp = 16.dp,
    content: @Composable BoxScope.() -> Unit
) {
    // Animated effects
    val infiniteTransition = rememberInfiniteTransition(label = "cardAnimation")
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.5f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Card(
        modifier = modifier
            .padding(4.dp)
            .shadow(
                elevation = elevation,
                spotColor = accentColor.copy(alpha = 0.3f),
                ambientColor = accentColor.copy(alpha = 0.1f),
                shape = RoundedCornerShape(cornerRadius)
            ),
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = NeonCard.copy(alpha = 0.9f)
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(cornerRadius))
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            NeonCard.copy(alpha = 0.95f),
                            NeonCard.copy(alpha = 0.85f)
                        )
                    )
                )
                .drawBehind {
                    // Top glow line
                    drawLine(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color.Transparent,
                                accentColor.copy(alpha = glowAlpha * 0.7f),
                                Color.Transparent
                            )
                        ),
                        start = Offset(size.width * 0.1f, 0f),
                        end = Offset(size.width * 0.9f, 0f),
                        strokeWidth = 2.dp.toPx()
                    )

                    // Bottom glow line
                    drawLine(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color.Transparent,
                                accentColor.copy(alpha = glowAlpha * 0.3f),
                                Color.Transparent
                            )
                        ),
                        start = Offset(size.width * 0.2f, size.height),
                        end = Offset(size.width * 0.8f, size.height),
                        strokeWidth = 1.dp.toPx()
                    )
                },
            content = content
        )
    }
}

/**
 * An alternative enhanced card component with neon glow effects and BoxScope content
 *
 * @param modifier Modifier for the card
 * @param shape The shape of the card
 * @param accentColor The accent color for the card's glow effects
 * @param backgroundColor The background color of the card
 * @param borderWidth The width of the border
 * @param elevation The elevation of the card
 * @param contentPadding The padding for the content
 * @param content The content of the card with BoxScope
 */
@Composable
fun EnhancedNeonCardWithScope(
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(16.dp),
    accentColor: Color = Color.Cyan,
    backgroundColor: Color = NeonCard.copy(alpha = 0.9f),
    borderWidth: Dp = 1.dp,
    elevation: Dp = 8.dp,
    contentPadding: Dp = 16.dp,
    content: @Composable BoxScope.() -> Unit
) {
    Card(
        modifier = modifier
            .padding(4.dp)
            .shadow(
                elevation = elevation,
                spotColor = accentColor.copy(alpha = 0.3f),
                ambientColor = accentColor.copy(alpha = 0.1f),
                shape = shape
            ),
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(contentPadding),
            content = content
        )
    }
}
