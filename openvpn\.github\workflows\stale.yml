name: 'Stale issues and PR'
on:
  schedule:
    - cron: '30 1 * * *'

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v4
        with:
          stale-issue-label: stale
          stale-pr-label: stale
          exempt-issue-labels: 'bug,enhancement,stale-exempt'
          stale-issue-message: 'This issue is stale because it has been open 30 days with no activity. Remove stale label or comment or this will be closed in 14 days.'
          stale-pr-message: 'This PR is stale because it has been open 30 days with no activity. Remove stale label or comment or this will be closed in 14 days.'
          close-issue-message: 'This issue was closed because it has been stalled for 14 days with no activity.'
          close-pr-message: 'This PR was closed because it has been stalled for 14 days with no activity.'
          days-before-issue-stale: 90
          days-before-pr-stale: 90
          days-before-issue-close: 30
          days-before-pr-close: 30
