package com.app.wordifynumbers.ui.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import com.app.wordifynumbers.model.*
import com.app.wordifynumbers.utils.LargeNumberUtils
import java.util.*

/**
 * ViewModel for the Large Numbers Education feature
 */
class LargeNumbersViewModel : ViewModel() {
    // Input number as string
    var numberInput by mutableStateOf("")
        private set
    
    // Selected language
    var selectedLanguage by mutableStateOf("English")
        private set
    
    var selectedLocale by mutableStateOf(Locale.US)
        private set
    
    // Number breakdown
    var numberBreakdown by mutableStateOf<NumberBreakdown?>(null)
        private set
    
    // Visualization units
    var visualizationUnits by mutableStateOf<List<VisualizationUnit>>(emptyList())
        private set
    
    // Explanation text
    var explanationText by mutableStateOf("")
        private set
    
    // Predefined scale values
    val predefinedScales = listOf(
        "Thousand" to 1_000L,
        "Million" to 1_000_000L,
        "Billion" to 1_000_000_000L,
        "Trillion" to 1_000_000_000_000L,
        "Quadrillion" to 1_000_000_000_000_000L,
        "Quintillion" to 1_000_000_000_000_000_000L
    )
    
    // List of supported languages
    val supportedLanguages = listOf(
        "English" to Locale.US,
        "Spanish" to Locale("es"),
        "French" to Locale.FRENCH,
        "German" to Locale.GERMAN,
        "Chinese" to Locale.CHINESE,
        "Japanese" to Locale.JAPANESE,
        "Hindi" to Locale("hi"),
        "Arabic" to Locale("ar"),
        "Russian" to Locale("ru"),
        "Italian" to Locale.ITALIAN,
        "Portuguese" to Locale("pt"),
        "Korean" to Locale.KOREAN,
        "Turkish" to Locale("tr"),
        "Polish" to Locale("pl"),
        "Ukrainian" to Locale("uk"),
        "Dutch" to Locale("nl"),
        "Thai" to Locale("th"),
        "Indonesian" to Locale("id"),
        "Vietnamese" to Locale("vi"),
        "Bengali" to Locale("bn")
    )
    
    /**
     * Updates the input number
     */
    fun updateNumberInput(input: String) {
        numberInput = input.filter { it.isDigit() }
        processNumber()
    }
    
    /**
     * Selects a predefined scale
     */
    fun selectPredefinedScale(scale: Long) {
        numberInput = scale.toString()
        processNumber()
    }
    
    /**
     * Updates the selected language
     */
    fun updateLanguage(language: String, locale: Locale) {
        selectedLanguage = language
        selectedLocale = locale
        processNumber()
    }
    
    /**
     * Processes the current number input
     */
    private fun processNumber() {
        val number = numberInput.toLongOrNull() ?: 0L
        
        if (number > 0) {
            // Generate number breakdown
            numberBreakdown = LargeNumberUtils.breakdownLargeNumber(number, selectedLocale)
            
            // Generate visualization units
            visualizationUnits = LargeNumberUtils.visualizeNumber(number, selectedLocale)
            
            // Generate explanation text
            explanationText = LargeNumberUtils.getLargeNumberExplanation(number, selectedLocale)
        } else {
            numberBreakdown = null
            visualizationUnits = emptyList()
            explanationText = ""
        }
    }
    
    /**
     * Resets all inputs and results
     */
    fun reset() {
        numberInput = ""
        numberBreakdown = null
        visualizationUnits = emptyList()
        explanationText = ""
    }
}
