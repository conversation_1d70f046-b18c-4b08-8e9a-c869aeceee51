package com.app.wordifynumbers.util

import kotlinx.serialization.Serializable

/**
 * Enum representing different types of calculations for history tracking
 */
@Serializable
enum class CalculationType(val displayName: String) {
    BASIC_CALCULATION("Basic Calculation"),
    SCIENTIFIC_CALCULATION("Scientific Calculation"),
    PERCENTAGE_CALCULATION("Percentage Calculation"),
    COMPLEX_CALCULATION("Complex Numbers"),
    AREA_VOLUME_CALCULATION("Area/Volume Calculation"),
    UNIT_CONVERSION("Unit Conversion"),
    CURRENCY_CONVERSION("Currency Conversion"),
    FINANCIAL("Financial Calculation"),
    STATISTICS("Statistics"),
    HEALTH_CALCULATION("Health Calculation"),
    PROGRAMMER_CALCULATION("Programmer Calculation"),
    DATE_TIME_CALCULATION("Date/Time Calculation"),
    AGE_CALCULATION("Age Calculation")
}
