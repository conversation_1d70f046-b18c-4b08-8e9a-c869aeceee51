package com.app.wordifynumbers.ui.theme

import android.app.Activity
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.dp
import androidx.core.view.WindowCompat

// Theme constants
object NeonSpacing {
    val ExtraSmall = 4.dp
    val Small = 8.dp
    val Medium = 16.dp
    val Large = 24.dp
    val ExtraLarge = 32.dp
}

object NeonBorder {
    val Thin = 1.dp
    val Medium = 2.dp
    val Thick = 3.dp
}

object NeonElevation {
    val Level0 = 0.dp
    val Level1 = 2.dp
    val Level2 = 4.dp
    val Level3 = 8.dp
    val Level4 = 12.dp
}

object NeonAnimation {
    const val ShortDuration = 150
    const val MediumDuration = 300
    const val LongDuration = 500
}

private val NeonDarkColorScheme = darkColorScheme(
    primary = NeonGlow,
    secondary = NeonPurple,
    tertiary = NeonPink,
    background = NeonBackground,
    surface = NeonSurface,
    onPrimary = NeonText,
    onSecondary = NeonText,
    onTertiary = NeonText,
    onBackground = NeonText,
    onSurface = NeonText,
    surfaceVariant = NeonCard,
    onSurfaceVariant = NeonText,
    outline = NeonGlow,
    primaryContainer = NeonBlue.copy(alpha = 0.18f),
    secondaryContainer = NeonPurple.copy(alpha = 0.16f),
    tertiaryContainer = NeonPink.copy(alpha = 0.12f)
)

private val NeonLightColorScheme = lightColorScheme(
    primary = NeonGlow,
    secondary = NeonPurple,
    tertiary = NeonPink,
    background = Color.White,
    surface = Color(0xFFF8F9FF),
    onPrimary = Color.White,
    onSecondary = Color.White,
    onTertiary = Color.White,
    onBackground = Color(0xFF1A1C1E),
    onSurface = Color(0xFF1A1C1E),
    surfaceVariant = Color(0xFFE7E9EC),
    onSurfaceVariant = Color(0xFF1A1C1E),
    outline = NeonGlow,
    primaryContainer = NeonBlue.copy(alpha = 0.1f),
    secondaryContainer = NeonPurple.copy(alpha = 0.1f),
    tertiaryContainer = NeonPink.copy(alpha = 0.1f)
)

@Composable
fun WordifyNumbersTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colorScheme = if (darkTheme) NeonDarkColorScheme else NeonLightColorScheme
    val view = LocalView.current

    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            WindowCompat.setDecorFitsSystemWindows(window, false)
            window.statusBarColor = Color.Transparent.toArgb()
            window.navigationBarColor = Color.Transparent.toArgb()

            WindowCompat.getInsetsController(window, view).apply {
                isAppearanceLightStatusBars = !darkTheme
                isAppearanceLightNavigationBars = !darkTheme
            }

            window.decorView.setBackgroundColor(
                if (darkTheme) NeonDeepBlue.toArgb() else Color.White.toArgb()
            )
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}