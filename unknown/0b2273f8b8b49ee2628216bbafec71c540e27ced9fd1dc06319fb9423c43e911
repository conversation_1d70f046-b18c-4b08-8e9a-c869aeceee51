package com.app.wordifynumbers.util

import kotlin.math.*

data class Complex(
    val real: Double,
    val imag: Double
) {
    // Basic arithmetic operations
    operator fun plus(other: Complex) = Complex(real + other.real, imag + other.imag)
    operator fun minus(other: Complex) = Complex(real - other.real, imag - other.imag)
    operator fun times(other: Complex) =
        Complex(
            real * other.real - imag * other.imag,
            real * other.imag + imag * other.real
        )
    operator fun div(other: Complex): Complex {
        val denom = other.real * other.real + other.imag * other.imag
        if (denom == 0.0) throw ArithmeticException("Division by zero")
        return Complex(
            (real * other.real + imag * other.imag) / denom,
            (imag * other.real - real * other.imag) / denom
        )
    }

    // Unary operations
    operator fun unaryMinus() = Complex(-real, -imag)
    fun conjugate() = Complex(real, -imag)

    // Properties
    fun magnitude(): Double = sqrt(real * real + imag * imag)
    fun phase(): Double = atan2(imag, real)

    // Advanced operations
    fun pow(n: Double): Complex {
        val r = magnitude()
        val theta = phase()
        val newR = r.pow(n)
        val newTheta = n * theta
        return Complex(
            newR * cos(newTheta),
            newR * sin(newTheta)
        )
    }

    fun sqrt(): Complex = pow(0.5)

    fun exp(): Complex {
        val expReal = exp(real)
        return Complex(
            expReal * cos(imag),
            expReal * sin(imag)
        )
    }

    fun ln(): Complex {
        val r = magnitude()
        val theta = phase()
        return Complex(ln(r), theta)
    }

    fun sin(): Complex {
        return Complex(
            sin(real) * cosh(imag),
            cos(real) * sinh(imag)
        )
    }

    fun cos(): Complex {
        return Complex(
            cos(real) * cosh(imag),
            -sin(real) * sinh(imag)
        )
    }

    fun tan(): Complex {
        val cosZ = cos()
        // Check if the denominator is zero to avoid division by zero
        if (cosZ.real == 0.0 && cosZ.imag == 0.0) {
            throw ArithmeticException("Division by zero in tan")
        }
        return sin().div(cosZ)
    }

    // String representations
    override fun toString(): String {
        val sign = if (imag >= 0) "+" else "-"
        return String.format("%.2f %s %.2fi", real, sign, kotlin.math.abs(imag))
    }

    fun toPolarString(): String {
        val r = magnitude()
        val theta = phase() * 180 / Math.PI
        return String.format("%.2f ∠ %.2f°", r, theta)
    }

    fun toExponentialString(): String {
        val r = magnitude()
        val theta = phase()
        return String.format("%.2f e^(%.2fi)", r, theta)
    }

    companion object {
        // Constants
        val ZERO = Complex(0.0, 0.0)
        val ONE = Complex(1.0, 0.0)
        val I = Complex(0.0, 1.0)

        // Create from polar form
        fun fromPolar(magnitude: Double, phaseRadians: Double): Complex {
            return Complex(
                magnitude * cos(phaseRadians),
                magnitude * sin(phaseRadians)
            )
        }

        fun fromPolarDegrees(magnitude: Double, phaseDegrees: Double): Complex {
            val phaseRadians = phaseDegrees * Math.PI / 180.0
            return fromPolar(magnitude, phaseRadians)
        }
    }
}

enum class ComplexOperation(val symbol: String, val displayName: String) {
    // Basic operations
    ADD("+", "Add"),
    SUBTRACT("-", "Subtract"),
    MULTIPLY("×", "Multiply"),
    DIVIDE("÷", "Divide"),

    // Unary operations
    CONJUGATE("z*", "Conjugate"),
    NEGATE("-z", "Negate"),

    // Power operations
    POWER("z^n", "Power"),
    SQUARE("z²", "Square"),
    SQRT("√z", "Square Root"),

    // Exponential and logarithmic
    EXP("e^z", "Exponential"),
    LN("ln(z)", "Natural Log"),

    // Trigonometric
    SIN("sin(z)", "Sine"),
    COS("cos(z)", "Cosine"),
    TAN("tan(z)", "Tangent")
}
