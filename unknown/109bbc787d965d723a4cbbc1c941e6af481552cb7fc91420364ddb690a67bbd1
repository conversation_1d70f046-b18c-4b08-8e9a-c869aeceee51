package com.app.wordifynumbers

import com.app.wordifynumbers.util.ExpressionEvaluator
import org.junit.Assert.assertEquals
import org.junit.Assert.assertThrows
import org.junit.Test
import kotlin.math.PI

class ExpressionEvaluatorTest {

    private val delta = 0.0001 // Delta for floating point comparisons

    @Test
    fun testBasicArithmetic() {
        assertEquals(5.0, ExpressionEvaluator.evaluate("2+3"), delta)
        assertEquals(-1.0, ExpressionEvaluator.evaluate("2-3"), delta)
        assertEquals(6.0, ExpressionEvaluator.evaluate("2*3"), delta)
        assertEquals(2.0, ExpressionEvaluator.evaluate("6/3"), delta)
        assertEquals(8.0, ExpressionEvaluator.evaluate("2^3"), delta)
        assertEquals(2.0, ExpressionEvaluator.evaluate("8%3"), delta)
    }

    @Test
    fun testNegativeNumbers() {
        assertEquals(-5.0, ExpressionEvaluator.evaluate("-5"), delta)
        assertEquals(-1.0, ExpressionEvaluator.evaluate("-1"), delta)
        assertEquals(-8.0, ExpressionEvaluator.evaluate("-2*4"), delta)
        assertEquals(8.0, ExpressionEvaluator.evaluate("-2*-4"), delta)
        assertEquals(-0.5, ExpressionEvaluator.evaluate("-2/4"), delta)
    }

    @Test
    fun testParentheses() {
        assertEquals(14.0, ExpressionEvaluator.evaluate("2*(3+4)"), delta)
        assertEquals(10.0, ExpressionEvaluator.evaluate("(2+3)*2"), delta)
        assertEquals(13.0, ExpressionEvaluator.evaluate("5+(2*4)"), delta)
        assertEquals(7.0, ExpressionEvaluator.evaluate("5+2*1"), delta)
        assertEquals(11.0, ExpressionEvaluator.evaluate("(5+2)*1.5+0.5"), delta)
    }

    @Test
    fun testImplicitMultiplication() {
        assertEquals(14.0, ExpressionEvaluator.evaluate("2(3+4)"), delta)
        assertEquals(10.0, ExpressionEvaluator.evaluate("(2+3)2"), delta)
        assertEquals(20.0, ExpressionEvaluator.evaluate("(2+3)(2+2)"), delta)
    }

    @Test
    fun testDecimalNumbers() {
        assertEquals(3.5, ExpressionEvaluator.evaluate("1.5+2"), delta)
        assertEquals(3.75, ExpressionEvaluator.evaluate("1.5*2.5"), delta)
        assertEquals(0.75, ExpressionEvaluator.evaluate("3/4"), delta)
    }

    @Test
    fun testOperatorPrecedence() {
        assertEquals(11.0, ExpressionEvaluator.evaluate("3+4*2"), delta)
        assertEquals(14.0, ExpressionEvaluator.evaluate("(3+4)*2"), delta)
        assertEquals(17.0, ExpressionEvaluator.evaluate("3+4*2+6"), delta)
        assertEquals(27.0, ExpressionEvaluator.evaluate("3^3"), delta)
        assertEquals(512.0, ExpressionEvaluator.evaluate("2^3^2"), delta) // Right-associative: 2^(3^2) = 2^9 = 512
    }

    @Test
    fun testTrigonometricFunctions() {
        assertEquals(0.0, ExpressionEvaluator.evaluate("sin(0)"), delta)
        assertEquals(1.0, ExpressionEvaluator.evaluate("sin(90)"), delta)
        assertEquals(1.0, ExpressionEvaluator.evaluate("cos(0)"), delta)
        assertEquals(0.0, ExpressionEvaluator.evaluate("cos(90)"), delta)
    }

    @Test
    fun testLogarithmicFunctions() {
        assertEquals(1.0, ExpressionEvaluator.evaluate("log(10)"), delta)
        assertEquals(2.0, ExpressionEvaluator.evaluate("log(100)"), delta)
        assertEquals(0.0, ExpressionEvaluator.evaluate("ln(1)"), delta)
        assertEquals(1.0, ExpressionEvaluator.evaluate("ln(e)"), delta)
    }

    @Test
    fun testErrorHandling() {
        assertThrows(ArithmeticException::class.java) {
            ExpressionEvaluator.evaluate("5/0")
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            ExpressionEvaluator.evaluate("5++3")
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            ExpressionEvaluator.evaluate("5**3")
        }
        
        assertThrows(IllegalArgumentException::class.java) {
            ExpressionEvaluator.evaluate("(5+3")
        }
    }
}
