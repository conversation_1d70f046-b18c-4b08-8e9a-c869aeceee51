package com.app.wordifynumbers.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.app.wordifynumbers.util.DateCalculator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.Period
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.time.temporal.ChronoUnit
import java.time.temporal.WeekFields
import java.util.Locale
import kotlin.math.abs

/**
 * ViewModel for the Date and Time Calculator screen
 * Handles business logic for date and time calculations
 */
class DateTimeCalculatorViewModel : ViewModel() {

    // Date calculation mode
    private val _dateMode = MutableStateFlow(DateMode.DIFFERENCE)
    val dateMode: StateFlow<DateMode> = _dateMode.asStateFlow()

    // Time calculation mode
    private val _timeMode = MutableStateFlow(TimeMode.DIFFERENCE)
    val timeMode: StateFlow<TimeMode> = _timeMode.asStateFlow()

    // Calculator mode (date or time)
    private val _calculatorMode = MutableStateFlow(CalculatorMode.DATE)
    val calculatorMode: StateFlow<CalculatorMode> = _calculatorMode.asStateFlow()

    // First date for calculations
    private val _date1 = MutableStateFlow(LocalDate.now())
    val date1: StateFlow<LocalDate> = _date1.asStateFlow()

    // Second date for calculations
    private val _date2 = MutableStateFlow(LocalDate.now())
    val date2: StateFlow<LocalDate> = _date2.asStateFlow()

    // First time for calculations
    private val _time1 = MutableStateFlow(LocalTime.now())
    val time1: StateFlow<LocalTime> = _time1.asStateFlow()

    // Second time for calculations
    private val _time2 = MutableStateFlow(LocalTime.now())
    val time2: StateFlow<LocalTime> = _time2.asStateFlow()

    // Days to add/subtract
    private val _daysToAdd = MutableStateFlow("")
    val daysToAdd: StateFlow<String> = _daysToAdd.asStateFlow()

    // Hours to add/subtract
    private val _hoursToAdd = MutableStateFlow("")
    val hoursToAdd: StateFlow<String> = _hoursToAdd.asStateFlow()

    // Minutes to add/subtract
    private val _minutesToAdd = MutableStateFlow("")
    val minutesToAdd: StateFlow<String> = _minutesToAdd.asStateFlow()

    // Seconds to add/subtract
    private val _secondsToAdd = MutableStateFlow("")
    val secondsToAdd: StateFlow<String> = _secondsToAdd.asStateFlow()

    // Date calculation result
    private val _dateResult = MutableStateFlow<DateResult?>(null)
    val dateResult: StateFlow<DateResult?> = _dateResult.asStateFlow()

    // Time calculation result
    private val _timeResult = MutableStateFlow<TimeResult?>(null)
    val timeResult: StateFlow<TimeResult?> = _timeResult.asStateFlow()

    // Error message
    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Available locales for date/time formatting
    private val _availableLocales = MutableStateFlow(getCommonLocales())
    val availableLocales: StateFlow<List<Locale>> = _availableLocales.asStateFlow()

    // Selected locale for formatting
    private val _selectedLocale = MutableStateFlow(Locale.getDefault())
    val selectedLocale: StateFlow<Locale> = _selectedLocale.asStateFlow()

    /**
     * Set the calculator mode (date or time)
     */
    fun setCalculatorMode(mode: CalculatorMode) {
        _calculatorMode.value = mode
        // Reset results when switching modes
        _dateResult.value = null
        _timeResult.value = null
        _error.value = null
    }

    /**
     * Set the date calculation mode
     */
    fun setDateMode(mode: DateMode) {
        _dateMode.value = mode
        // Reset results when switching modes
        _dateResult.value = null
        _error.value = null
    }

    /**
     * Set the time calculation mode
     */
    fun setTimeMode(mode: TimeMode) {
        _timeMode.value = mode
        // Reset results when switching modes
        _timeResult.value = null
        _error.value = null
    }

    /**
     * Set the first date
     */
    fun setDate1(date: LocalDate) {
        _date1.value = date
        calculateDateResult()
    }

    /**
     * Set the second date
     */
    fun setDate2(date: LocalDate) {
        _date2.value = date
        calculateDateResult()
    }

    /**
     * Set the first time
     */
    fun setTime1(time: LocalTime) {
        _time1.value = time
        calculateTimeResult()
    }

    /**
     * Set the second time
     */
    fun setTime2(time: LocalTime) {
        _time2.value = time
        calculateTimeResult()
    }

    /**
     * Set the days to add/subtract
     */
    fun setDaysToAdd(days: String) {
        _daysToAdd.value = days
        calculateDateResult()
    }

    /**
     * Set the hours to add/subtract
     */
    fun setHoursToAdd(hours: String) {
        _hoursToAdd.value = hours
        calculateTimeResult()
    }

    /**
     * Set the minutes to add/subtract
     */
    fun setMinutesToAdd(minutes: String) {
        _minutesToAdd.value = minutes
        calculateTimeResult()
    }

    /**
     * Set the seconds to add/subtract
     */
    fun setSecondsToAdd(seconds: String) {
        _secondsToAdd.value = seconds
        calculateTimeResult()
    }

    /**
     * Set the selected locale for formatting
     */
    fun setSelectedLocale(locale: Locale) {
        _selectedLocale.value = locale
        // Recalculate results with new locale
        calculateDateResult()
        calculateTimeResult()
    }

    /**
     * Calculate the date result based on the current mode and inputs
     */
    fun calculateDateResult() {
        viewModelScope.launch {
            try {
                _error.value = null

                when (_dateMode.value) {
                    DateMode.DIFFERENCE -> {
                        val period = DateCalculator.calculateDateDifference(_date1.value, _date2.value)
                        val totalDays = DateCalculator.calculateDaysBetween(_date1.value, _date2.value)

                        _dateResult.value = DateResult(
                            mainResult = "${totalDays} days",
                            details = listOf(
                                DateCalculator.formatPeriod(period),
                                "${totalDays} total days",
                                "${formatDate(_date1.value)} to ${formatDate(_date2.value)}",
                                "${formatWeeks(totalDays)} weeks",
                                "${formatMonths(period)} months"
                            )
                        )
                    }
                    DateMode.ADD_SUBTRACT -> {
                        val days = _daysToAdd.value.toIntOrNull()
                        if (days == null) {
                            _dateResult.value = null
                            return@launch
                        }

                        val resultDate = DateCalculator.addDays(_date1.value, days.toLong())
                        _dateResult.value = DateResult(
                            mainResult = formatDate(resultDate),
                            details = listOf(
                                if (days >= 0) "Adding $days days to ${formatDate(_date1.value)}"
                                else "Subtracting ${abs(days)} days from ${formatDate(_date1.value)}",
                                "Day of week: ${formatDayOfWeek(resultDate)}",
                                "Week of year: ${getWeekOfYear(resultDate)}",
                                "Day of year: ${resultDate.dayOfYear}"
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                _error.value = "Error: ${e.message ?: "Unknown error"}"
                _dateResult.value = null
            }
        }
    }

    /**
     * Calculate the time result based on the current mode and inputs
     */
    fun calculateTimeResult() {
        viewModelScope.launch {
            try {
                _error.value = null

                when (_timeMode.value) {
                    TimeMode.DIFFERENCE -> {
                        // Create LocalDateTime objects with the same date but different times
                        val dateTime1 = LocalDateTime.of(LocalDate.now(), _time1.value)
                        val dateTime2 = LocalDateTime.of(LocalDate.now(), _time2.value)

                        // If time2 is earlier than time1, add a day to dateTime2
                        val adjustedDateTime2 = if (_time2.value.isBefore(_time1.value)) {
                            LocalDateTime.of(LocalDate.now().plusDays(1), _time2.value)
                        } else {
                            dateTime2
                        }

                        val duration = Duration.between(dateTime1, adjustedDateTime2)
                        val totalSeconds = duration.seconds
                        val hours = totalSeconds / 3600
                        val minutes = (totalSeconds % 3600) / 60
                        val seconds = totalSeconds % 60

                        _timeResult.value = TimeResult(
                            mainResult = String.format("%02d:%02d:%02d", hours, minutes, seconds),
                            details = listOf(
                                "$hours hours, $minutes minutes, $seconds seconds",
                                "Total minutes: ${totalSeconds / 60}",
                                "Total seconds: $totalSeconds",
                                "${formatTime(_time1.value)} to ${formatTime(_time2.value)}"
                            )
                        )
                    }
                    TimeMode.ADD_SUBTRACT -> {
                        val hours = _hoursToAdd.value.toIntOrNull() ?: 0
                        val minutes = _minutesToAdd.value.toIntOrNull() ?: 0
                        val seconds = _secondsToAdd.value.toIntOrNull() ?: 0

                        if (hours == 0 && minutes == 0 && seconds == 0) {
                            _timeResult.value = null
                            return@launch
                        }

                        val resultTime = _time1.value
                            .plusHours(hours.toLong())
                            .plusMinutes(minutes.toLong())
                            .plusSeconds(seconds.toLong())

                        _timeResult.value = TimeResult(
                            mainResult = formatTime(resultTime),
                            details = listOf(
                                if (hours >= 0 && minutes >= 0 && seconds >= 0)
                                    "Adding $hours hours, $minutes minutes, $seconds seconds to ${formatTime(_time1.value)}"
                                else
                                    "Subtracting ${abs(hours)} hours, ${abs(minutes)} minutes, ${abs(seconds)} seconds from ${formatTime(_time1.value)}",
                                "24-hour format: ${formatTime(resultTime, "HH:mm:ss")}",
                                "12-hour format: ${formatTime(resultTime, "hh:mm:ss a")}"
                            )
                        )
                    }
                }
            } catch (e: Exception) {
                _error.value = "Error: ${e.message ?: "Unknown error"}"
                _timeResult.value = null
            }
        }
    }

    /**
     * Format a date according to the selected locale
     */
    private fun formatDate(date: LocalDate): String {
        return try {
            val formatter = DateTimeFormatter.ofLocalizedDate(FormatStyle.MEDIUM)
                .withLocale(_selectedLocale.value)
            date.format(formatter)
        } catch (e: Exception) {
            // Fallback to ISO format if locale-specific formatting fails
            date.format(DateTimeFormatter.ISO_LOCAL_DATE)
        }
    }

    /**
     * Format a date with a specific pattern
     */
    fun formatDate(date: LocalDate, pattern: String): String {
        return try {
            val formatter = DateTimeFormatter.ofPattern(pattern, _selectedLocale.value)
            date.format(formatter)
        } catch (e: Exception) {
            // Fallback to ISO format if pattern formatting fails
            date.format(DateTimeFormatter.ISO_LOCAL_DATE)
        }
    }

    /**
     * Format a time according to the selected locale
     */
    private fun formatTime(time: LocalTime): String {
        return try {
            val formatter = DateTimeFormatter.ofLocalizedTime(FormatStyle.MEDIUM)
                .withLocale(_selectedLocale.value)
            time.format(formatter)
        } catch (e: Exception) {
            // Fallback to ISO format if locale-specific formatting fails
            time.format(DateTimeFormatter.ISO_LOCAL_TIME)
        }
    }

    /**
     * Format a time with a specific pattern
     */
    private fun formatTime(time: LocalTime, pattern: String): String {
        return try {
            val formatter = DateTimeFormatter.ofPattern(pattern, _selectedLocale.value)
            time.format(formatter)
        } catch (e: Exception) {
            // Fallback to ISO format if pattern formatting fails
            time.format(DateTimeFormatter.ISO_LOCAL_TIME)
        }
    }

    /**
     * Format the day of week according to the selected locale
     */
    private fun formatDayOfWeek(date: LocalDate): String {
        return try {
            val formatter = DateTimeFormatter.ofPattern("EEEE", _selectedLocale.value)
            date.format(formatter)
        } catch (e: Exception) {
            // Fallback to English if locale-specific formatting fails
            date.dayOfWeek.toString()
        }
    }

    /**
     * Get the week of year for a date
     */
    private fun getWeekOfYear(date: LocalDate): Int {
        val weekFields = WeekFields.of(_selectedLocale.value)
        return date.get(weekFields.weekOfWeekBasedYear())
    }

    /**
     * Format total days as weeks and days
     */
    private fun formatWeeks(totalDays: Long): String {
        val weeks = totalDays / 7
        val remainingDays = totalDays % 7
        return "$weeks weeks and $remainingDays days"
    }

    /**
     * Format a period as months
     */
    private fun formatMonths(period: Period): String {
        val totalMonths = period.years * 12 + period.months
        return "$totalMonths months"
    }

    /**
     * Get a list of common locales for date/time formatting
     */
    private fun getCommonLocales(): List<Locale> {
        return listOf(
            Locale.US,                  // English (US)
            Locale.UK,                  // English (UK)
            Locale.GERMANY,             // German
            Locale.FRANCE,              // French
            Locale.JAPAN,               // Japanese
            Locale.CHINA,               // Chinese
            Locale.KOREA,               // Korean
            Locale("es", "ES"),         // Spanish
            Locale("it", "IT"),         // Italian
            Locale("ru", "RU"),         // Russian
            Locale("ar", "SA"),         // Arabic
            Locale("hi", "IN"),         // Hindi
            Locale("pt", "BR"),         // Portuguese (Brazil)
            Locale("tr", "TR"),         // Turkish
            Locale("nl", "NL"),         // Dutch
            Locale("sv", "SE"),         // Swedish
            Locale("pl", "PL"),         // Polish
            Locale("th", "TH"),         // Thai
            Locale("ur", "PK"),         // Urdu
            Locale("vi", "VN")          // Vietnamese
        )
    }

    /**
     * Get the display name for a locale
     */
    fun getLocaleDisplayName(locale: Locale): String {
        return "${locale.getDisplayLanguage(Locale.US)} (${locale.country})"
    }
}

/**
 * Enum representing the date calculation mode
 */
enum class DateMode(val displayName: String) {
    DIFFERENCE("Date Difference"),
    ADD_SUBTRACT("Add/Subtract Days")
}

/**
 * Enum representing the time calculation mode
 */
enum class TimeMode(val displayName: String) {
    DIFFERENCE("Time Difference"),
    ADD_SUBTRACT("Add/Subtract Time")
}

/**
 * Enum representing the calculator mode
 */
enum class CalculatorMode(val displayName: String) {
    DATE("Date Calculator"),
    TIME("Time Calculator")
}

/**
 * Data class representing a date calculation result
 */
data class DateResult(
    val mainResult: String,
    val details: List<String>
)

/**
 * Data class representing a time calculation result
 */
data class TimeResult(
    val mainResult: String,
    val details: List<String>
)
