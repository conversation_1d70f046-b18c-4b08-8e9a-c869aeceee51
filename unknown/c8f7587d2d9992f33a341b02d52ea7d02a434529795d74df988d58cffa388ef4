package com.app.wordifynumbers

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

import com.app.wordifynumbers.utils.NumberToWords
import com.app.wordifynumbers.utils.DigitTranslator
import com.app.wordifynumbers.utils.CulturalInsights
import com.app.wordifynumbers.model.ConversionProfile
import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import androidx.annotation.RequiresApi
import com.ibm.icu.text.RuleBasedNumberFormat
import java.math.BigInteger
import java.util.Locale

class NumberConverterViewModel(private val context: Context) : ViewModel() {
    // State for Number to Words
    private val supportedLanguages = listOf(
        "English", "Chinese (Mandarin)", "Spanish", "Hindi", "Arabic",
        "Bengali", "Portuguese", "Russian", "Japanese", "German",
        "French", "Turkish", "Korean", "Italian", "Polish",
        "Ukrainian", "Dutch", "Thai", "Indonesian", "Vietnamese",
        "Urdu"
    )
    private val _languages = MutableStateFlow(supportedLanguages)
    val languages: StateFlow<List<String>> = _languages
    private val _numberInput = MutableStateFlow("")
    val numberInput: StateFlow<String> = _numberInput
    private val _numberWords = MutableStateFlow("")
    val numberWords: StateFlow<String> = _numberWords
    private val _detailedNumberWords = MutableStateFlow("")
    val detailedNumberWords: StateFlow<String> = _detailedNumberWords
    private val _supportsDetailedFormat = MutableStateFlow(false)
    val supportsDetailedFormat: StateFlow<Boolean> = _supportsDetailedFormat
    private val _numberError = MutableStateFlow<String?>(null)
    val numberError: StateFlow<String?> = _numberError

    // State for Digit Translator
    private val _selectedLanguage = MutableStateFlow("English")
    val selectedLanguage: StateFlow<String> = _selectedLanguage
    private val _digitTranslations = MutableStateFlow<List<Pair<String, String>>>(emptyList())
    val digitTranslations: StateFlow<List<Pair<String, String>>> = _digitTranslations

    // State for Cultural Insights
    private val _currentFact = MutableStateFlow("")
    val currentFact: StateFlow<String> = _currentFact

    // State for Profiles
    private val _profiles = MutableStateFlow<List<ConversionProfile>>(emptyList())
    val profiles: StateFlow<List<ConversionProfile>> = _profiles
    private val _selectedProfile = MutableStateFlow<ConversionProfile?>(null)
    val selectedProfile: StateFlow<ConversionProfile?> = _selectedProfile

    // History
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("wordify_prefs", Context.MODE_PRIVATE)

    init {
        loadProfiles()
        loadHistory()
        updateDigitTranslations()
    }

    fun onNumberInputChange(input: String) {
        _numberInput.value = input
        if (input.isBlank()) {
            _numberError.value = null
            _numberWords.value = ""
            return
        }
        try {
            val num = input.toBigIntegerOrNull() ?: BigInteger.ZERO
            if (num < BigInteger.ZERO) {
                _numberError.value = "Please enter a positive number."
                _numberWords.value = ""
            } else {
                // Convert to Long if within range, otherwise handle as BigInteger
                val numLong = if (num <= BigInteger.valueOf(Long.MAX_VALUE)) num.toLong() else Long.MAX_VALUE
                val locale = when (_selectedLanguage.value) {
                    "Spanish" -> Locale("es")
                    "French" -> Locale("fr")
                    "German" -> Locale("de")
                    "Portuguese" -> Locale("pt")
                    "Chinese (Mandarin)" -> Locale.CHINESE
                    "Japanese" -> Locale.JAPANESE
                    "Arabic" -> Locale("ar")
                    "Russian" -> Locale("ru")
                    "Italian" -> Locale.ITALIAN
                    "Hindi" -> Locale("hi")
                    "Bengali" -> Locale("bn")
                    "Indonesian" -> Locale("id")
                    "Turkish" -> Locale("tr")
                    "Korean" -> Locale.KOREAN
                    "Polish" -> Locale("pl")
                    "Ukrainian" -> Locale("uk")
                    "Dutch" -> Locale("nl")
                    "Thai" -> Locale("th")
                    "Vietnamese" -> Locale("vi")
                    "Urdu" -> Locale("ur")
                    else -> Locale.US
                }

                try {
                    // Special handling for Urdu
                    val words = if (locale.language == "ur") {
                        // Use our custom Urdu converter
                        if (num > BigInteger.valueOf(Long.MAX_VALUE)) {
                            val truncatedNum = Long.MAX_VALUE
                            val truncatedWords = NumberToWords.convertUrdu(truncatedNum)
                            _numberError.value = "Number is very large. Showing approximate conversion."
                            truncatedWords + " (بہت بڑا نمبر)"
                        } else {
                            NumberToWords.convertUrdu(numLong)
                        }
                    } else {
                        // Use ICU4J for other languages
                        val formatter = RuleBasedNumberFormat(locale, RuleBasedNumberFormat.SPELLOUT)

                        // Handle very large numbers by breaking them down if needed
                        if (num > BigInteger.valueOf(Long.MAX_VALUE)) {
                            // For extremely large numbers, show a message about the limitation
                            val truncatedNum = Long.MAX_VALUE
                            val truncatedWords = formatter.format(truncatedNum)
                            _numberError.value = "Number is very large. Showing approximate conversion."
                            truncatedWords + " (extremely large number)"
                        } else {
                            formatter.format(numLong)
                        }
                    }

                    // Format the words to follow a consistent digit pattern
                    val formattedWords = NumberToWords.formatNumberWords(words)
                    _numberWords.value = formattedWords

                    // Always enable detailed format for testing
                    _supportsDetailedFormat.value = true

                    // Generate detailed format
                    val detailedWords = NumberToWords.formatDetailedNumberWords(words)
                    _detailedNumberWords.value = detailedWords

                    if (_numberError.value == null) {
                        _numberError.value = null
                    }
                    addToHistory("$input → $formattedWords")
                    getFactForNumber(numLong)
                } catch (e: Exception) {
                    // Fallback to our custom converters or English
                    val words = if (locale.language == "ur") {
                        // Use our custom Urdu converter as fallback for Urdu
                        if (num > BigInteger.valueOf(Long.MAX_VALUE)) {
                            val truncatedNum = Long.MAX_VALUE
                            val truncatedWords = NumberToWords.convertUrdu(truncatedNum)
                            truncatedWords + " (بہت بڑا نمبر)"
                        } else {
                            NumberToWords.convertUrdu(numLong)
                        }
                    } else {
                        // Fallback to English for other languages
                        val englishFormatter = RuleBasedNumberFormat(Locale.US, RuleBasedNumberFormat.SPELLOUT)

                        if (num > BigInteger.valueOf(Long.MAX_VALUE)) {
                            val truncatedNum = Long.MAX_VALUE
                            val truncatedWords = englishFormatter.format(truncatedNum)
                            truncatedWords + " (extremely large number)"
                        } else {
                            englishFormatter.format(numLong)
                        }
                    }

                    // Format the words to follow a consistent digit pattern
                    val formattedWords = NumberToWords.formatNumberWords(words)
                    _numberWords.value = formattedWords

                    // Always enable detailed format for testing
                    _supportsDetailedFormat.value = true

                    // Generate detailed format
                    val detailedWords = NumberToWords.formatDetailedNumberWords(words)
                    _detailedNumberWords.value = detailedWords

                    if (locale.language != "ur") {
                        _numberError.value = "This language may not be fully supported. Showing result."
                    } else {
                        _numberError.value = null // Urdu is fully supported with our custom implementation
                    }
                    addToHistory("$input → $formattedWords")
                    getFactForNumber(numLong)
                }
            }
        } catch (e: Exception) {
            _numberError.value = "Invalid input. Please enter a valid number."
            _numberWords.value = ""
        }
    }

    fun onLanguageSelected(language: String) {
        _selectedLanguage.value = language
        updateDigitTranslations()
        // re-convert current input in new language
        onNumberInputChange(_numberInput.value)
    }

    // Clear number-to-words state
    fun resetNumberToWords() {
        _numberInput.value = ""
        _numberWords.value = ""
        _numberError.value = null
    }

    private fun updateDigitTranslations() {
        _digitTranslations.value = DigitTranslator.getTranslations(_selectedLanguage.value)
    }

    fun getFactForNumber(number: Long) {
        _currentFact.value = CulturalInsights.getFact(number)
    }

    fun nextFact() {
        _currentFact.value = CulturalInsights.getRandomFact()
    }

    // Profiles
    fun loadProfiles() {
        // Load from SharedPreferences or Room
        _profiles.value = ConversionProfile.loadProfiles(sharedPreferences)
    }

    @RequiresApi(Build.VERSION_CODES.N)
    fun saveProfile(profile: ConversionProfile) {
        ConversionProfile.saveProfile(sharedPreferences, profile)
        loadProfiles()
    }

    fun selectProfile(profile: ConversionProfile) {
        _selectedProfile.value = profile
    }

    // History
    fun addToHistory(entry: String) {
        val history = loadHistory().toMutableList()
        history.add(0, entry)
        if (history.size > 10) history.removeAt(history.lastIndex)
        sharedPreferences.edit().putStringSet("history", history.toSet()).apply()
    }

    fun loadHistory(): List<String> {
        return sharedPreferences.getStringSet("history", emptySet())?.toList() ?: emptyList()
    }
}
