# Gradle JVM settings
org.gradle.jvmargs=-Xmx4g -XX:+UseParallelGC -XX:MaxMetaspaceSize=1g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
# org.gradle.java.home=${JAVA_HOME}

# Build optimizations
org.gradle.parallel=true
org.gradle.caching=true

# Enable Gradle Daemon
org.gradle.daemon=true

# Enable configuration cache
org.gradle.configuration-cache=true

# AndroidX settings
android.useAndroidX=true
android.nonTransitiveRClass=true

# Kotlin settings
kotlin.code.style=official
kotlin.incremental=true
kotlin.caching.enabled=true

# Compose compiler settings
android.defaults.buildfeatures.buildconfig=true
android.experimental.enableComposeCompilerReports=true