int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim fragment_close_enter 0x7f010018
int anim fragment_close_exit 0x7f010019
int anim fragment_fade_enter 0x7f01001a
int anim fragment_fade_exit 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim fragment_open_enter 0x7f01001d
int anim fragment_open_exit 0x7f01001e
int array assume_strong_biometrics_models 0x7f020000
int array crypto_fingerprint_fallback_prefixes 0x7f020001
int array crypto_fingerprint_fallback_vendors 0x7f020002
int array delay_showing_prompt_models 0x7f020003
int array hide_fingerprint_instantly_prefixes 0x7f020004
int attr action 0x7f030000
int attr actionBarDivider 0x7f030001
int attr actionBarItemBackground 0x7f030002
int attr actionBarPopupTheme 0x7f030003
int attr actionBarSize 0x7f030004
int attr actionBarSplitStyle 0x7f030005
int attr actionBarStyle 0x7f030006
int attr actionBarTabBarStyle 0x7f030007
int attr actionBarTabStyle 0x7f030008
int attr actionBarTabTextStyle 0x7f030009
int attr actionBarTheme 0x7f03000a
int attr actionBarWidgetTheme 0x7f03000b
int attr actionButtonStyle 0x7f03000c
int attr actionDropDownStyle 0x7f03000d
int attr actionLayout 0x7f03000e
int attr actionMenuTextAppearance 0x7f03000f
int attr actionMenuTextColor 0x7f030010
int attr actionModeBackground 0x7f030011
int attr actionModeCloseButtonStyle 0x7f030012
int attr actionModeCloseDrawable 0x7f030013
int attr actionModeCopyDrawable 0x7f030014
int attr actionModeCutDrawable 0x7f030015
int attr actionModeFindDrawable 0x7f030016
int attr actionModePasteDrawable 0x7f030017
int attr actionModePopupWindowStyle 0x7f030018
int attr actionModeSelectAllDrawable 0x7f030019
int attr actionModeShareDrawable 0x7f03001a
int attr actionModeSplitBackground 0x7f03001b
int attr actionModeStyle 0x7f03001c
int attr actionModeWebSearchDrawable 0x7f03001d
int attr actionOverflowButtonStyle 0x7f03001e
int attr actionOverflowMenuStyle 0x7f03001f
int attr actionProviderClass 0x7f030020
int attr actionViewClass 0x7f030021
int attr activityChooserViewStyle 0x7f030022
int attr alertDialogButtonGroupStyle 0x7f030023
int attr alertDialogCenterButtons 0x7f030024
int attr alertDialogStyle 0x7f030025
int attr alertDialogTheme 0x7f030026
int attr allowStacking 0x7f030027
int attr alpha 0x7f030028
int attr alphabeticModifiers 0x7f030029
int attr argType 0x7f03002a
int attr arrowHeadLength 0x7f03002b
int attr arrowShaftLength 0x7f03002c
int attr autoCompleteTextViewStyle 0x7f03002d
int attr autoSizeMaxTextSize 0x7f03002e
int attr autoSizeMinTextSize 0x7f03002f
int attr autoSizePresetSizes 0x7f030030
int attr autoSizeStepGranularity 0x7f030031
int attr autoSizeTextType 0x7f030032
int attr background 0x7f030033
int attr backgroundSplit 0x7f030034
int attr backgroundStacked 0x7f030035
int attr backgroundTint 0x7f030036
int attr backgroundTintMode 0x7f030037
int attr barLength 0x7f030038
int attr borderlessButtonStyle 0x7f030039
int attr buttonBarButtonStyle 0x7f03003a
int attr buttonBarNegativeButtonStyle 0x7f03003b
int attr buttonBarNeutralButtonStyle 0x7f03003c
int attr buttonBarPositiveButtonStyle 0x7f03003d
int attr buttonBarStyle 0x7f03003e
int attr buttonCompat 0x7f03003f
int attr buttonGravity 0x7f030040
int attr buttonIconDimen 0x7f030041
int attr buttonPanelSideLayout 0x7f030042
int attr buttonStyle 0x7f030043
int attr buttonStyleSmall 0x7f030044
int attr buttonTint 0x7f030045
int attr buttonTintMode 0x7f030046
int attr checkboxStyle 0x7f030047
int attr checkedTextViewStyle 0x7f030048
int attr closeIcon 0x7f030049
int attr closeItemLayout 0x7f03004a
int attr collapseContentDescription 0x7f03004b
int attr collapseIcon 0x7f03004c
int attr color 0x7f03004d
int attr colorAccent 0x7f03004e
int attr colorBackgroundFloating 0x7f03004f
int attr colorButtonNormal 0x7f030050
int attr colorControlActivated 0x7f030051
int attr colorControlHighlight 0x7f030052
int attr colorControlNormal 0x7f030053
int attr colorError 0x7f030054
int attr colorPrimary 0x7f030055
int attr colorPrimaryDark 0x7f030056
int attr colorSwitchThumbNormal 0x7f030057
int attr commitIcon 0x7f030058
int attr contentDescription 0x7f030059
int attr contentInsetEnd 0x7f03005a
int attr contentInsetEndWithActions 0x7f03005b
int attr contentInsetLeft 0x7f03005c
int attr contentInsetRight 0x7f03005d
int attr contentInsetStart 0x7f03005e
int attr contentInsetStartWithNavigation 0x7f03005f
int attr controlBackground 0x7f030060
int attr customNavigationLayout 0x7f030061
int attr data 0x7f030062
int attr dataPattern 0x7f030063
int attr defaultQueryHint 0x7f030064
int attr destination 0x7f030065
int attr dialogCornerRadius 0x7f030066
int attr dialogPreferredPadding 0x7f030067
int attr dialogTheme 0x7f030068
int attr displayOptions 0x7f030069
int attr divider 0x7f03006a
int attr dividerHorizontal 0x7f03006b
int attr dividerPadding 0x7f03006c
int attr dividerVertical 0x7f03006d
int attr drawableBottomCompat 0x7f03006e
int attr drawableEndCompat 0x7f03006f
int attr drawableLeftCompat 0x7f030070
int attr drawableRightCompat 0x7f030071
int attr drawableSize 0x7f030072
int attr drawableStartCompat 0x7f030073
int attr drawableTint 0x7f030074
int attr drawableTintMode 0x7f030075
int attr drawableTopCompat 0x7f030076
int attr drawerArrowStyle 0x7f030077
int attr dropDownListViewStyle 0x7f030078
int attr dropdownListPreferredItemHeight 0x7f030079
int attr editTextBackground 0x7f03007a
int attr editTextColor 0x7f03007b
int attr editTextStyle 0x7f03007c
int attr elevation 0x7f03007d
int attr enterAnim 0x7f03007e
int attr exitAnim 0x7f03007f
int attr expandActivityOverflowButtonDrawable 0x7f030080
int attr firstBaselineToTopHeight 0x7f030081
int attr font 0x7f030082
int attr fontFamily 0x7f030083
int attr fontProviderAuthority 0x7f030084
int attr fontProviderCerts 0x7f030085
int attr fontProviderFetchStrategy 0x7f030086
int attr fontProviderFetchTimeout 0x7f030087
int attr fontProviderPackage 0x7f030088
int attr fontProviderQuery 0x7f030089
int attr fontProviderSystemFontFamily 0x7f03008a
int attr fontStyle 0x7f03008b
int attr fontVariationSettings 0x7f03008c
int attr fontWeight 0x7f03008d
int attr gapBetweenBars 0x7f03008e
int attr goIcon 0x7f03008f
int attr graph 0x7f030090
int attr height 0x7f030091
int attr hideOnContentScroll 0x7f030092
int attr homeAsUpIndicator 0x7f030093
int attr homeLayout 0x7f030094
int attr icon 0x7f030095
int attr iconTint 0x7f030096
int attr iconTintMode 0x7f030097
int attr iconifiedByDefault 0x7f030098
int attr imageButtonStyle 0x7f030099
int attr indeterminateProgressStyle 0x7f03009a
int attr initialActivityCount 0x7f03009b
int attr isLightTheme 0x7f03009c
int attr itemPadding 0x7f03009d
int attr lStar 0x7f03009e
int attr lastBaselineToBottomHeight 0x7f03009f
int attr launchSingleTop 0x7f0300a0
int attr layout 0x7f0300a1
int attr lineHeight 0x7f0300a2
int attr listChoiceBackgroundIndicator 0x7f0300a3
int attr listChoiceIndicatorMultipleAnimated 0x7f0300a4
int attr listChoiceIndicatorSingleAnimated 0x7f0300a5
int attr listDividerAlertDialog 0x7f0300a6
int attr listItemLayout 0x7f0300a7
int attr listLayout 0x7f0300a8
int attr listMenuViewStyle 0x7f0300a9
int attr listPopupWindowStyle 0x7f0300aa
int attr listPreferredItemHeight 0x7f0300ab
int attr listPreferredItemHeightLarge 0x7f0300ac
int attr listPreferredItemHeightSmall 0x7f0300ad
int attr listPreferredItemPaddingEnd 0x7f0300ae
int attr listPreferredItemPaddingLeft 0x7f0300af
int attr listPreferredItemPaddingRight 0x7f0300b0
int attr listPreferredItemPaddingStart 0x7f0300b1
int attr logo 0x7f0300b2
int attr logoDescription 0x7f0300b3
int attr maxButtonHeight 0x7f0300b4
int attr measureWithLargestChild 0x7f0300b5
int attr menu 0x7f0300b6
int attr mimeType 0x7f0300b7
int attr multiChoiceItemLayout 0x7f0300b8
int attr navGraph 0x7f0300b9
int attr navigationContentDescription 0x7f0300ba
int attr navigationIcon 0x7f0300bb
int attr navigationMode 0x7f0300bc
int attr nestedScrollViewStyle 0x7f0300bd
int attr nullable 0x7f0300be
int attr numericModifiers 0x7f0300bf
int attr overlapAnchor 0x7f0300c0
int attr paddingBottomNoButtons 0x7f0300c1
int attr paddingEnd 0x7f0300c2
int attr paddingStart 0x7f0300c3
int attr paddingTopNoTitle 0x7f0300c4
int attr panelBackground 0x7f0300c5
int attr panelMenuListTheme 0x7f0300c6
int attr panelMenuListWidth 0x7f0300c7
int attr popEnterAnim 0x7f0300c8
int attr popExitAnim 0x7f0300c9
int attr popUpTo 0x7f0300ca
int attr popUpToInclusive 0x7f0300cb
int attr popUpToSaveState 0x7f0300cc
int attr popupMenuStyle 0x7f0300cd
int attr popupTheme 0x7f0300ce
int attr popupWindowStyle 0x7f0300cf
int attr preserveIconSpacing 0x7f0300d0
int attr progressBarPadding 0x7f0300d1
int attr progressBarStyle 0x7f0300d2
int attr queryBackground 0x7f0300d3
int attr queryHint 0x7f0300d4
int attr queryPatterns 0x7f0300d5
int attr radioButtonStyle 0x7f0300d6
int attr ratingBarStyle 0x7f0300d7
int attr ratingBarStyleIndicator 0x7f0300d8
int attr ratingBarStyleSmall 0x7f0300d9
int attr restoreState 0x7f0300da
int attr route 0x7f0300db
int attr searchHintIcon 0x7f0300dc
int attr searchIcon 0x7f0300dd
int attr searchViewStyle 0x7f0300de
int attr seekBarStyle 0x7f0300df
int attr selectableItemBackground 0x7f0300e0
int attr selectableItemBackgroundBorderless 0x7f0300e1
int attr shortcutMatchRequired 0x7f0300e2
int attr showAsAction 0x7f0300e3
int attr showDividers 0x7f0300e4
int attr showText 0x7f0300e5
int attr showTitle 0x7f0300e6
int attr singleChoiceItemLayout 0x7f0300e7
int attr spinBars 0x7f0300e8
int attr spinnerDropDownItemStyle 0x7f0300e9
int attr spinnerStyle 0x7f0300ea
int attr splitTrack 0x7f0300eb
int attr srcCompat 0x7f0300ec
int attr startDestination 0x7f0300ed
int attr state_above_anchor 0x7f0300ee
int attr subMenuArrow 0x7f0300ef
int attr submitBackground 0x7f0300f0
int attr subtitle 0x7f0300f1
int attr subtitleTextAppearance 0x7f0300f2
int attr subtitleTextColor 0x7f0300f3
int attr subtitleTextStyle 0x7f0300f4
int attr suggestionRowLayout 0x7f0300f5
int attr switchMinWidth 0x7f0300f6
int attr switchPadding 0x7f0300f7
int attr switchStyle 0x7f0300f8
int attr switchTextAppearance 0x7f0300f9
int attr targetPackage 0x7f0300fa
int attr textAllCaps 0x7f0300fb
int attr textAppearanceLargePopupMenu 0x7f0300fc
int attr textAppearanceListItem 0x7f0300fd
int attr textAppearanceListItemSecondary 0x7f0300fe
int attr textAppearanceListItemSmall 0x7f0300ff
int attr textAppearancePopupMenuHeader 0x7f030100
int attr textAppearanceSearchResultSubtitle 0x7f030101
int attr textAppearanceSearchResultTitle 0x7f030102
int attr textAppearanceSmallPopupMenu 0x7f030103
int attr textColorAlertDialogListItem 0x7f030104
int attr textColorSearchUrl 0x7f030105
int attr textLocale 0x7f030106
int attr theme 0x7f030107
int attr thickness 0x7f030108
int attr thumbTextPadding 0x7f030109
int attr thumbTint 0x7f03010a
int attr thumbTintMode 0x7f03010b
int attr tickMark 0x7f03010c
int attr tickMarkTint 0x7f03010d
int attr tickMarkTintMode 0x7f03010e
int attr tint 0x7f03010f
int attr tintMode 0x7f030110
int attr title 0x7f030111
int attr titleMargin 0x7f030112
int attr titleMarginBottom 0x7f030113
int attr titleMarginEnd 0x7f030114
int attr titleMarginStart 0x7f030115
int attr titleMarginTop 0x7f030116
int attr titleMargins 0x7f030117
int attr titleTextAppearance 0x7f030118
int attr titleTextColor 0x7f030119
int attr titleTextStyle 0x7f03011a
int attr toolbarNavigationButtonStyle 0x7f03011b
int attr toolbarStyle 0x7f03011c
int attr tooltipForegroundColor 0x7f03011d
int attr tooltipFrameBackground 0x7f03011e
int attr tooltipText 0x7f03011f
int attr track 0x7f030120
int attr trackTint 0x7f030121
int attr trackTintMode 0x7f030122
int attr ttcIndex 0x7f030123
int attr uri 0x7f030124
int attr viewInflaterClass 0x7f030125
int attr voiceIcon 0x7f030126
int attr windowActionBar 0x7f030127
int attr windowActionBarOverlay 0x7f030128
int attr windowActionModeOverlay 0x7f030129
int attr windowFixedHeightMajor 0x7f03012a
int attr windowFixedHeightMinor 0x7f03012b
int attr windowFixedWidthMajor 0x7f03012c
int attr windowFixedWidthMinor 0x7f03012d
int attr windowMinWidthMajor 0x7f03012e
int attr windowMinWidthMinor 0x7f03012f
int attr windowNoTitle 0x7f030130
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_allow_stacked_button_bar 0x7f040001
int bool abc_config_actionMenuItemAllCaps 0x7f040002
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color androidx_core_ripple_material_light 0x7f05001b
int color androidx_core_secondary_text_default_material_light 0x7f05001c
int color background_floating_material_dark 0x7f05001d
int color background_floating_material_light 0x7f05001e
int color background_material_dark 0x7f05001f
int color background_material_light 0x7f050020
int color biometric_error_color 0x7f050021
int color bright_foreground_disabled_material_dark 0x7f050022
int color bright_foreground_disabled_material_light 0x7f050023
int color bright_foreground_inverse_material_dark 0x7f050024
int color bright_foreground_inverse_material_light 0x7f050025
int color bright_foreground_material_dark 0x7f050026
int color bright_foreground_material_light 0x7f050027
int color button_material_dark 0x7f050028
int color button_material_light 0x7f050029
int color call_notification_answer_color 0x7f05002a
int color call_notification_decline_color 0x7f05002b
int color dim_foreground_disabled_material_dark 0x7f05002c
int color dim_foreground_disabled_material_light 0x7f05002d
int color dim_foreground_material_dark 0x7f05002e
int color dim_foreground_material_light 0x7f05002f
int color error_color_material_dark 0x7f050030
int color error_color_material_light 0x7f050031
int color foreground_material_dark 0x7f050032
int color foreground_material_light 0x7f050033
int color highlighted_text_material_dark 0x7f050034
int color highlighted_text_material_light 0x7f050035
int color material_blue_grey_800 0x7f050036
int color material_blue_grey_900 0x7f050037
int color material_blue_grey_950 0x7f050038
int color material_deep_teal_200 0x7f050039
int color material_deep_teal_500 0x7f05003a
int color material_grey_100 0x7f05003b
int color material_grey_300 0x7f05003c
int color material_grey_50 0x7f05003d
int color material_grey_600 0x7f05003e
int color material_grey_800 0x7f05003f
int color material_grey_850 0x7f050040
int color material_grey_900 0x7f050041
int color neon_blue 0x7f050042
int color neon_card 0x7f050043
int color neon_cyan 0x7f050044
int color neon_deep_blue 0x7f050045
int color neon_glow 0x7f050046
int color neon_green 0x7f050047
int color neon_hint 0x7f050048
int color neon_magenta 0x7f050049
int color neon_nav 0x7f05004a
int color neon_orange 0x7f05004b
int color neon_pink 0x7f05004c
int color neon_purple 0x7f05004d
int color neon_red 0x7f05004e
int color neon_surface 0x7f05004f
int color neon_teal 0x7f050050
int color neon_text 0x7f050051
int color neon_yellow 0x7f050052
int color notification_action_color_filter 0x7f050053
int color notification_icon_bg_color 0x7f050054
int color primary_dark_material_dark 0x7f050055
int color primary_dark_material_light 0x7f050056
int color primary_material_dark 0x7f050057
int color primary_material_light 0x7f050058
int color primary_text_default_material_dark 0x7f050059
int color primary_text_default_material_light 0x7f05005a
int color primary_text_disabled_material_dark 0x7f05005b
int color primary_text_disabled_material_light 0x7f05005c
int color ripple_material_dark 0x7f05005d
int color ripple_material_light 0x7f05005e
int color secondary_text_default_material_dark 0x7f05005f
int color secondary_text_default_material_light 0x7f050060
int color secondary_text_disabled_material_dark 0x7f050061
int color secondary_text_disabled_material_light 0x7f050062
int color switch_thumb_disabled_material_dark 0x7f050063
int color switch_thumb_disabled_material_light 0x7f050064
int color switch_thumb_material_dark 0x7f050065
int color switch_thumb_material_light 0x7f050066
int color switch_thumb_normal_material_dark 0x7f050067
int color switch_thumb_normal_material_light 0x7f050068
int color tooltip_background_dark 0x7f050069
int color tooltip_background_light 0x7f05006a
int color vector_tint_color 0x7f05006b
int color vector_tint_theme_color 0x7f05006c
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_switch_padding 0x7f06003b
int dimen abc_text_size_body_1_material 0x7f06003c
int dimen abc_text_size_body_2_material 0x7f06003d
int dimen abc_text_size_button_material 0x7f06003e
int dimen abc_text_size_caption_material 0x7f06003f
int dimen abc_text_size_display_1_material 0x7f060040
int dimen abc_text_size_display_2_material 0x7f060041
int dimen abc_text_size_display_3_material 0x7f060042
int dimen abc_text_size_display_4_material 0x7f060043
int dimen abc_text_size_headline_material 0x7f060044
int dimen abc_text_size_large_material 0x7f060045
int dimen abc_text_size_medium_material 0x7f060046
int dimen abc_text_size_menu_header_material 0x7f060047
int dimen abc_text_size_menu_material 0x7f060048
int dimen abc_text_size_small_material 0x7f060049
int dimen abc_text_size_subhead_material 0x7f06004a
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004b
int dimen abc_text_size_title_material 0x7f06004c
int dimen abc_text_size_title_material_toolbar 0x7f06004d
int dimen compat_button_inset_horizontal_material 0x7f06004e
int dimen compat_button_inset_vertical_material 0x7f06004f
int dimen compat_button_padding_horizontal_material 0x7f060050
int dimen compat_button_padding_vertical_material 0x7f060051
int dimen compat_control_corner_material 0x7f060052
int dimen compat_notification_large_icon_max_height 0x7f060053
int dimen compat_notification_large_icon_max_width 0x7f060054
int dimen disabled_alpha_material_dark 0x7f060055
int dimen disabled_alpha_material_light 0x7f060056
int dimen fingerprint_icon_size 0x7f060057
int dimen highlight_alpha_material_colored 0x7f060058
int dimen highlight_alpha_material_dark 0x7f060059
int dimen highlight_alpha_material_light 0x7f06005a
int dimen hint_alpha_material_dark 0x7f06005b
int dimen hint_alpha_material_light 0x7f06005c
int dimen hint_pressed_alpha_material_dark 0x7f06005d
int dimen hint_pressed_alpha_material_light 0x7f06005e
int dimen notification_action_icon_size 0x7f06005f
int dimen notification_action_text_size 0x7f060060
int dimen notification_big_circle_margin 0x7f060061
int dimen notification_content_margin_start 0x7f060062
int dimen notification_large_icon_height 0x7f060063
int dimen notification_large_icon_width 0x7f060064
int dimen notification_main_column_padding_top 0x7f060065
int dimen notification_media_narrow_margin 0x7f060066
int dimen notification_right_icon_size 0x7f060067
int dimen notification_right_side_padding_top 0x7f060068
int dimen notification_small_icon_background_padding 0x7f060069
int dimen notification_small_icon_size_as_large 0x7f06006a
int dimen notification_subtext_size 0x7f06006b
int dimen notification_top_pad 0x7f06006c
int dimen notification_top_pad_large_text 0x7f06006d
int dimen tooltip_corner_radius 0x7f06006e
int dimen tooltip_horizontal_padding 0x7f06006f
int dimen tooltip_margin 0x7f060070
int dimen tooltip_precise_anchor_extra_offset 0x7f060071
int dimen tooltip_precise_anchor_threshold 0x7f060072
int dimen tooltip_vertical_padding 0x7f060073
int dimen tooltip_y_offset_non_touch 0x7f060074
int dimen tooltip_y_offset_touch 0x7f060075
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070001
int drawable abc_action_bar_item_background_material 0x7f070002
int drawable abc_btn_borderless_material 0x7f070003
int drawable abc_btn_check_material 0x7f070004
int drawable abc_btn_check_material_anim 0x7f070005
int drawable abc_btn_check_to_on_mtrl_000 0x7f070006
int drawable abc_btn_check_to_on_mtrl_015 0x7f070007
int drawable abc_btn_colored_material 0x7f070008
int drawable abc_btn_default_mtrl_shape 0x7f070009
int drawable abc_btn_radio_material 0x7f07000a
int drawable abc_btn_radio_material_anim 0x7f07000b
int drawable abc_btn_radio_to_on_mtrl_000 0x7f07000c
int drawable abc_btn_radio_to_on_mtrl_015 0x7f07000d
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f07000e
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f07000f
int drawable abc_cab_background_internal_bg 0x7f070010
int drawable abc_cab_background_top_material 0x7f070011
int drawable abc_cab_background_top_mtrl_alpha 0x7f070012
int drawable abc_control_background_material 0x7f070013
int drawable abc_dialog_material_background 0x7f070014
int drawable abc_edit_text_material 0x7f070015
int drawable abc_ic_ab_back_material 0x7f070016
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f070017
int drawable abc_ic_clear_material 0x7f070018
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070019
int drawable abc_ic_go_search_api_material 0x7f07001a
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f07001b
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f07001c
int drawable abc_ic_menu_overflow_material 0x7f07001d
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f07001e
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f07001f
int drawable abc_ic_menu_share_mtrl_alpha 0x7f070020
int drawable abc_ic_search_api_material 0x7f070021
int drawable abc_ic_star_black_16dp 0x7f070022
int drawable abc_ic_star_black_36dp 0x7f070023
int drawable abc_ic_star_black_48dp 0x7f070024
int drawable abc_ic_star_half_black_16dp 0x7f070025
int drawable abc_ic_star_half_black_36dp 0x7f070026
int drawable abc_ic_star_half_black_48dp 0x7f070027
int drawable abc_ic_voice_search_api_material 0x7f070028
int drawable abc_item_background_holo_dark 0x7f070029
int drawable abc_item_background_holo_light 0x7f07002a
int drawable abc_list_divider_material 0x7f07002b
int drawable abc_list_divider_mtrl_alpha 0x7f07002c
int drawable abc_list_focused_holo 0x7f07002d
int drawable abc_list_longpressed_holo 0x7f07002e
int drawable abc_list_pressed_holo_dark 0x7f07002f
int drawable abc_list_pressed_holo_light 0x7f070030
int drawable abc_list_selector_background_transition_holo_dark 0x7f070031
int drawable abc_list_selector_background_transition_holo_light 0x7f070032
int drawable abc_list_selector_disabled_holo_dark 0x7f070033
int drawable abc_list_selector_disabled_holo_light 0x7f070034
int drawable abc_list_selector_holo_dark 0x7f070035
int drawable abc_list_selector_holo_light 0x7f070036
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070037
int drawable abc_popup_background_mtrl_mult 0x7f070038
int drawable abc_ratingbar_indicator_material 0x7f070039
int drawable abc_ratingbar_material 0x7f07003a
int drawable abc_ratingbar_small_material 0x7f07003b
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f07003c
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f07003d
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f07003e
int drawable abc_scrubber_primary_mtrl_alpha 0x7f07003f
int drawable abc_scrubber_track_mtrl_alpha 0x7f070040
int drawable abc_seekbar_thumb_material 0x7f070041
int drawable abc_seekbar_tick_mark_material 0x7f070042
int drawable abc_seekbar_track_material 0x7f070043
int drawable abc_spinner_mtrl_am_alpha 0x7f070044
int drawable abc_spinner_textfield_background_material 0x7f070045
int drawable abc_switch_thumb_material 0x7f070046
int drawable abc_switch_track_mtrl_alpha 0x7f070047
int drawable abc_tab_indicator_material 0x7f070048
int drawable abc_tab_indicator_mtrl_alpha 0x7f070049
int drawable abc_text_cursor_material 0x7f07004a
int drawable abc_text_select_handle_left_mtrl_dark 0x7f07004b
int drawable abc_text_select_handle_left_mtrl_light 0x7f07004c
int drawable abc_text_select_handle_middle_mtrl_dark 0x7f07004d
int drawable abc_text_select_handle_middle_mtrl_light 0x7f07004e
int drawable abc_text_select_handle_right_mtrl_dark 0x7f07004f
int drawable abc_text_select_handle_right_mtrl_light 0x7f070050
int drawable abc_textfield_activated_mtrl_alpha 0x7f070051
int drawable abc_textfield_default_mtrl_alpha 0x7f070052
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f070053
int drawable abc_textfield_search_default_mtrl_alpha 0x7f070054
int drawable abc_textfield_search_material 0x7f070055
int drawable abc_vector_test 0x7f070056
int drawable btn_checkbox_checked_mtrl 0x7f070057
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f070058
int drawable btn_checkbox_unchecked_mtrl 0x7f070059
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f07005a
int drawable btn_radio_off_mtrl 0x7f07005b
int drawable btn_radio_off_to_on_mtrl_animation 0x7f07005c
int drawable btn_radio_on_mtrl 0x7f07005d
int drawable btn_radio_on_to_off_mtrl_animation 0x7f07005e
int drawable fingerprint_dialog_error 0x7f07005f
int drawable fingerprint_dialog_fp_icon 0x7f070060
int drawable ic_call_answer 0x7f070061
int drawable ic_call_answer_low 0x7f070062
int drawable ic_call_answer_video 0x7f070063
int drawable ic_call_answer_video_low 0x7f070064
int drawable ic_call_decline 0x7f070065
int drawable ic_call_decline_low 0x7f070066
int drawable ic_launcher_background 0x7f070067
int drawable ic_launcher_foreground 0x7f070068
int drawable notification_action_background 0x7f070069
int drawable notification_bg 0x7f07006a
int drawable notification_bg_low 0x7f07006b
int drawable notification_bg_low_normal 0x7f07006c
int drawable notification_bg_low_pressed 0x7f07006d
int drawable notification_bg_normal 0x7f07006e
int drawable notification_bg_normal_pressed 0x7f07006f
int drawable notification_icon_background 0x7f070070
int drawable notification_oversize_large_icon_bg 0x7f070071
int drawable notification_template_icon_bg 0x7f070072
int drawable notification_template_icon_low_bg 0x7f070073
int drawable notification_tile_bg 0x7f070074
int drawable notify_panel_notification_icon_bg 0x7f070075
int drawable tooltip_frame_dark 0x7f070076
int drawable tooltip_frame_light 0x7f070077
int id ALT 0x7f080000
int id CTRL 0x7f080001
int id FUNCTION 0x7f080002
int id META 0x7f080003
int id SHIFT 0x7f080004
int id SYM 0x7f080005
int id accessibility_action_clickable_span 0x7f080006
int id accessibility_custom_action_0 0x7f080007
int id accessibility_custom_action_1 0x7f080008
int id accessibility_custom_action_10 0x7f080009
int id accessibility_custom_action_11 0x7f08000a
int id accessibility_custom_action_12 0x7f08000b
int id accessibility_custom_action_13 0x7f08000c
int id accessibility_custom_action_14 0x7f08000d
int id accessibility_custom_action_15 0x7f08000e
int id accessibility_custom_action_16 0x7f08000f
int id accessibility_custom_action_17 0x7f080010
int id accessibility_custom_action_18 0x7f080011
int id accessibility_custom_action_19 0x7f080012
int id accessibility_custom_action_2 0x7f080013
int id accessibility_custom_action_20 0x7f080014
int id accessibility_custom_action_21 0x7f080015
int id accessibility_custom_action_22 0x7f080016
int id accessibility_custom_action_23 0x7f080017
int id accessibility_custom_action_24 0x7f080018
int id accessibility_custom_action_25 0x7f080019
int id accessibility_custom_action_26 0x7f08001a
int id accessibility_custom_action_27 0x7f08001b
int id accessibility_custom_action_28 0x7f08001c
int id accessibility_custom_action_29 0x7f08001d
int id accessibility_custom_action_3 0x7f08001e
int id accessibility_custom_action_30 0x7f08001f
int id accessibility_custom_action_31 0x7f080020
int id accessibility_custom_action_4 0x7f080021
int id accessibility_custom_action_5 0x7f080022
int id accessibility_custom_action_6 0x7f080023
int id accessibility_custom_action_7 0x7f080024
int id accessibility_custom_action_8 0x7f080025
int id accessibility_custom_action_9 0x7f080026
int id action_bar 0x7f080027
int id action_bar_activity_content 0x7f080028
int id action_bar_container 0x7f080029
int id action_bar_root 0x7f08002a
int id action_bar_spinner 0x7f08002b
int id action_bar_subtitle 0x7f08002c
int id action_bar_title 0x7f08002d
int id action_container 0x7f08002e
int id action_context_bar 0x7f08002f
int id action_divider 0x7f080030
int id action_image 0x7f080031
int id action_menu_divider 0x7f080032
int id action_menu_presenter 0x7f080033
int id action_mode_bar 0x7f080034
int id action_mode_bar_stub 0x7f080035
int id action_mode_close_button 0x7f080036
int id action_text 0x7f080037
int id actions 0x7f080038
int id activity_chooser_view_content 0x7f080039
int id add 0x7f08003a
int id alertTitle 0x7f08003b
int id always 0x7f08003c
int id androidx_compose_ui_view_composition_context 0x7f08003d
int id async 0x7f08003e
int id beginning 0x7f08003f
int id blocking 0x7f080040
int id bottom 0x7f080041
int id buttonPanel 0x7f080042
int id center_vertical 0x7f080043
int id checkbox 0x7f080044
int id checked 0x7f080045
int id chronometer 0x7f080046
int id collapseActionView 0x7f080047
int id compose_view_saveable_id_tag 0x7f080048
int id consume_window_insets_tag 0x7f080049
int id content 0x7f08004a
int id contentPanel 0x7f08004b
int id custom 0x7f08004c
int id customPanel 0x7f08004d
int id decor_content_parent 0x7f08004e
int id default_activity_button 0x7f08004f
int id dialog_button 0x7f080050
int id disableHome 0x7f080051
int id edit_query 0x7f080052
int id edit_text_id 0x7f080053
int id end 0x7f080054
int id expand_activities_button 0x7f080055
int id expanded_menu 0x7f080056
int id fingerprint_description 0x7f080057
int id fingerprint_error 0x7f080058
int id fingerprint_icon 0x7f080059
int id fingerprint_subtitle 0x7f08005a
int id forever 0x7f08005b
int id fragment_container_view_tag 0x7f08005c
int id group_divider 0x7f08005d
int id hide_ime_id 0x7f08005e
int id hide_in_inspector_tag 0x7f08005f
int id home 0x7f080060
int id homeAsUp 0x7f080061
int id icon 0x7f080062
int id icon_group 0x7f080063
int id ifRoom 0x7f080064
int id image 0x7f080065
int id info 0x7f080066
int id inspection_slot_table_set 0x7f080067
int id is_pooling_container_tag 0x7f080068
int id italic 0x7f080069
int id line1 0x7f08006a
int id line3 0x7f08006b
int id listMode 0x7f08006c
int id list_item 0x7f08006d
int id message 0x7f08006e
int id middle 0x7f08006f
int id multiply 0x7f080070
int id nav_controller_view_tag 0x7f080071
int id never 0x7f080072
int id none 0x7f080073
int id normal 0x7f080074
int id notification_background 0x7f080075
int id notification_main_column 0x7f080076
int id notification_main_column_container 0x7f080077
int id off 0x7f080078
int id on 0x7f080079
int id parentPanel 0x7f08007a
int id pooling_container_listener_holder_tag 0x7f08007b
int id progress_circular 0x7f08007c
int id progress_horizontal 0x7f08007d
int id radio 0x7f08007e
int id report_drawn 0x7f08007f
int id right_icon 0x7f080080
int id right_side 0x7f080081
int id screen 0x7f080082
int id scrollIndicatorDown 0x7f080083
int id scrollIndicatorUp 0x7f080084
int id scrollView 0x7f080085
int id search_badge 0x7f080086
int id search_bar 0x7f080087
int id search_button 0x7f080088
int id search_close_btn 0x7f080089
int id search_edit_frame 0x7f08008a
int id search_go_btn 0x7f08008b
int id search_mag_icon 0x7f08008c
int id search_plate 0x7f08008d
int id search_src_text 0x7f08008e
int id search_voice_btn 0x7f08008f
int id select_dialog_listview 0x7f080090
int id shortcut 0x7f080091
int id showCustom 0x7f080092
int id showHome 0x7f080093
int id showTitle 0x7f080094
int id spacer 0x7f080095
int id split_action_bar 0x7f080096
int id src_atop 0x7f080097
int id src_in 0x7f080098
int id src_over 0x7f080099
int id submenuarrow 0x7f08009a
int id submit_area 0x7f08009b
int id tabMode 0x7f08009c
int id tag_accessibility_actions 0x7f08009d
int id tag_accessibility_clickable_spans 0x7f08009e
int id tag_accessibility_heading 0x7f08009f
int id tag_accessibility_pane_title 0x7f0800a0
int id tag_on_apply_window_listener 0x7f0800a1
int id tag_on_receive_content_listener 0x7f0800a2
int id tag_on_receive_content_mime_types 0x7f0800a3
int id tag_screen_reader_focusable 0x7f0800a4
int id tag_state_description 0x7f0800a5
int id tag_transition_group 0x7f0800a6
int id tag_unhandled_key_event_manager 0x7f0800a7
int id tag_unhandled_key_listeners 0x7f0800a8
int id tag_window_insets_animation_callback 0x7f0800a9
int id text 0x7f0800aa
int id text2 0x7f0800ab
int id textSpacerNoButtons 0x7f0800ac
int id textSpacerNoTitle 0x7f0800ad
int id time 0x7f0800ae
int id title 0x7f0800af
int id titleDividerNoCustom 0x7f0800b0
int id title_template 0x7f0800b1
int id top 0x7f0800b2
int id topPanel 0x7f0800b3
int id unchecked 0x7f0800b4
int id uniform 0x7f0800b5
int id up 0x7f0800b6
int id useLogo 0x7f0800b7
int id view_tree_lifecycle_owner 0x7f0800b8
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0800b9
int id view_tree_saved_state_registry_owner 0x7f0800ba
int id view_tree_view_model_store_owner 0x7f0800bb
int id visible_removing_fragment_view_tag 0x7f0800bc
int id withText 0x7f0800bd
int id wrap_content 0x7f0800be
int id wrapped_composition_tag 0x7f0800bf
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer cancel_button_image_alpha 0x7f090002
int integer config_tooltipAnimTime 0x7f090003
int integer status_bar_notification_info_maxnum 0x7f090004
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0a0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0a0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0a0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0a0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0a0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0a0005
int interpolator fast_out_slow_in 0x7f0a0006
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout custom_dialog 0x7f0b001c
int layout fingerprint_dialog_layout 0x7f0b001d
int layout ime_base_split_test_activity 0x7f0b001e
int layout ime_secondary_split_test_activity 0x7f0b001f
int layout notification_action 0x7f0b0020
int layout notification_action_tombstone 0x7f0b0021
int layout notification_template_custom_big 0x7f0b0022
int layout notification_template_icon_group 0x7f0b0023
int layout notification_template_part_chronometer 0x7f0b0024
int layout notification_template_part_time 0x7f0b0025
int layout select_dialog_item_material 0x7f0b0026
int layout select_dialog_multichoice_material 0x7f0b0027
int layout select_dialog_singlechoice_material 0x7f0b0028
int layout support_simple_spinner_dropdown_item 0x7f0b0029
int mipmap ic_launcher 0x7f0c0000
int mipmap ic_launcher_foreground 0x7f0c0001
int mipmap ic_launcher_round 0x7f0c0002
int string abc_action_bar_home_description 0x7f0d0000
int string abc_action_bar_up_description 0x7f0d0001
int string abc_action_menu_overflow_description 0x7f0d0002
int string abc_action_mode_done 0x7f0d0003
int string abc_activity_chooser_view_see_all 0x7f0d0004
int string abc_activitychooserview_choose_application 0x7f0d0005
int string abc_capital_off 0x7f0d0006
int string abc_capital_on 0x7f0d0007
int string abc_menu_alt_shortcut_label 0x7f0d0008
int string abc_menu_ctrl_shortcut_label 0x7f0d0009
int string abc_menu_delete_shortcut_label 0x7f0d000a
int string abc_menu_enter_shortcut_label 0x7f0d000b
int string abc_menu_function_shortcut_label 0x7f0d000c
int string abc_menu_meta_shortcut_label 0x7f0d000d
int string abc_menu_shift_shortcut_label 0x7f0d000e
int string abc_menu_space_shortcut_label 0x7f0d000f
int string abc_menu_sym_shortcut_label 0x7f0d0010
int string abc_prepend_shortcut_label 0x7f0d0011
int string abc_search_hint 0x7f0d0012
int string abc_searchview_description_clear 0x7f0d0013
int string abc_searchview_description_query 0x7f0d0014
int string abc_searchview_description_search 0x7f0d0015
int string abc_searchview_description_submit 0x7f0d0016
int string abc_searchview_description_voice 0x7f0d0017
int string abc_shareactionprovider_share_with 0x7f0d0018
int string abc_shareactionprovider_share_with_application 0x7f0d0019
int string abc_toolbar_collapse_description 0x7f0d001a
int string androidx_startup 0x7f0d001b
int string app_name 0x7f0d001c
int string bottom_sheet_collapse_description 0x7f0d001d
int string bottom_sheet_dismiss_description 0x7f0d001e
int string bottom_sheet_drag_handle_description 0x7f0d001f
int string bottom_sheet_expand_description 0x7f0d0020
int string call_notification_answer_action 0x7f0d0021
int string call_notification_answer_video_action 0x7f0d0022
int string call_notification_decline_action 0x7f0d0023
int string call_notification_hang_up_action 0x7f0d0024
int string call_notification_incoming_text 0x7f0d0025
int string call_notification_ongoing_text 0x7f0d0026
int string call_notification_screening_text 0x7f0d0027
int string close_drawer 0x7f0d0028
int string close_sheet 0x7f0d0029
int string collapsed 0x7f0d002a
int string confirm_device_credential_password 0x7f0d002b
int string date_input_headline 0x7f0d002c
int string date_input_headline_description 0x7f0d002d
int string date_input_invalid_for_pattern 0x7f0d002e
int string date_input_invalid_not_allowed 0x7f0d002f
int string date_input_invalid_year_range 0x7f0d0030
int string date_input_label 0x7f0d0031
int string date_input_no_input_description 0x7f0d0032
int string date_input_title 0x7f0d0033
int string date_picker_headline 0x7f0d0034
int string date_picker_headline_description 0x7f0d0035
int string date_picker_navigate_to_year_description 0x7f0d0036
int string date_picker_no_selection_description 0x7f0d0037
int string date_picker_scroll_to_earlier_years 0x7f0d0038
int string date_picker_scroll_to_later_years 0x7f0d0039
int string date_picker_switch_to_calendar_mode 0x7f0d003a
int string date_picker_switch_to_day_selection 0x7f0d003b
int string date_picker_switch_to_input_mode 0x7f0d003c
int string date_picker_switch_to_next_month 0x7f0d003d
int string date_picker_switch_to_previous_month 0x7f0d003e
int string date_picker_switch_to_year_selection 0x7f0d003f
int string date_picker_title 0x7f0d0040
int string date_picker_today_description 0x7f0d0041
int string date_picker_year_picker_pane_title 0x7f0d0042
int string date_range_input_invalid_range_input 0x7f0d0043
int string date_range_input_title 0x7f0d0044
int string date_range_picker_day_in_range 0x7f0d0045
int string date_range_picker_end_headline 0x7f0d0046
int string date_range_picker_scroll_to_next_month 0x7f0d0047
int string date_range_picker_scroll_to_previous_month 0x7f0d0048
int string date_range_picker_start_headline 0x7f0d0049
int string date_range_picker_title 0x7f0d004a
int string default_error_message 0x7f0d004b
int string default_error_msg 0x7f0d004c
int string default_popup_window_title 0x7f0d004d
int string dialog 0x7f0d004e
int string dropdown_menu 0x7f0d004f
int string expanded 0x7f0d0050
int string fingerprint_dialog_touch_sensor 0x7f0d0051
int string fingerprint_error_hw_not_available 0x7f0d0052
int string fingerprint_error_hw_not_present 0x7f0d0053
int string fingerprint_error_lockout 0x7f0d0054
int string fingerprint_error_no_fingerprints 0x7f0d0055
int string fingerprint_error_user_canceled 0x7f0d0056
int string fingerprint_not_recognized 0x7f0d0057
int string generic_error_no_device_credential 0x7f0d0058
int string generic_error_no_keyguard 0x7f0d0059
int string generic_error_user_canceled 0x7f0d005a
int string in_progress 0x7f0d005b
int string indeterminate 0x7f0d005c
int string m3c_bottom_sheet_pane_title 0x7f0d005d
int string navigation_menu 0x7f0d005e
int string not_selected 0x7f0d005f
int string off 0x7f0d0060
int string on 0x7f0d0061
int string range_end 0x7f0d0062
int string range_start 0x7f0d0063
int string search_bar_search 0x7f0d0064
int string search_menu_title 0x7f0d0065
int string selected 0x7f0d0066
int string snackbar_dismiss 0x7f0d0067
int string status_bar_notification_info_overflow 0x7f0d0068
int string suggestions_available 0x7f0d0069
int string switch_role 0x7f0d006a
int string tab 0x7f0d006b
int string template_percent 0x7f0d006c
int string time_picker_am 0x7f0d006d
int string time_picker_hour 0x7f0d006e
int string time_picker_hour_24h_suffix 0x7f0d006f
int string time_picker_hour_selection 0x7f0d0070
int string time_picker_hour_suffix 0x7f0d0071
int string time_picker_hour_text_field 0x7f0d0072
int string time_picker_minute 0x7f0d0073
int string time_picker_minute_selection 0x7f0d0074
int string time_picker_minute_suffix 0x7f0d0075
int string time_picker_minute_text_field 0x7f0d0076
int string time_picker_period_toggle_description 0x7f0d0077
int string time_picker_pm 0x7f0d0078
int string tooltip_long_press_label 0x7f0d0079
int string tooltip_pane_description 0x7f0d007a
int style AlertDialog_AppCompat 0x7f0e0000
int style AlertDialog_AppCompat_Light 0x7f0e0001
int style Animation_AppCompat_Dialog 0x7f0e0002
int style Animation_AppCompat_DropDownUp 0x7f0e0003
int style Animation_AppCompat_Tooltip 0x7f0e0004
int style Base_AlertDialog_AppCompat 0x7f0e0005
int style Base_AlertDialog_AppCompat_Light 0x7f0e0006
int style Base_Animation_AppCompat_Dialog 0x7f0e0007
int style Base_Animation_AppCompat_DropDownUp 0x7f0e0008
int style Base_Animation_AppCompat_Tooltip 0x7f0e0009
int style Base_DialogWindowTitle_AppCompat 0x7f0e000a
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0e000b
int style Base_TextAppearance_AppCompat 0x7f0e000c
int style Base_TextAppearance_AppCompat_Body1 0x7f0e000d
int style Base_TextAppearance_AppCompat_Body2 0x7f0e000e
int style Base_TextAppearance_AppCompat_Button 0x7f0e000f
int style Base_TextAppearance_AppCompat_Caption 0x7f0e0010
int style Base_TextAppearance_AppCompat_Display1 0x7f0e0011
int style Base_TextAppearance_AppCompat_Display2 0x7f0e0012
int style Base_TextAppearance_AppCompat_Display3 0x7f0e0013
int style Base_TextAppearance_AppCompat_Display4 0x7f0e0014
int style Base_TextAppearance_AppCompat_Headline 0x7f0e0015
int style Base_TextAppearance_AppCompat_Inverse 0x7f0e0016
int style Base_TextAppearance_AppCompat_Large 0x7f0e0017
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0e0018
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0e0019
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0e001a
int style Base_TextAppearance_AppCompat_Medium 0x7f0e001b
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0e001c
int style Base_TextAppearance_AppCompat_Menu 0x7f0e001d
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0e001e
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0e001f
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0e0020
int style Base_TextAppearance_AppCompat_Small 0x7f0e0021
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0e0022
int style Base_TextAppearance_AppCompat_Subhead 0x7f0e0023
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0e0024
int style Base_TextAppearance_AppCompat_Title 0x7f0e0025
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0e0026
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0e0027
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0e0028
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0e0029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0e002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0e002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0e002c
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0e002d
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0e002e
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0e002f
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0e0030
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0e0031
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0e0032
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0e0033
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0e0034
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0e0035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0e0036
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0e0037
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0e0038
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0e0039
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0e003a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0e003b
int style Base_Theme_AppCompat 0x7f0e003c
int style Base_Theme_AppCompat_CompactMenu 0x7f0e003d
int style Base_Theme_AppCompat_Dialog 0x7f0e003e
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0e003f
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0e0040
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0e0041
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0e0042
int style Base_Theme_AppCompat_Light 0x7f0e0043
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0e0044
int style Base_Theme_AppCompat_Light_Dialog 0x7f0e0045
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0e0046
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0e0047
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0e0048
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0e0049
int style Base_ThemeOverlay_AppCompat 0x7f0e004a
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0e004b
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0e004c
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0e004d
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0e004e
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0e004f
int style Base_ThemeOverlay_AppCompat_Light 0x7f0e0050
int style Base_V21_Theme_AppCompat 0x7f0e0051
int style Base_V21_Theme_AppCompat_Dialog 0x7f0e0052
int style Base_V21_Theme_AppCompat_Light 0x7f0e0053
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0e0054
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0e0055
int style Base_V22_Theme_AppCompat 0x7f0e0056
int style Base_V22_Theme_AppCompat_Light 0x7f0e0057
int style Base_V23_Theme_AppCompat 0x7f0e0058
int style Base_V23_Theme_AppCompat_Light 0x7f0e0059
int style Base_V26_Theme_AppCompat 0x7f0e005a
int style Base_V26_Theme_AppCompat_Light 0x7f0e005b
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0e005c
int style Base_V28_Theme_AppCompat 0x7f0e005d
int style Base_V28_Theme_AppCompat_Light 0x7f0e005e
int style Base_V7_Theme_AppCompat 0x7f0e005f
int style Base_V7_Theme_AppCompat_Dialog 0x7f0e0060
int style Base_V7_Theme_AppCompat_Light 0x7f0e0061
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0e0062
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0e0063
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0e0064
int style Base_V7_Widget_AppCompat_EditText 0x7f0e0065
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0e0066
int style Base_Widget_AppCompat_ActionBar 0x7f0e0067
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0e0068
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0e0069
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0e006a
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0e006b
int style Base_Widget_AppCompat_ActionButton 0x7f0e006c
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0e006d
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0e006e
int style Base_Widget_AppCompat_ActionMode 0x7f0e006f
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0e0070
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0e0071
int style Base_Widget_AppCompat_Button 0x7f0e0072
int style Base_Widget_AppCompat_Button_Borderless 0x7f0e0073
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0e0074
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0e0075
int style Base_Widget_AppCompat_Button_Colored 0x7f0e0076
int style Base_Widget_AppCompat_Button_Small 0x7f0e0077
int style Base_Widget_AppCompat_ButtonBar 0x7f0e0078
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0e0079
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0e007a
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0e007b
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0e007c
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0e007d
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0e007e
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0e007f
int style Base_Widget_AppCompat_EditText 0x7f0e0080
int style Base_Widget_AppCompat_ImageButton 0x7f0e0081
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0e0082
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0e0083
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0e0084
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0e0085
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0e0086
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0e0087
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0e0088
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0e0089
int style Base_Widget_AppCompat_ListMenuView 0x7f0e008a
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0e008b
int style Base_Widget_AppCompat_ListView 0x7f0e008c
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0e008d
int style Base_Widget_AppCompat_ListView_Menu 0x7f0e008e
int style Base_Widget_AppCompat_PopupMenu 0x7f0e008f
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0e0090
int style Base_Widget_AppCompat_PopupWindow 0x7f0e0091
int style Base_Widget_AppCompat_ProgressBar 0x7f0e0092
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0e0093
int style Base_Widget_AppCompat_RatingBar 0x7f0e0094
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0e0095
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0e0096
int style Base_Widget_AppCompat_SearchView 0x7f0e0097
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0e0098
int style Base_Widget_AppCompat_SeekBar 0x7f0e0099
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0e009a
int style Base_Widget_AppCompat_Spinner 0x7f0e009b
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0e009c
int style Base_Widget_AppCompat_TextView 0x7f0e009d
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0e009e
int style Base_Widget_AppCompat_Toolbar 0x7f0e009f
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0e00a0
int style DialogWindowTheme 0x7f0e00a1
int style FloatingDialogTheme 0x7f0e00a2
int style FloatingDialogWindowTheme 0x7f0e00a3
int style Platform_AppCompat 0x7f0e00a4
int style Platform_AppCompat_Light 0x7f0e00a5
int style Platform_ThemeOverlay_AppCompat 0x7f0e00a6
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0e00a7
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0e00a8
int style Platform_V21_AppCompat 0x7f0e00a9
int style Platform_V21_AppCompat_Light 0x7f0e00aa
int style Platform_V25_AppCompat 0x7f0e00ab
int style Platform_V25_AppCompat_Light 0x7f0e00ac
int style Platform_Widget_AppCompat_Spinner 0x7f0e00ad
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0e00ae
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0e00af
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0e00b0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0e00b1
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0e00b2
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0e00b3
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0e00b4
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0e00b5
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0e00b6
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0e00b7
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0e00b8
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0e00b9
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0e00ba
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0e00bb
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0e00bc
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0e00bd
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0e00be
int style TextAppearance_AppCompat 0x7f0e00bf
int style TextAppearance_AppCompat_Body1 0x7f0e00c0
int style TextAppearance_AppCompat_Body2 0x7f0e00c1
int style TextAppearance_AppCompat_Button 0x7f0e00c2
int style TextAppearance_AppCompat_Caption 0x7f0e00c3
int style TextAppearance_AppCompat_Display1 0x7f0e00c4
int style TextAppearance_AppCompat_Display2 0x7f0e00c5
int style TextAppearance_AppCompat_Display3 0x7f0e00c6
int style TextAppearance_AppCompat_Display4 0x7f0e00c7
int style TextAppearance_AppCompat_Headline 0x7f0e00c8
int style TextAppearance_AppCompat_Inverse 0x7f0e00c9
int style TextAppearance_AppCompat_Large 0x7f0e00ca
int style TextAppearance_AppCompat_Large_Inverse 0x7f0e00cb
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0e00cc
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0e00cd
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0e00ce
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0e00cf
int style TextAppearance_AppCompat_Medium 0x7f0e00d0
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0e00d1
int style TextAppearance_AppCompat_Menu 0x7f0e00d2
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0e00d3
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0e00d4
int style TextAppearance_AppCompat_Small 0x7f0e00d5
int style TextAppearance_AppCompat_Small_Inverse 0x7f0e00d6
int style TextAppearance_AppCompat_Subhead 0x7f0e00d7
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0e00d8
int style TextAppearance_AppCompat_Title 0x7f0e00d9
int style TextAppearance_AppCompat_Title_Inverse 0x7f0e00da
int style TextAppearance_AppCompat_Tooltip 0x7f0e00db
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0e00dc
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0e00dd
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0e00de
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0e00df
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0e00e0
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0e00e1
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0e00e2
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0e00e3
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0e00e4
int style TextAppearance_AppCompat_Widget_Button 0x7f0e00e5
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0e00e6
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0e00e7
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0e00e8
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0e00e9
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0e00ea
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0e00eb
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0e00ec
int style TextAppearance_AppCompat_Widget_Switch 0x7f0e00ed
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0e00ee
int style TextAppearance_Compat_Notification 0x7f0e00ef
int style TextAppearance_Compat_Notification_Info 0x7f0e00f0
int style TextAppearance_Compat_Notification_Line2 0x7f0e00f1
int style TextAppearance_Compat_Notification_Time 0x7f0e00f2
int style TextAppearance_Compat_Notification_Title 0x7f0e00f3
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0e00f4
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0e00f5
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0e00f6
int style Theme_AppCompat 0x7f0e00f7
int style Theme_AppCompat_CompactMenu 0x7f0e00f8
int style Theme_AppCompat_DayNight 0x7f0e00f9
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0e00fa
int style Theme_AppCompat_DayNight_Dialog 0x7f0e00fb
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0e00fc
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0e00fd
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0e00fe
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0e00ff
int style Theme_AppCompat_Dialog 0x7f0e0100
int style Theme_AppCompat_Dialog_Alert 0x7f0e0101
int style Theme_AppCompat_Dialog_MinWidth 0x7f0e0102
int style Theme_AppCompat_DialogWhenLarge 0x7f0e0103
int style Theme_AppCompat_Empty 0x7f0e0104
int style Theme_AppCompat_Light 0x7f0e0105
int style Theme_AppCompat_Light_DarkActionBar 0x7f0e0106
int style Theme_AppCompat_Light_Dialog 0x7f0e0107
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0e0108
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0e0109
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0e010a
int style Theme_AppCompat_Light_NoActionBar 0x7f0e010b
int style Theme_AppCompat_NoActionBar 0x7f0e010c
int style Theme_WordifyNumbers 0x7f0e010d
int style Theme_WordifyNumbers_Dark 0x7f0e010e
int style ThemeOverlay_AppCompat 0x7f0e010f
int style ThemeOverlay_AppCompat_ActionBar 0x7f0e0110
int style ThemeOverlay_AppCompat_Dark 0x7f0e0111
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0e0112
int style ThemeOverlay_AppCompat_DayNight 0x7f0e0113
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f0e0114
int style ThemeOverlay_AppCompat_Dialog 0x7f0e0115
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0e0116
int style ThemeOverlay_AppCompat_Light 0x7f0e0117
int style Widget_AppCompat_ActionBar 0x7f0e0118
int style Widget_AppCompat_ActionBar_Solid 0x7f0e0119
int style Widget_AppCompat_ActionBar_TabBar 0x7f0e011a
int style Widget_AppCompat_ActionBar_TabText 0x7f0e011b
int style Widget_AppCompat_ActionBar_TabView 0x7f0e011c
int style Widget_AppCompat_ActionButton 0x7f0e011d
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0e011e
int style Widget_AppCompat_ActionButton_Overflow 0x7f0e011f
int style Widget_AppCompat_ActionMode 0x7f0e0120
int style Widget_AppCompat_ActivityChooserView 0x7f0e0121
int style Widget_AppCompat_AutoCompleteTextView 0x7f0e0122
int style Widget_AppCompat_Button 0x7f0e0123
int style Widget_AppCompat_Button_Borderless 0x7f0e0124
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0e0125
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0e0126
int style Widget_AppCompat_Button_Colored 0x7f0e0127
int style Widget_AppCompat_Button_Small 0x7f0e0128
int style Widget_AppCompat_ButtonBar 0x7f0e0129
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0e012a
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0e012b
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0e012c
int style Widget_AppCompat_CompoundButton_Switch 0x7f0e012d
int style Widget_AppCompat_DrawerArrowToggle 0x7f0e012e
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0e012f
int style Widget_AppCompat_EditText 0x7f0e0130
int style Widget_AppCompat_ImageButton 0x7f0e0131
int style Widget_AppCompat_Light_ActionBar 0x7f0e0132
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0e0133
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0e0134
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0e0135
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0e0136
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0e0137
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0e0138
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0e0139
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0e013a
int style Widget_AppCompat_Light_ActionButton 0x7f0e013b
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0e013c
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0e013d
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0e013e
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0e013f
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0e0140
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0e0141
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0e0142
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0e0143
int style Widget_AppCompat_Light_PopupMenu 0x7f0e0144
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0e0145
int style Widget_AppCompat_Light_SearchView 0x7f0e0146
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0e0147
int style Widget_AppCompat_ListMenuView 0x7f0e0148
int style Widget_AppCompat_ListPopupWindow 0x7f0e0149
int style Widget_AppCompat_ListView 0x7f0e014a
int style Widget_AppCompat_ListView_DropDown 0x7f0e014b
int style Widget_AppCompat_ListView_Menu 0x7f0e014c
int style Widget_AppCompat_PopupMenu 0x7f0e014d
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0e014e
int style Widget_AppCompat_PopupWindow 0x7f0e014f
int style Widget_AppCompat_ProgressBar 0x7f0e0150
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0e0151
int style Widget_AppCompat_RatingBar 0x7f0e0152
int style Widget_AppCompat_RatingBar_Indicator 0x7f0e0153
int style Widget_AppCompat_RatingBar_Small 0x7f0e0154
int style Widget_AppCompat_SearchView 0x7f0e0155
int style Widget_AppCompat_SearchView_ActionBar 0x7f0e0156
int style Widget_AppCompat_SeekBar 0x7f0e0157
int style Widget_AppCompat_SeekBar_Discrete 0x7f0e0158
int style Widget_AppCompat_Spinner 0x7f0e0159
int style Widget_AppCompat_Spinner_DropDown 0x7f0e015a
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0e015b
int style Widget_AppCompat_Spinner_Underlined 0x7f0e015c
int style Widget_AppCompat_TextView 0x7f0e015d
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0e015e
int style Widget_AppCompat_Toolbar 0x7f0e015f
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0e0160
int style Widget_Compat_NotificationActionContainer 0x7f0e0161
int style Widget_Compat_NotificationActionText 0x7f0e0162
int[] styleable ActionBar { 0x7f030033, 0x7f030034, 0x7f030035, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f030061, 0x7f030069, 0x7f03006a, 0x7f03007d, 0x7f030091, 0x7f030092, 0x7f030093, 0x7f030094, 0x7f030095, 0x7f03009a, 0x7f03009d, 0x7f0300b2, 0x7f0300bc, 0x7f0300ce, 0x7f0300d1, 0x7f0300d2, 0x7f0300f1, 0x7f0300f4, 0x7f030111, 0x7f03011a }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f030033, 0x7f030034, 0x7f03004a, 0x7f030091, 0x7f0300f4, 0x7f03011a }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f030080, 0x7f03009b }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f030000, 0x7f030062, 0x7f030063, 0x7f0300fa }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable AlertDialog { 0x010100f2, 0x7f030041, 0x7f030042, 0x7f0300a7, 0x7f0300a8, 0x7f0300b8, 0x7f0300e6, 0x7f0300e7 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatImageView { 0x01010119, 0x7f0300ec, 0x7f03010f, 0x7f030110 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f03010c, 0x7f03010d, 0x7f03010e }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f03002e, 0x7f03002f, 0x7f030030, 0x7f030031, 0x7f030032, 0x7f03006e, 0x7f03006f, 0x7f030070, 0x7f030071, 0x7f030073, 0x7f030074, 0x7f030075, 0x7f030076, 0x7f030081, 0x7f030083, 0x7f03008c, 0x7f03009f, 0x7f0300a2, 0x7f0300fb, 0x7f030106 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_firstBaselineToTopHeight 14
int styleable AppCompatTextView_fontFamily 15
int styleable AppCompatTextView_fontVariationSettings 16
int styleable AppCompatTextView_lastBaselineToBottomHeight 17
int styleable AppCompatTextView_lineHeight 18
int styleable AppCompatTextView_textAllCaps 19
int styleable AppCompatTextView_textLocale 20
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030001, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000f, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030022, 0x7f030023, 0x7f030024, 0x7f030025, 0x7f030026, 0x7f03002d, 0x7f030039, 0x7f03003a, 0x7f03003b, 0x7f03003c, 0x7f03003d, 0x7f03003e, 0x7f030043, 0x7f030044, 0x7f030047, 0x7f030048, 0x7f03004e, 0x7f03004f, 0x7f030050, 0x7f030051, 0x7f030052, 0x7f030053, 0x7f030054, 0x7f030055, 0x7f030056, 0x7f030057, 0x7f030060, 0x7f030066, 0x7f030067, 0x7f030068, 0x7f03006b, 0x7f03006d, 0x7f030078, 0x7f030079, 0x7f03007a, 0x7f03007b, 0x7f03007c, 0x7f030093, 0x7f030099, 0x7f0300a3, 0x7f0300a4, 0x7f0300a5, 0x7f0300a6, 0x7f0300a9, 0x7f0300aa, 0x7f0300ab, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f0300af, 0x7f0300b0, 0x7f0300b1, 0x7f0300c5, 0x7f0300c6, 0x7f0300c7, 0x7f0300cd, 0x7f0300cf, 0x7f0300d6, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300de, 0x7f0300df, 0x7f0300e0, 0x7f0300e1, 0x7f0300e9, 0x7f0300ea, 0x7f0300f8, 0x7f0300fc, 0x7f0300fd, 0x7f0300fe, 0x7f0300ff, 0x7f030100, 0x7f030101, 0x7f030102, 0x7f030103, 0x7f030104, 0x7f030105, 0x7f03011b, 0x7f03011c, 0x7f03011d, 0x7f03011e, 0x7f030125, 0x7f030127, 0x7f030128, 0x7f030129, 0x7f03012a, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseDrawable 19
int styleable AppCompatTheme_actionModeCopyDrawable 20
int styleable AppCompatTheme_actionModeCutDrawable 21
int styleable AppCompatTheme_actionModeFindDrawable 22
int styleable AppCompatTheme_actionModePasteDrawable 23
int styleable AppCompatTheme_actionModePopupWindowStyle 24
int styleable AppCompatTheme_actionModeSelectAllDrawable 25
int styleable AppCompatTheme_actionModeShareDrawable 26
int styleable AppCompatTheme_actionModeSplitBackground 27
int styleable AppCompatTheme_actionModeStyle 28
int styleable AppCompatTheme_actionModeWebSearchDrawable 29
int styleable AppCompatTheme_actionOverflowButtonStyle 30
int styleable AppCompatTheme_actionOverflowMenuStyle 31
int styleable AppCompatTheme_activityChooserViewStyle 32
int styleable AppCompatTheme_alertDialogButtonGroupStyle 33
int styleable AppCompatTheme_alertDialogCenterButtons 34
int styleable AppCompatTheme_alertDialogStyle 35
int styleable AppCompatTheme_alertDialogTheme 36
int styleable AppCompatTheme_autoCompleteTextViewStyle 37
int styleable AppCompatTheme_borderlessButtonStyle 38
int styleable AppCompatTheme_buttonBarButtonStyle 39
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 40
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 41
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 42
int styleable AppCompatTheme_buttonBarStyle 43
int styleable AppCompatTheme_buttonStyle 44
int styleable AppCompatTheme_buttonStyleSmall 45
int styleable AppCompatTheme_checkboxStyle 46
int styleable AppCompatTheme_checkedTextViewStyle 47
int styleable AppCompatTheme_colorAccent 48
int styleable AppCompatTheme_colorBackgroundFloating 49
int styleable AppCompatTheme_colorButtonNormal 50
int styleable AppCompatTheme_colorControlActivated 51
int styleable AppCompatTheme_colorControlHighlight 52
int styleable AppCompatTheme_colorControlNormal 53
int styleable AppCompatTheme_colorError 54
int styleable AppCompatTheme_colorPrimary 55
int styleable AppCompatTheme_colorPrimaryDark 56
int styleable AppCompatTheme_colorSwitchThumbNormal 57
int styleable AppCompatTheme_controlBackground 58
int styleable AppCompatTheme_dialogCornerRadius 59
int styleable AppCompatTheme_dialogPreferredPadding 60
int styleable AppCompatTheme_dialogTheme 61
int styleable AppCompatTheme_dividerHorizontal 62
int styleable AppCompatTheme_dividerVertical 63
int styleable AppCompatTheme_dropDownListViewStyle 64
int styleable AppCompatTheme_dropdownListPreferredItemHeight 65
int styleable AppCompatTheme_editTextBackground 66
int styleable AppCompatTheme_editTextColor 67
int styleable AppCompatTheme_editTextStyle 68
int styleable AppCompatTheme_homeAsUpIndicator 69
int styleable AppCompatTheme_imageButtonStyle 70
int styleable AppCompatTheme_listChoiceBackgroundIndicator 71
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 72
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 73
int styleable AppCompatTheme_listDividerAlertDialog 74
int styleable AppCompatTheme_listMenuViewStyle 75
int styleable AppCompatTheme_listPopupWindowStyle 76
int styleable AppCompatTheme_listPreferredItemHeight 77
int styleable AppCompatTheme_listPreferredItemHeightLarge 78
int styleable AppCompatTheme_listPreferredItemHeightSmall 79
int styleable AppCompatTheme_listPreferredItemPaddingEnd 80
int styleable AppCompatTheme_listPreferredItemPaddingLeft 81
int styleable AppCompatTheme_listPreferredItemPaddingRight 82
int styleable AppCompatTheme_listPreferredItemPaddingStart 83
int styleable AppCompatTheme_panelBackground 84
int styleable AppCompatTheme_panelMenuListTheme 85
int styleable AppCompatTheme_panelMenuListWidth 86
int styleable AppCompatTheme_popupMenuStyle 87
int styleable AppCompatTheme_popupWindowStyle 88
int styleable AppCompatTheme_radioButtonStyle 89
int styleable AppCompatTheme_ratingBarStyle 90
int styleable AppCompatTheme_ratingBarStyleIndicator 91
int styleable AppCompatTheme_ratingBarStyleSmall 92
int styleable AppCompatTheme_searchViewStyle 93
int styleable AppCompatTheme_seekBarStyle 94
int styleable AppCompatTheme_selectableItemBackground 95
int styleable AppCompatTheme_selectableItemBackgroundBorderless 96
int styleable AppCompatTheme_spinnerDropDownItemStyle 97
int styleable AppCompatTheme_spinnerStyle 98
int styleable AppCompatTheme_switchStyle 99
int styleable AppCompatTheme_textAppearanceLargePopupMenu 100
int styleable AppCompatTheme_textAppearanceListItem 101
int styleable AppCompatTheme_textAppearanceListItemSecondary 102
int styleable AppCompatTheme_textAppearanceListItemSmall 103
int styleable AppCompatTheme_textAppearancePopupMenuHeader 104
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 105
int styleable AppCompatTheme_textAppearanceSearchResultTitle 106
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 107
int styleable AppCompatTheme_textColorAlertDialogListItem 108
int styleable AppCompatTheme_textColorSearchUrl 109
int styleable AppCompatTheme_toolbarNavigationButtonStyle 110
int styleable AppCompatTheme_toolbarStyle 111
int styleable AppCompatTheme_tooltipForegroundColor 112
int styleable AppCompatTheme_tooltipFrameBackground 113
int styleable AppCompatTheme_viewInflaterClass 114
int styleable AppCompatTheme_windowActionBar 115
int styleable AppCompatTheme_windowActionBarOverlay 116
int styleable AppCompatTheme_windowActionModeOverlay 117
int styleable AppCompatTheme_windowFixedHeightMajor 118
int styleable AppCompatTheme_windowFixedHeightMinor 119
int styleable AppCompatTheme_windowFixedWidthMajor 120
int styleable AppCompatTheme_windowFixedWidthMinor 121
int styleable AppCompatTheme_windowMinWidthMajor 122
int styleable AppCompatTheme_windowMinWidthMinor 123
int styleable AppCompatTheme_windowNoTitle 124
int[] styleable ButtonBarLayout { 0x7f030027 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f0300d5, 0x7f0300e2 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f030028, 0x7f03009e }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f03003f, 0x7f030045, 0x7f030046 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable DrawerArrowToggle { 0x7f03002b, 0x7f03002c, 0x7f030038, 0x7f03004d, 0x7f030072, 0x7f03008e, 0x7f0300e8, 0x7f030108 }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f030084, 0x7f030085, 0x7f030086, 0x7f030087, 0x7f030088, 0x7f030089, 0x7f03008a }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f030082, 0x7f03008b, 0x7f03008c, 0x7f03008d, 0x7f030123 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f03006a, 0x7f03006c, 0x7f0300b5, 0x7f0300e4 }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000e, 0x7f030020, 0x7f030021, 0x7f030029, 0x7f030059, 0x7f030096, 0x7f030097, 0x7f0300bf, 0x7f0300e3, 0x7f03011f }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0300d0, 0x7f0300ef }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable NavAction { 0x010100d0, 0x7f030065, 0x7f03007e, 0x7f03007f, 0x7f0300a0, 0x7f0300c8, 0x7f0300c9, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300da }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f03002a, 0x7f0300be }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f030000, 0x7f0300b7, 0x7f030124 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f0300ed }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f0300b9 }
int styleable NavHost_navGraph 0
int[] styleable NavInclude { 0x7f030090 }
int styleable NavInclude_graph 0
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f0300db }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f0300c0 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0300ee }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable RecycleListView { 0x7f0300c1, 0x7f0300c4 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f030049, 0x7f030058, 0x7f030064, 0x7f03008f, 0x7f030098, 0x7f0300a1, 0x7f0300d3, 0x7f0300d4, 0x7f0300dc, 0x7f0300dd, 0x7f0300f0, 0x7f0300f5, 0x7f030126 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0300ce }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f0300e5, 0x7f0300eb, 0x7f0300f6, 0x7f0300f7, 0x7f0300f9, 0x7f030109, 0x7f03010a, 0x7f03010b, 0x7f030120, 0x7f030121, 0x7f030122 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f030083, 0x7f03008c, 0x7f0300fb, 0x7f030106 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f030040, 0x7f03004b, 0x7f03004c, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f0300b2, 0x7f0300b3, 0x7f0300b4, 0x7f0300b6, 0x7f0300ba, 0x7f0300bb, 0x7f0300ce, 0x7f0300f1, 0x7f0300f2, 0x7f0300f3, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030118, 0x7f030119 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable View { 0x01010000, 0x010100da, 0x7f0300c2, 0x7f0300c3, 0x7f030107 }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f030036, 0x7f030037 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int xml backup_rules 0x7f100000
int xml data_extraction_rules 0x7f100001
int xml network_security_config 0x7f100002
