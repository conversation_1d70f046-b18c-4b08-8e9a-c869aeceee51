package com.app.wordifynumbers.util

import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

/**
 * Utility class for formatting values
 */
object FormatUtils {
    /**
     * Format a currency amount with the appropriate currency symbol
     */
    fun formatCurrency(amount: Double): String {
        val format = NumberFormat.getCurrencyInstance(Locale.US)
        return format.format(amount)
    }

    /**
     * Format a timestamp as a readable date
     */
    fun formatDate(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        return dateFormat.format(Date(timestamp))
    }
}
