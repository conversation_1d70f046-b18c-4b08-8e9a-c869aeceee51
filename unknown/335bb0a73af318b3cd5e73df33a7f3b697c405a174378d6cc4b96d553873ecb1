package com.app.wordifynumbers.util

import kotlin.math.*

object ScientificCalculator {
    // Mathematical constants
    const val E = Math.E
    const val PI = Math.PI
    const val PHI = 1.618033988749895 // Golden ratio
    
    // Physical constants
    const val SPEED_OF_LIGHT = 299792458.0 // m/s
    const val GRAVITATIONAL_CONSTANT = 6.67430e-11 // m³/kg·s²
    const val PLANCKS_CONSTANT = 6.62607015e-34 // J·s
    const val ELEMENTARY_CHARGE = 1.602176634e-19 // C
    
    fun factorial(n: Int): Double {
        if (n < 0) throw IllegalArgumentException("Factorial not defined for negative numbers")
        if (n > 170) throw IllegalArgumentException("Factorial too large to compute")
        var result = 1.0
        for (i in 2..n) result *= i
        return result
    }
    
    fun combination(n: Int, r: Int): Double {
        if (n < r) throw IllegalArgumentException("n must be greater than or equal to r")
        return factorial(n) / (factorial(r) * factorial(n - r))
    }
    
    fun permutation(n: Int, r: Int): Double {
        if (n < r) throw IllegalArgumentException("n must be greater than or equal to r")
        return factorial(n) / factorial(n - r)
    }
    
    fun log(base: Double, x: Double): Double {
        if (x <= 0 || base <= 0 || base == 1.0) 
            throw IllegalArgumentException("Invalid logarithm arguments")
        return ln(x) / ln(base)
    }
    
    fun nthRoot(n: Int, x: Double): Double {
        if (n == 0) throw IllegalArgumentException("Zero-th root is undefined")
        if (n % 2 == 0 && x < 0) throw IllegalArgumentException("Even root of negative number")
        return x.pow(1.0 / n)
    }
    
    fun isPrime(n: Long): Boolean {
        if (n < 2) return false
        if (n == 2L) return true
        if (n % 2 == 0L) return false
        val sqrt = sqrt(n.toDouble()).toLong()
        for (i in 3..sqrt step 2) {
            if (n % i == 0L) return false
        }
        return true
    }
    
    fun primeFactors(n: Long): List<Long> {
        var num = n
        val factors = mutableListOf<Long>()
        var divisor = 2L
        
        while (num > 1 && divisor * divisor <= num) {
            while (num % divisor == 0L) {
                factors.add(divisor)
                num /= divisor
            }
            divisor = if (divisor == 2L) 3L else divisor + 2
        }
        
        if (num > 1) factors.add(num)
        return factors
    }
    
    fun gcd(a: Long, b: Long): Long {
        var x = a
        var y = b
        while (y != 0L) {
            val temp = y
            y = x % y
            x = temp
        }
        return abs(x)
    }
    
    fun lcm(a: Long, b: Long): Long {
        return abs(a * b) / gcd(a, b)
    }
    
    fun fibonacci(n: Int): Long {
        if (n < 0) throw IllegalArgumentException("Fibonacci not defined for negative numbers")
        if (n > 92) throw IllegalArgumentException("Fibonacci number too large for Long")
        
        var a = 0L
        var b = 1L
        repeat(n) {
            val temp = a + b
            a = b
            b = temp
        }
        return a
    }
    
    fun approximatePI(iterations: Int): Double {
        var pi = 0.0
        for (i in 0 until iterations) {
            pi += 4.0 * ((-1.0).pow(i) / (2 * i + 1))
        }
        return pi
    }
    
    // Advanced mathematical functions
    fun gamma(x: Double): Double {
        if (x <= 0.0 && x == x.roundToInt().toDouble()) 
            throw IllegalArgumentException("Gamma function not defined for non-positive integers")
        
        // Lanczos approximation
        val p = doubleArrayOf(
            676.5203681218851, -1259.1392167224028, 771.32342877765313,
            -176.61502916214059, 12.507343278686905, -0.13857109526572012,
            9.9843695780195716e-6, 1.5056327351493116e-7
        )
        
        var x = x
        var y = x
        val t = x + 7.5
        var sum = 0.99999999999980993
        
        for (i in p.indices) {
            sum += p[i] / ++y
        }
        
        return sqrt(2 * PI) * t.pow(x - 0.5) * exp(-t) * sum
    }
    
    fun beta(x: Double, y: Double): Double {
        return gamma(x) * gamma(y) / gamma(x + y)
    }
    
    fun erf(x: Double): Double {
        // Error function approximation
        val t = 1.0 / (1.0 + 0.5 * abs(x))
        val tau = t * exp(
            -x * x - 1.26551223 +
            t * (1.00002368 +
            t * (0.37409196 +
            t * (0.09678418 +
            t * (-0.18628806 +
            t * (0.27886807 +
            t * (-1.13520398 +
            t * (1.48851587 +
            t * (-0.82215223 +
            t * 0.17087277))))))))
        )
        return if (x >= 0) 1.0 - tau else tau - 1.0
    }
}
