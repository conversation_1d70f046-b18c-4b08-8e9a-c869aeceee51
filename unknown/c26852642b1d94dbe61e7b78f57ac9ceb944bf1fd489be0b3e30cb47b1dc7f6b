package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.DateMode
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.absoluteValue

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DateCalculatorScreen(modifier: Modifier = Modifier) {
    var mode by remember { mutableStateOf(DateMode.DIFFERENCE) }
    var date1 by remember { mutableStateOf(LocalDate.now()) }
    var date2 by remember { mutableStateOf(LocalDate.now()) }
    var daysToAdd by remember { mutableStateOf("") }
    var result by remember { mutableStateOf<DateResult?>(null) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(NeonGlow.copy(alpha = 0.10f), NeonCard),
                    center = Offset(0.5f, 0.5f),
                    radius = 900f
                )
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // Top Bar
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp, start = 16.dp, end = 16.dp, bottom = 6.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Date Calculator",
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.ExtraBold,
                        color = NeonGlow,
                        letterSpacing = 1.5.sp
                    )
                )
                IconButton(onClick = { /* TODO: Add help or settings */ }) {
                    Icon(Icons.Default.Info, contentDescription = "Info", tint = NeonGlow)
                }
            }
            
            // Main content
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(18.dp)
            ) {
                // Mode Selection
                NeonCard {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        DateMode.values().forEach { dateMode ->
                            NeonButton(
                                onClick = { 
                                    mode = dateMode
                                    result = null
                                },
                                modifier = Modifier.weight(1f),
                                enabled = mode != dateMode
                            ) {
                                Text(dateMode.displayName)
                            }
                        }
                    }
                }

                // Date Input Section
                NeonCard {
                    Column(
                        modifier = Modifier
                            .padding(16.dp)
                            .fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        when (mode) {
                            DateMode.DIFFERENCE -> {
                                DatePickerField(
                                    label = "First Date",
                                    date = date1,
                                    onDateChange = { 
                                        date1 = it
                                        calculateResult(mode, date1, date2, daysToAdd.toIntOrNull())?.let {
                                            result = it
                                        }
                                    }
                                )
                                DatePickerField(
                                    label = "Second Date",
                                    date = date2,
                                    onDateChange = { 
                                        date2 = it
                                        calculateResult(mode, date1, date2, daysToAdd.toIntOrNull())?.let {
                                            result = it
                                        }
                                    }
                                )
                            }
                            DateMode.ADD_SUBTRACT -> {
                                DatePickerField(
                                    label = "Start Date",
                                    date = date1,
                                    onDateChange = { 
                                        date1 = it
                                        calculateResult(mode, date1, date2, daysToAdd.toIntOrNull())?.let {
                                            result = it
                                        }
                                    }
                                )
                                NeonTextField(
                                    value = daysToAdd,
                                    onValueChange = { 
                                        daysToAdd = it
                                        calculateResult(mode, date1, date2, it.toIntOrNull())?.let {
                                            result = it
                                        }
                                    },
                                    label = { Text("Days to Add/Subtract") },
                                    modifier = Modifier.fillMaxWidth(),
                                    singleLine = true
                                )
                            }
                        }
                    }
                }

                // Results Section
                result?.let { dateResult ->
                    NeonCard(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text(
                                text = "Result",
                                style = MaterialTheme.typography.titleMedium,
                                color = NeonGlow
                            )
                            when (dateResult) {
                                is DateDifferenceResult -> {
                                    Text("Days: ${dateResult.days}")
                                    Text("Weeks: ${dateResult.weeks}")
                                    Text("Months: ${dateResult.months}")
                                    Text("Years: ${dateResult.years}")
                                }
                                is DateOperationResult -> {
                                    Text("New Date: ${dateResult.newDate}")
                                }
                            }
                        }
                    }
                }
            }

            // Calculate button
            NeonButton(
                onClick = {
                    result = calculateResult(mode, date1, date2, daysToAdd.toIntOrNull())
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                accentColor = NeonPink
            ) {
                Text("Calculate")
            }
        }
    }
}

private fun calculateResult(
    mode: DateMode,
    date1: LocalDate,
    date2: LocalDate,
    days: Int?
): DateResult? {
    return when (mode) {
        DateMode.DIFFERENCE -> {
            val daysBetween = ChronoUnit.DAYS.between(date1, date2).absoluteValue
            val weeks = daysBetween / 7
            val months = ChronoUnit.MONTHS.between(date1, date2).absoluteValue
            val years = ChronoUnit.YEARS.between(date1, date2).absoluteValue
            DateDifferenceResult(daysBetween, weeks, months, years)
        }
        DateMode.ADD_SUBTRACT -> {
            days?.let {
                DateOperationResult(date1.plusDays(it.toLong()))
            }
        }
    }
}

sealed class DateResult
data class DateDifferenceResult(
    val days: Long,
    val weeks: Long,
    val months: Long,
    val years: Long
) : DateResult()

data class DateOperationResult(val newDate: LocalDate) : DateResult()
