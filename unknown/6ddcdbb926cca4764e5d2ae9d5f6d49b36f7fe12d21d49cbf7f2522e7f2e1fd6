package com.app.wordifynumbers.utils

import com.app.wordifynumbers.model.*
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.*
import kotlin.math.log10
import kotlin.math.pow

/**
 * Utility class for large number operations
 */
object LargeNumberUtils {

    /**
     * Breaks down a large number into its scale components
     */
    fun breakdownLargeNumber(number: Long, locale: Locale): NumberBreakdown {
        val components = mutableListOf<ScaleComponent>()
        var remainingNumber = number
        
        // Handle special case for zero
        if (number == 0L) {
            return NumberBreakdown(
                originalNumber = 0,
                components = listOf(ScaleComponent(0, NumberScale.ONES, getLocalizedScaleName(NumberScale.ONES, locale))),
                scientificNotation = "0",
                internationalFormat = "0",
                localizedFormat = "0"
            )
        }
        
        // Find the largest scale that fits the number
        val scales = NumberScale.values().sortedByDescending { it.value }
        
        for (scale in scales) {
            if (remainingNumber >= scale.value) {
                val value = (remainingNumber / scale.value).toInt()
                components.add(
                    ScaleComponent(
                        value = value,
                        scale = scale,
                        localizedName = getLocalizedScaleName(scale, locale)
                    )
                )
                remainingNumber %= scale.value
                
                // If we've processed down to zero, we're done
                if (remainingNumber == 0L) {
                    break
                }
            }
        }
        
        return NumberBreakdown(
            originalNumber = number,
            components = components,
            scientificNotation = formatScientificNotation(number),
            internationalFormat = formatInternational(number),
            localizedFormat = formatLocalized(number, locale)
        )
    }
    
    /**
     * Formats a number in scientific notation
     */
    private fun formatScientificNotation(number: Long): String {
        if (number == 0L) return "0"
        
        val exponent = log10(number.toDouble()).toInt()
        val mantissa = number.toDouble() / 10.0.pow(exponent.toDouble())
        
        return if (mantissa == 1.0) {
            "10^$exponent"
        } else {
            String.format("%.2f × 10^%d", mantissa, exponent)
        }
    }
    
    /**
     * Formats a number in international format (with commas)
     */
    private fun formatInternational(number: Long): String {
        return DecimalFormat("#,###").format(number)
    }
    
    /**
     * Formats a number according to the locale
     */
    private fun formatLocalized(number: Long, locale: Locale): String {
        return NumberFormat.getNumberInstance(locale).format(number)
    }
    
    /**
     * Provides contextual explanations for large numbers
     */
    fun getLargeNumberExplanation(number: Long, locale: Locale): String {
        return when {
            number < 1_000 -> getExplanationForSmallNumber(number, locale)
            number < 1_000_000 -> getExplanationForThousands(number, locale)
            number < 1_000_000_000 -> getExplanationForMillions(number, locale)
            number < 1_000_000_000_000 -> getExplanationForBillions(number, locale)
            else -> getExplanationForTrillionsPlus(number, locale)
        }
    }
    
    private fun getExplanationForSmallNumber(number: Long, locale: Locale): String {
        val languageCode = locale.language
        return when (languageCode) {
            "es" -> "Este es un número pequeño, menor que mil."
            "fr" -> "C'est un petit nombre, inférieur à mille."
            "de" -> "Dies ist eine kleine Zahl, kleiner als tausend."
            "zh" -> "这是一个小于一千的小数字。"
            "ja" -> "これは千未満の小さな数字です。"
            "hi" -> "यह एक छोटी संख्या है, एक हजार से कम।"
            "ar" -> "هذا رقم صغير، أقل من ألف."
            "ru" -> "Это небольшое число, меньше тысячи."
            "it" -> "Questo è un numero piccolo, inferiore a mille."
            "pt" -> "Este é um número pequeno, menor que mil."
            else -> "This is a small number, less than a thousand."
        }
    }
    
    private fun getExplanationForThousands(number: Long, locale: Locale): String {
        val languageCode = locale.language
        return when (languageCode) {
            "es" -> "Este número está en los miles. Por ejemplo, la población de una pequeña ciudad o el precio de un ordenador."
            "fr" -> "Ce nombre est en milliers. Par exemple, la population d'une petite ville ou le prix d'un ordinateur."
            "de" -> "Diese Zahl liegt im Tausenderbereich. Zum Beispiel die Bevölkerung einer kleinen Stadt oder der Preis eines Computers."
            "zh" -> "这个数字在千位数范围内。例如，一个小城镇的人口或一台电脑的价格。"
            "ja" -> "この数字は千の位です。例えば、小さな町の人口やコンピュータの価格など。"
            "hi" -> "यह संख्या हजारों में है। उदाहरण के लिए, एक छोटे शहर की आबादी या एक कंप्यूटर की कीमत।"
            "ar" -> "هذا الرقم بالآلاف. على سبيل المثال، عدد سكان مدينة صغيرة أو سعر كمبيوتر."
            "ru" -> "Это число в тысячах. Например, население небольшого города или цена компьютера."
            "it" -> "Questo numero è nell'ordine delle migliaia. Ad esempio, la popolazione di una piccola città o il prezzo di un computer."
            "pt" -> "Este número está na casa dos milhares. Por exemplo, a população de uma pequena cidade ou o preço de um computador."
            else -> "This number is in the thousands. For example, the population of a small town or the price of a computer."
        }
    }
    
    private fun getExplanationForMillions(number: Long, locale: Locale): String {
        val languageCode = locale.language
        return when (languageCode) {
            "es" -> "Este número está en los millones. Por ejemplo, la población de una gran ciudad o el presupuesto anual de una empresa mediana."
            "fr" -> "Ce nombre est en millions. Par exemple, la population d'une grande ville ou le budget annuel d'une entreprise de taille moyenne."
            "de" -> "Diese Zahl liegt im Millionenbereich. Zum Beispiel die Bevölkerung einer Großstadt oder das Jahresbudget eines mittelständischen Unternehmens."
            "zh" -> "这个数字在百万范围内。例如，一个大城市的人口或一个中型公司的年度预算。"
            "ja" -> "この数字は百万の位です。例えば、大都市の人口や中規模企業の年間予算など。"
            "hi" -> "यह संख्या लाखों में है। उदाहरण के लिए, एक बड़े शहर की आबादी या एक मध्यम आकार की कंपनी का वार्षिक बजट।"
            "ar" -> "هذا الرقم بالملايين. على سبيل المثال، عدد سكان مدينة كبيرة أو الميزانية السنوية لشركة متوسطة الحجم."
            "ru" -> "Это число в миллионах. Например, население крупного города или годовой бюджет компании среднего размера."
            "it" -> "Questo numero è nell'ordine dei milioni. Ad esempio, la popolazione di una grande città o il budget annuale di un'azienda di medie dimensioni."
            "pt" -> "Este número está na casa dos milhões. Por exemplo, a população de uma grande cidade ou o orçamento anual de uma empresa de médio porte."
            else -> "This number is in the millions. For example, the population of a large city or the annual budget of a medium-sized company."
        }
    }
    
    private fun getExplanationForBillions(number: Long, locale: Locale): String {
        val languageCode = locale.language
        return when (languageCode) {
            "es" -> "Este número está en los miles de millones. Por ejemplo, el PIB de un país pequeño o el valor de una gran empresa tecnológica."
            "fr" -> "Ce nombre est en milliards. Par exemple, le PIB d'un petit pays ou la valeur d'une grande entreprise technologique."
            "de" -> "Diese Zahl liegt im Milliardenbereich. Zum Beispiel das BIP eines kleinen Landes oder der Wert eines großen Technologieunternehmens."
            "zh" -> "这个数字在十亿范围内。例如，一个小国家的GDP或一个大型科技公司的价值。"
            "ja" -> "この数字は十億の位です。例えば、小国のGDPや大手テクノロジー企業の価値など。"
            "hi" -> "यह संख्या अरबों में है। उदाहरण के लिए, एक छोटे देश का सकल घरेलू उत्पाद या एक बड़ी तकनीकी कंपनी का मूल्य।"
            "ar" -> "هذا الرقم بالمليارات. على سبيل المثال، الناتج المحلي الإجمالي لبلد صغير أو قيمة شركة تكنولوجيا كبيرة."
            "ru" -> "Это число в миллиардах. Например, ВВП небольшой страны или стоимость крупной технологической компании."
            "it" -> "Questo numero è nell'ordine dei miliardi. Ad esempio, il PIL di un piccolo paese o il valore di una grande azienda tecnologica."
            "pt" -> "Este número está na casa dos bilhões. Por exemplo, o PIB de um país pequeno ou o valor de uma grande empresa de tecnologia."
            else -> "This number is in the billions. For example, the GDP of a small country or the value of a large technology company."
        }
    }
    
    private fun getExplanationForTrillionsPlus(number: Long, locale: Locale): String {
        val languageCode = locale.language
        return when (languageCode) {
            "es" -> "Este es un número extremadamente grande. Para contextualizar, el PIB mundial es de aproximadamente 100 billones de dólares."
            "fr" -> "C'est un nombre extrêmement grand. Pour contextualiser, le PIB mondial est d'environ 100 billions de dollars."
            "de" -> "Dies ist eine extrem große Zahl. Zum Vergleich: Das weltweite BIP beträgt etwa 100 Billionen Dollar."
            "zh" -> "这是一个极其庞大的数字。作为参考，全球GDP约为100万亿美元。"
            "ja" -> "これは非常に大きな数字です。参考までに、世界のGDPは約100兆ドルです。"
            "hi" -> "यह एक अत्यधिक बड़ी संख्या है। संदर्भ के लिए, विश्व जीडीपी लगभग 100 ट्रिलियन डॉलर है।"
            "ar" -> "هذا رقم كبير للغاية. للسياق، الناتج المحلي الإجمالي العالمي هو حوالي 100 تريليون دولار."
            "ru" -> "Это чрезвычайно большое число. Для контекста, мировой ВВП составляет около 100 триллионов долларов."
            "it" -> "Questo è un numero estremamente grande. Per contestualizzare, il PIL mondiale è di circa 100 trilioni di dollari."
            "pt" -> "Este é um número extremamente grande. Para contextualizar, o PIB mundial é de aproximadamente 100 trilhões de dólares."
            else -> "This is an extremely large number. For context, the world GDP is about 100 trillion dollars."
        }
    }
    
    /**
     * Creates visual representation units for a number
     */
    fun visualizeNumber(number: Long, locale: Locale): List<VisualizationUnit> {
        val units = mutableListOf<VisualizationUnit>()
        
        // Add a unit for the current number
        units.add(
            VisualizationUnit(
                magnitude = number,
                label = formatInternational(number),
                description = getLargeNumberExplanation(number, locale),
                comparisonExample = getComparisonExample(number, locale)
            )
        )
        
        // Add a unit for a smaller scale (if applicable)
        if (number > 1000) {
            val smallerScale = number / 1000
            units.add(
                VisualizationUnit(
                    magnitude = smallerScale,
                    label = formatInternational(smallerScale),
                    description = "1/${formatInternational(1000)} of the original number",
                    comparisonExample = getComparisonExample(smallerScale, locale)
                )
            )
        }
        
        // Add a unit for a larger scale (if applicable)
        if (number < 1_000_000_000_000L) {
            val largerScale = number * 1000
            units.add(
                VisualizationUnit(
                    magnitude = largerScale,
                    label = formatInternational(largerScale),
                    description = "${formatInternational(1000)} times the original number",
                    comparisonExample = getComparisonExample(largerScale, locale)
                )
            )
        }
        
        return units
    }
    
    /**
     * Provides a comparison example for a number
     */
    private fun getComparisonExample(number: Long, locale: Locale): String {
        val languageCode = locale.language
        
        return when {
            number < 10 -> when (languageCode) {
                "es" -> "Menos que los dedos de ambas manos."
                "fr" -> "Moins que les doigts des deux mains."
                "de" -> "Weniger als die Finger beider Hände."
                "zh" -> "少于双手的手指数量。"
                "ja" -> "両手の指より少ない。"
                "hi" -> "दोनों हाथों की उंगलियों से कम।"
                "ar" -> "أقل من أصابع اليدين."
                "ru" -> "Меньше, чем пальцев на обеих руках."
                "it" -> "Meno delle dita di entrambe le mani."
                "pt" -> "Menos que os dedos de ambas as mãos."
                else -> "Less than the fingers on both hands."
            }
            number < 100 -> when (languageCode) {
                "es" -> "Aproximadamente el número de años en un siglo."
                "fr" -> "Environ le nombre d'années dans un siècle."
                "de" -> "Ungefähr die Anzahl der Jahre in einem Jahrhundert."
                "zh" -> "大约是一个世纪的年数。"
                "ja" -> "約1世紀の年数。"
                "hi" -> "लगभग एक शताब्दी में वर्षों की संख्या।"
                "ar" -> "تقريبا عدد السنوات في قرن واحد."
                "ru" -> "Примерно количество лет в веке."
                "it" -> "Circa il numero di anni in un secolo."
                "pt" -> "Aproximadamente o número de anos em um século."
                else -> "About the number of years in a century."
            }
            number < 1_000 -> when (languageCode) {
                "es" -> "Aproximadamente las páginas de un libro largo."
                "fr" -> "Environ les pages d'un long livre."
                "de" -> "Ungefähr die Seiten eines langen Buches."
                "zh" -> "大约是一本长书的页数。"
                "ja" -> "長い本のページ数ほど。"
                "hi" -> "लगभग एक लंबी किताब के पन्नों की संख्या।"
                "ar" -> "تقريبا صفحات كتاب طويل."
                "ru" -> "Примерно страницы длинной книги."
                "it" -> "Circa le pagine di un libro lungo."
                "pt" -> "Aproximadamente as páginas de um livro longo."
                else -> "About the pages in a long book."
            }
            number < 10_000 -> when (languageCode) {
                "es" -> "La población de un pueblo pequeño."
                "fr" -> "La population d'un petit village."
                "de" -> "Die Bevölkerung eines kleinen Dorfes."
                "zh" -> "一个小村庄的人口。"
                "ja" -> "小さな村の人口。"
                "hi" -> "एक छोटे गांव की आबादी।"
                "ar" -> "عدد سكان قرية صغيرة."
                "ru" -> "Население маленькой деревни."
                "it" -> "La popolazione di un piccolo villaggio."
                "pt" -> "A população de uma pequena vila."
                else -> "The population of a small village."
            }
            number < 100_000 -> when (languageCode) {
                "es" -> "La capacidad de un estadio deportivo grande."
                "fr" -> "La capacité d'un grand stade sportif."
                "de" -> "Die Kapazität eines großen Sportstadions."
                "zh" -> "一个大型体育场馆的容量。"
                "ja" -> "大きなスポーツスタジアムの収容人数。"
                "hi" -> "एक बड़े खेल स्टेडियम की क्षमता।"
                "ar" -> "سعة ملعب رياضي كبير."
                "ru" -> "Вместимость большого спортивного стадиона."
                "it" -> "La capacità di un grande stadio sportivo."
                "pt" -> "A capacidade de um grande estádio esportivo."
                else -> "The capacity of a large sports stadium."
            }
            number < 1_000_000 -> when (languageCode) {
                "es" -> "La población de una ciudad mediana."
                "fr" -> "La population d'une ville moyenne."
                "de" -> "Die Bevölkerung einer mittelgroßen Stadt."
                "zh" -> "一个中等城市的人口。"
                "ja" -> "中規模都市の人口。"
                "hi" -> "एक मध्यम आकार के शहर की आबादी।"
                "ar" -> "عدد سكان مدينة متوسطة الحجم."
                "ru" -> "Население среднего города."
                "it" -> "La popolazione di una città di medie dimensioni."
                "pt" -> "A população de uma cidade média."
                else -> "The population of a medium-sized city."
            }
            number < 10_000_000 -> when (languageCode) {
                "es" -> "La población de una gran ciudad o área metropolitana."
                "fr" -> "La population d'une grande ville ou zone métropolitaine."
                "de" -> "Die Bevölkerung einer Großstadt oder eines Ballungsraums."
                "zh" -> "一个大城市或都市区的人口。"
                "ja" -> "大都市または都市圏の人口。"
                "hi" -> "एक बड़े शहर या महानगरीय क्षेत्र की आबादी।"
                "ar" -> "عدد سكان مدينة كبيرة أو منطقة حضرية."
                "ru" -> "Население крупного города или мегаполиса."
                "it" -> "La popolazione di una grande città o area metropolitana."
                "pt" -> "A população de uma grande cidade ou área metropolitana."
                else -> "The population of a large city or metropolitan area."
            }
            number < 100_000_000 -> when (languageCode) {
                "es" -> "La población de un país pequeño."
                "fr" -> "La population d'un petit pays."
                "de" -> "Die Bevölkerung eines kleinen Landes."
                "zh" -> "一个小国家的人口。"
                "ja" -> "小国の人口。"
                "hi" -> "एक छोटे देश की आबादी।"
                "ar" -> "عدد سكان دولة صغيرة."
                "ru" -> "Население небольшой страны."
                "it" -> "La popolazione di un piccolo paese."
                "pt" -> "A população de um país pequeno."
                else -> "The population of a small country."
            }
            number < 1_000_000_000 -> when (languageCode) {
                "es" -> "La población de un país grande."
                "fr" -> "La population d'un grand pays."
                "de" -> "Die Bevölkerung eines großen Landes."
                "zh" -> "一个大国家的人口。"
                "ja" -> "大国の人口。"
                "hi" -> "एक बड़े देश की आबादी।"
                "ar" -> "عدد سكان دولة كبيرة."
                "ru" -> "Население большой страны."
                "it" -> "La popolazione di un grande paese."
                "pt" -> "A população de um país grande."
                else -> "The population of a large country."
            }
            number < 10_000_000_000 -> when (languageCode) {
                "es" -> "Más que la población mundial."
                "fr" -> "Plus que la population mondiale."
                "de" -> "Mehr als die Weltbevölkerung."
                "zh" -> "超过世界人口。"
                "ja" -> "世界の人口より多い。"
                "hi" -> "विश्व की आबादी से अधिक।"
                "ar" -> "أكثر من عدد سكان العالم."
                "ru" -> "Больше, чем население мира."
                "it" -> "Più della popolazione mondiale."
                "pt" -> "Mais do que a população mundial."
                else -> "More than the world's population."
            }
            else -> when (languageCode) {
                "es" -> "Un número astronómicamente grande."
                "fr" -> "Un nombre astronomiquement grand."
                "de" -> "Eine astronomisch große Zahl."
                "zh" -> "一个天文数字。"
                "ja" -> "天文学的に大きな数字。"
                "hi" -> "एक खगोलीय रूप से बड़ी संख्या।"
                "ar" -> "رقم كبير بشكل فلكي."
                "ru" -> "Астрономически большое число."
                "it" -> "Un numero astronomicamente grande."
                "pt" -> "Um número astronomicamente grande."
                else -> "An astronomically large number."
            }
        }
    }
}
