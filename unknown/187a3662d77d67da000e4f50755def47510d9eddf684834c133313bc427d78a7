<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>name</key>
    <string>OpenVPN Configuration</string>

    <key>fileTypes</key>
    <array>
        <string>ovpn</string>
        <string>openvpn</string>
        <string>conf</string>
    </array>

    <key>patterns</key>
    <array>
        <dict>
            <key>match</key>
            <string>(;|#).*$</string>
            <key>name</key>
            <string>comment.line.character.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>^\s*(?i)(allow-pull-fqdn|bind|cd|chroot|client-nat|comp-noadapt|compress|connect-retry(-max)?|daemon|dev(-node|-type)?|disable-occ|down(-pre)?|echo|errors-to-stderr|fast-io|float|fragment|group|http-proxy(-option)?|ifconfig(-noexec|-nowarn)?|ignore-unknown-option|inactive|inetd|ipchange|iproute|keepalive|keying-material-exporter|link-mtu|lladdr|local|log|log-append|lport|machine-readable-output|management(-client|-client-auth|-client-group|-client-pf|-client-user|-forget-disconnect|-hold|-log-cache|-signal|-up-down)?|management-external-(cert|key)|management-query-(passwords|proxy|remote)|mark|mlock|mode|mssfix|mtu-(disc|test)?|multihome|mute|nice|nobind|passtos|persist-(key|local-ip|remote-ip|tun)?|ping(-exit|-restart|-timer-rem)|plugin|port|proto(-force)?|rcvbuf|redirect-(gateway|private)|remap-usr1|remote(-random|-random-hostname)?|resolv-retry|route(-delay|-gateway|-metric|-noexec|-nopull|-pre-down|-up)?|rport|script-security|setcon|setenv(-safe)?|shaper|show-proxy-settings|sndbuf|socket-flags|socks-proxy|status(-version)?|suppress-timestamps|syslog|topology|tun-mtu(-extra)?|txqueuelen|up(-delay|-restart)?|user|verb|writepid|auth(-gen-token|-user-pass-optional|-user-pass-verify)?|bcast-buffers|ccd-exclusive|client-(config-dir|connect|disconnect|to-client)|connect-freq|disable|duplicate-cn|hash-size|ifconfig-(pool|pool-persist|push)|iroute|learn-address|max-clients|max-routes-per-client|opt-verify|port-share|push(-peer-info|-remove|-reset)?|server(-bridge)?|stale-routes-check|tcp-(nodelay|queue-limit)|tmp-dir|username-as-common-name|verify-client-cert|allow-recursive-routing|auth-(retry|user-pass)|client|connect-timeout|explicit-exit-notify|pull(-filter)?|server-poll-timeout|static-challenge|cipher|data-ciphers(-fallback)?|engine|key-direction|mute-replay-warnings|ncp-(ciphers|disable)|prng|replay-(persist|window)|secret|test-crypto|use-prediction-resistance|askpass|auth-(nocache|token)|ca(path)?|cert|crl-verify|cryptoapicert|dh|ecdh-curve|extra-certs|hand-window|key(-method)?|pkcs11-(cert-private|id|id-management|pin-cache|private-mode|protected-authentication|providers)|pkcs12|remote-cert-(eku|tls)|reneg-(bytes|pkts|sec)|single-session|tls-(auth|cert-profile|cipher|client|crypt|exit|export-cert|server|timeout|verify|version-(max|min))|tran-window|verify-(hash|x509-name)|x509-track|x509-username-field|(rm|mk)tun|allow-nonadmin|block-outside-dns|dhcp-(option|release|renew)|ip-win32|pause-exit|register-dns|route-method|service|show-net-up|tap-sleep|win-sys|ifconfig-ipv6(-pool|-push)?|(i)?route-ipv6|server-ipv6)\b</string>
            <key>name</key>
            <string>keyword.language.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>^\s*(?i)(comp-lzo)\b</string>
            <key>name</key>
            <string>invalid.deprecated.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>\b((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))\b</string>
            <key>name</key>
            <string>string.other.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>\b(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\b</string>
            <key>name</key>
            <string>string.other.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>\s+([0-9]+(\.[0-9]+)*)\s+</string>
            <key>name</key>
            <string>constant.numeric.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>\s+(?i)(autolocal|bypass-dhcp|bypass-dns|def1|ignore|infinite|local|net-gateway|server|tap|tcp|tcp-client|tcp-server|tun|udp|vpn_gateway)\s+</string>
            <key>name</key>
            <string>entity.other.attribute-name.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>\b(?i)(AES-128-CBC|AES-128-CFB|AES-128-CFB1|AES-128-CFB8|AES-128-GCM|AES-128-OFB|AES-192-CBC|AES-192-CFB|AES-192-CFB1|AES-192-CFB8|AES-192-GCM|AES-192-OFB|AES-256-CBC|AES-256-CFB|AES-256-CFB1|AES-256-CFB8|AES-256-GCM|AES-256-OFB|CAMELLIA-128-CBC|CAMELLIA-128-CFB|CAMELLIA-128-CFB1|CAMELLIA-128-CFB8|CAMELLIA-128-OFB|CAMELLIA-192-CBC|CAMELLIA-192-CFB|CAMELLIA-192-CFB1|CAMELLIA-192-CFB8|CAMELLIA-192-OFB|CAMELLIA-256-CBC|CAMELLIA-256-CFB|CAMELLIA-256-CFB1|CAMELLIA-256-CFB8|CAMELLIA-256-OFB|SEED-CBC|SEED-CFB|SEED-OFB)</string>
            <key>name</key>
            <string>entity.other.attribute-name.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>\b(?i)(BF-CBC|BF-CFB|BF-OFB|CAST5-CBC|CAST5-CFB|CAST5-OFB|DES-CBC|DES-CFB|DES-CFB1|DES-CFB8|DES-EDE-CBC|DES-EDE-CFB|DES-EDE-OFB|DES-EDE3-CBC|DES-EDE3-CFB|DES-EDE3-CFB1|DES-EDE3-CFB8|DES-EDE3-OFB|DES-OFB|DESX-CBC|IDEA-CBC|IDEA-CFB|IDEA-OFB|RC2-40-CBC|RC2-64-CBC|RC2-CBC|RC2-CFB|RC2-OFB)\b</string>
            <key>name</key>
            <string>invalid.deprecated.openvpn</string>
        </dict>

        <dict>
            <key>match</key>
            <string>\[ '"\]</string>
            <key>name</key>
            <string>constant.character.escape.openvpn</string>
        </dict>

        <dict>
            <key>begin</key>
            <string>"</string>

            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.string.begin.openvpn</string>
                </dict>
            </dict>

            <key>end</key>
            <string>"</string>

            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.string.end.openvpn</string>
                </dict>
            </dict>

            <key>name</key>
            <string>string.quoted.double.openvpn</string>

            <key>patterns</key>
            <array>
                <dict>
                    <key>match</key>
                    <string>\[ '"\]</string>

                    <key>name</key>
                    <string>constant.character.escape.openvpn</string>
                </dict>
            </array>
        </dict>

        <dict>
            <key>begin</key>
            <string>(&lt;/?)((?i:ca|cert|key|tls-auth )\b)</string>

            <key>captures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.begin.html</string>
                </dict>

                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.structure.any.openvpn</string>
                </dict>
            </dict>

            <key>end</key>
            <string>"</string>

            <key>end</key>
            <string>(&gt;)</string>
            <key>endCaptures</key>
            <dict>
              <key>1</key>
              <dict>
                <key>name</key>
                <string>punctuation.definition.tag.end.html</string>
              </dict>
            </dict>
        </dict>
        <dict>
            <key>begin</key>
            <string>^-----</string>
            <key>captures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.comment.openvpn</string>
                </dict>
            </dict>
            <key>end</key>
            <string>-----$</string>
            <key>name</key>
            <string>comment.block.openvpn</string>
        </dict>
    </array>

    <key>scopeName</key>
    <string>source.openvpn</string>

    <key>uuid</key>
    <string>b7665bea-bdf5-4f06-ad7e-eba459f1e2fe</string>
</dict>
</plist>
