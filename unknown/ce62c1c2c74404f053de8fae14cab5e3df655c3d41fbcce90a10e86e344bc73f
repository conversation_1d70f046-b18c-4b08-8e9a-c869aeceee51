package com.app.wordifynumbers

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider

/**
 * Factory for creating NumberConverterViewModel instances
 */
class NumberConverterViewModelFactory(private val context: Context) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(NumberConverterViewModel::class.java)) {
            return NumberConverterViewModel(context) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
