<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- Production configuration -->
    <domain-config cleartextTrafficPermitted="false">
        <!-- Only allow HTTPS connections -->
        <domain includeSubdomains="true">api.wordifynumbers.com</domain>
        <domain includeSubdomains="true">analytics.google.com</domain>
        <domain includeSubdomains="true">firebase.googleapis.com</domain>
        
        <!-- Pin certificates for critical domains -->
        <pin-set expiration="2025-12-31">
            <!-- SHA-256 hash of the certificate -->
            <pin digest="SHA-256">AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</pin>
            <!-- Backup pin -->
            <pin digest="SHA-256">BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=</pin>
        </pin-set>
    </domain-config>
    
    <!-- Debug configuration - only for development -->
    <debug-overrides>
        <trust-anchors>
            <!-- Trust user-added CAs for debugging -->
            <certificates src="user"/>
            <certificates src="system"/>
        </trust-anchors>
    </debug-overrides>
    
    <!-- Base configuration -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- Trust only system CAs -->
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
