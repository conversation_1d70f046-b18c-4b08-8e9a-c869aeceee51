package com.app.wordifynumbers

import com.app.wordifynumbers.utils.NumberToWords
import org.junit.Assert.assertEquals
import org.junit.Test

class NumberToWordsTest {
    @Test
    fun testConvert() {
        assertEquals("One", NumberToWords.convert(1))
        assertEquals("Twenty-One", NumberToWords.convert(21))
        assertEquals("One Hundred Twenty-Three", NumberToWords.convert(123))
        assertEquals("Zero", NumberToWords.convert(0))
        assertEquals("Minus One", NumberToWords.convert(-1))
    }
}
