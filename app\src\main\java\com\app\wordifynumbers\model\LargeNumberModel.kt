package com.app.wordifynumbers.model

import java.util.Locale

/**
 * Represents a breakdown of a large number into its scale components
 */
data class NumberBreakdown(
    val originalNumber: Long,
    val components: List<ScaleComponent>,
    val scientificNotation: String,
    val internationalFormat: String,
    val localizedFormat: String
)

/**
 * Represents a single scale component of a large number
 * (e.g., millions, billions, etc.)
 */
data class ScaleComponent(
    val value: Int,
    val scale: NumberScale,
    val localizedName: String
)

/**
 * Represents a visualization unit for displaying large numbers
 */
data class VisualizationUnit(
    val magnitude: Long,
    val label: String,
    val description: String,
    val comparisonExample: String
)

/**
 * Enum representing different number scales
 */
enum class NumberScale(val value: Long, val englishName: String) {
    ONES(1, "ones"),
    TENS(10, "tens"),
    HUNDREDS(100, "hundreds"),
    THOUSANDS(1_000, "thousand"),
    TEN_THOUSANDS(10_000, "ten thousand"),
    HUNDRED_THOUSANDS(100_000, "hundred thousand"),
    MILLIONS(1_000_000, "million"),
    TEN_MILLIONS(10_000_000, "ten million"),
    HUNDRED_MILLIONS(100_000_000, "hundred million"),
    BILLIONS(1_000_000_000, "billion"),
    TEN_BILLIONS(10_000_000_000, "ten billion"),
    HUNDRED_BILLIONS(100_000_000_000, "hundred billion"),
    TRILLIONS(1_000_000_000_000, "trillion"),
    TEN_TRILLIONS(10_000_000_000_000, "ten trillion"),
    HUNDRED_TRILLIONS(100_000_000_000_000, "hundred trillion"),
    QUADRILLIONS(1_000_000_000_000_000, "quadrillion"),
    QUINTILLIONS(1_000_000_000_000_000_000, "quintillion");

    companion object {
        fun fromValue(value: Long): NumberScale {
            return values().firstOrNull { it.value <= value && value < it.value * 1000 }
                ?: QUINTILLIONS
        }
    }
}

/**
 * Map of language codes to their scale name translations
 */
val scaleTranslations = mapOf(
    // English is the default
    "en" to mapOf(
        NumberScale.ONES to "ones",
        NumberScale.TENS to "tens",
        NumberScale.HUNDREDS to "hundreds",
        NumberScale.THOUSANDS to "thousand",
        NumberScale.TEN_THOUSANDS to "ten thousand",
        NumberScale.HUNDRED_THOUSANDS to "hundred thousand",
        NumberScale.MILLIONS to "million",
        NumberScale.TEN_MILLIONS to "ten million",
        NumberScale.HUNDRED_MILLIONS to "hundred million",
        NumberScale.BILLIONS to "billion",
        NumberScale.TEN_BILLIONS to "ten billion",
        NumberScale.HUNDRED_BILLIONS to "hundred billion",
        NumberScale.TRILLIONS to "trillion",
        NumberScale.TEN_TRILLIONS to "ten trillion",
        NumberScale.HUNDRED_TRILLIONS to "hundred trillion",
        NumberScale.QUADRILLIONS to "quadrillion",
        NumberScale.QUINTILLIONS to "quintillion"
    ),
    // Spanish
    "es" to mapOf(
        NumberScale.ONES to "unidades",
        NumberScale.TENS to "decenas",
        NumberScale.HUNDREDS to "centenas",
        NumberScale.THOUSANDS to "mil",
        NumberScale.TEN_THOUSANDS to "diez mil",
        NumberScale.HUNDRED_THOUSANDS to "cien mil",
        NumberScale.MILLIONS to "millón",
        NumberScale.TEN_MILLIONS to "diez millones",
        NumberScale.HUNDRED_MILLIONS to "cien millones",
        NumberScale.BILLIONS to "mil millones",
        NumberScale.TEN_BILLIONS to "diez mil millones",
        NumberScale.HUNDRED_BILLIONS to "cien mil millones",
        NumberScale.TRILLIONS to "billón",
        NumberScale.TEN_TRILLIONS to "diez billones",
        NumberScale.HUNDRED_TRILLIONS to "cien billones",
        NumberScale.QUADRILLIONS to "mil billones",
        NumberScale.QUINTILLIONS to "trillón"
    ),
    // French
    "fr" to mapOf(
        NumberScale.ONES to "unités",
        NumberScale.TENS to "dizaines",
        NumberScale.HUNDREDS to "centaines",
        NumberScale.THOUSANDS to "mille",
        NumberScale.TEN_THOUSANDS to "dix mille",
        NumberScale.HUNDRED_THOUSANDS to "cent mille",
        NumberScale.MILLIONS to "million",
        NumberScale.TEN_MILLIONS to "dix millions",
        NumberScale.HUNDRED_MILLIONS to "cent millions",
        NumberScale.BILLIONS to "milliard",
        NumberScale.TEN_BILLIONS to "dix milliards",
        NumberScale.HUNDRED_BILLIONS to "cent milliards",
        NumberScale.TRILLIONS to "billion",
        NumberScale.TEN_TRILLIONS to "dix billions",
        NumberScale.HUNDRED_TRILLIONS to "cent billions",
        NumberScale.QUADRILLIONS to "billiard",
        NumberScale.QUINTILLIONS to "trillion"
    ),
    // German
    "de" to mapOf(
        NumberScale.ONES to "Einheiten",
        NumberScale.TENS to "Zehner",
        NumberScale.HUNDREDS to "Hunderter",
        NumberScale.THOUSANDS to "Tausend",
        NumberScale.TEN_THOUSANDS to "Zehntausend",
        NumberScale.HUNDRED_THOUSANDS to "Hunderttausend",
        NumberScale.MILLIONS to "Million",
        NumberScale.TEN_MILLIONS to "Zehn Millionen",
        NumberScale.HUNDRED_MILLIONS to "Hundert Millionen",
        NumberScale.BILLIONS to "Milliarde",
        NumberScale.TEN_BILLIONS to "Zehn Milliarden",
        NumberScale.HUNDRED_BILLIONS to "Hundert Milliarden",
        NumberScale.TRILLIONS to "Billion",
        NumberScale.TEN_TRILLIONS to "Zehn Billionen",
        NumberScale.HUNDRED_TRILLIONS to "Hundert Billionen",
        NumberScale.QUADRILLIONS to "Billiarde",
        NumberScale.QUINTILLIONS to "Trillion"
    ),
    // Chinese
    "zh" to mapOf(
        NumberScale.ONES to "个",
        NumberScale.TENS to "十",
        NumberScale.HUNDREDS to "百",
        NumberScale.THOUSANDS to "千",
        NumberScale.TEN_THOUSANDS to "万",
        NumberScale.HUNDRED_THOUSANDS to "十万",
        NumberScale.MILLIONS to "百万",
        NumberScale.TEN_MILLIONS to "千万",
        NumberScale.HUNDRED_MILLIONS to "亿",
        NumberScale.BILLIONS to "十亿",
        NumberScale.TEN_BILLIONS to "百亿",
        NumberScale.HUNDRED_BILLIONS to "千亿",
        NumberScale.TRILLIONS to "兆",
        NumberScale.TEN_TRILLIONS to "十兆",
        NumberScale.HUNDRED_TRILLIONS to "百兆",
        NumberScale.QUADRILLIONS to "千兆",
        NumberScale.QUINTILLIONS to "京"
    ),
    // Japanese
    "ja" to mapOf(
        NumberScale.ONES to "一",
        NumberScale.TENS to "十",
        NumberScale.HUNDREDS to "百",
        NumberScale.THOUSANDS to "千",
        NumberScale.TEN_THOUSANDS to "万",
        NumberScale.HUNDRED_THOUSANDS to "十万",
        NumberScale.MILLIONS to "百万",
        NumberScale.TEN_MILLIONS to "千万",
        NumberScale.HUNDRED_MILLIONS to "億",
        NumberScale.BILLIONS to "十億",
        NumberScale.TEN_BILLIONS to "百億",
        NumberScale.HUNDRED_BILLIONS to "千億",
        NumberScale.TRILLIONS to "兆",
        NumberScale.TEN_TRILLIONS to "十兆",
        NumberScale.HUNDRED_TRILLIONS to "百兆",
        NumberScale.QUADRILLIONS to "千兆",
        NumberScale.QUINTILLIONS to "京"
    ),
    // Hindi
    "hi" to mapOf(
        NumberScale.ONES to "इकाई",
        NumberScale.TENS to "दहाई",
        NumberScale.HUNDREDS to "सैकड़ा",
        NumberScale.THOUSANDS to "हज़ार",
        NumberScale.TEN_THOUSANDS to "दस हज़ार",
        NumberScale.HUNDRED_THOUSANDS to "लाख",
        NumberScale.MILLIONS to "दस लाख",
        NumberScale.TEN_MILLIONS to "करोड़",
        NumberScale.HUNDRED_MILLIONS to "दस करोड़",
        NumberScale.BILLIONS to "अरब",
        NumberScale.TEN_BILLIONS to "दस अरब",
        NumberScale.HUNDRED_BILLIONS to "खरब",
        NumberScale.TRILLIONS to "दस खरब",
        NumberScale.TEN_TRILLIONS to "नील",
        NumberScale.HUNDRED_TRILLIONS to "दस नील",
        NumberScale.QUADRILLIONS to "पद्म",
        NumberScale.QUINTILLIONS to "शंख"
    ),
    // Arabic
    "ar" to mapOf(
        NumberScale.ONES to "آحاد",
        NumberScale.TENS to "عشرات",
        NumberScale.HUNDREDS to "مئات",
        NumberScale.THOUSANDS to "ألف",
        NumberScale.TEN_THOUSANDS to "عشرة آلاف",
        NumberScale.HUNDRED_THOUSANDS to "مائة ألف",
        NumberScale.MILLIONS to "مليون",
        NumberScale.TEN_MILLIONS to "عشرة ملايين",
        NumberScale.HUNDRED_MILLIONS to "مائة مليون",
        NumberScale.BILLIONS to "مليار",
        NumberScale.TEN_BILLIONS to "عشرة مليارات",
        NumberScale.HUNDRED_BILLIONS to "مائة مليار",
        NumberScale.TRILLIONS to "تريليون",
        NumberScale.TEN_TRILLIONS to "عشرة تريليونات",
        NumberScale.HUNDRED_TRILLIONS to "مائة تريليون",
        NumberScale.QUADRILLIONS to "كوادريليون",
        NumberScale.QUINTILLIONS to "كوينتليون"
    ),
    // Russian
    "ru" to mapOf(
        NumberScale.ONES to "единицы",
        NumberScale.TENS to "десятки",
        NumberScale.HUNDREDS to "сотни",
        NumberScale.THOUSANDS to "тысяча",
        NumberScale.TEN_THOUSANDS to "десять тысяч",
        NumberScale.HUNDRED_THOUSANDS to "сто тысяч",
        NumberScale.MILLIONS to "миллион",
        NumberScale.TEN_MILLIONS to "десять миллионов",
        NumberScale.HUNDRED_MILLIONS to "сто миллионов",
        NumberScale.BILLIONS to "миллиард",
        NumberScale.TEN_BILLIONS to "десять миллиардов",
        NumberScale.HUNDRED_BILLIONS to "сто миллиардов",
        NumberScale.TRILLIONS to "триллион",
        NumberScale.TEN_TRILLIONS to "десять триллионов",
        NumberScale.HUNDRED_TRILLIONS to "сто триллионов",
        NumberScale.QUADRILLIONS to "квадриллион",
        NumberScale.QUINTILLIONS to "квинтиллион"
    ),
    // Italian
    "it" to mapOf(
        NumberScale.ONES to "unità",
        NumberScale.TENS to "decine",
        NumberScale.HUNDREDS to "centinaia",
        NumberScale.THOUSANDS to "mille",
        NumberScale.TEN_THOUSANDS to "diecimila",
        NumberScale.HUNDRED_THOUSANDS to "centomila",
        NumberScale.MILLIONS to "milione",
        NumberScale.TEN_MILLIONS to "dieci milioni",
        NumberScale.HUNDRED_MILLIONS to "cento milioni",
        NumberScale.BILLIONS to "miliardo",
        NumberScale.TEN_BILLIONS to "dieci miliardi",
        NumberScale.HUNDRED_BILLIONS to "cento miliardi",
        NumberScale.TRILLIONS to "bilione",
        NumberScale.TEN_TRILLIONS to "dieci bilioni",
        NumberScale.HUNDRED_TRILLIONS to "cento bilioni",
        NumberScale.QUADRILLIONS to "biliardo",
        NumberScale.QUINTILLIONS to "trilione"
    ),
    // Portuguese
    "pt" to mapOf(
        NumberScale.ONES to "unidades",
        NumberScale.TENS to "dezenas",
        NumberScale.HUNDREDS to "centenas",
        NumberScale.THOUSANDS to "mil",
        NumberScale.TEN_THOUSANDS to "dez mil",
        NumberScale.HUNDRED_THOUSANDS to "cem mil",
        NumberScale.MILLIONS to "milhão",
        NumberScale.TEN_MILLIONS to "dez milhões",
        NumberScale.HUNDRED_MILLIONS to "cem milhões",
        NumberScale.BILLIONS to "bilhão",
        NumberScale.TEN_BILLIONS to "dez bilhões",
        NumberScale.HUNDRED_BILLIONS to "cem bilhões",
        NumberScale.TRILLIONS to "trilhão",
        NumberScale.TEN_TRILLIONS to "dez trilhões",
        NumberScale.HUNDRED_TRILLIONS to "cem trilhões",
        NumberScale.QUADRILLIONS to "quatrilhão",
        NumberScale.QUINTILLIONS to "quintilhão"
    ),
    // Korean
    "ko" to mapOf(
        NumberScale.ONES to "일",
        NumberScale.TENS to "십",
        NumberScale.HUNDREDS to "백",
        NumberScale.THOUSANDS to "천",
        NumberScale.TEN_THOUSANDS to "만",
        NumberScale.HUNDRED_THOUSANDS to "십만",
        NumberScale.MILLIONS to "백만",
        NumberScale.TEN_MILLIONS to "천만",
        NumberScale.HUNDRED_MILLIONS to "억",
        NumberScale.BILLIONS to "십억",
        NumberScale.TEN_BILLIONS to "백억",
        NumberScale.HUNDRED_BILLIONS to "천억",
        NumberScale.TRILLIONS to "조",
        NumberScale.TEN_TRILLIONS to "십조",
        NumberScale.HUNDRED_TRILLIONS to "백조",
        NumberScale.QUADRILLIONS to "천조",
        NumberScale.QUINTILLIONS to "경"
    )
)

/**
 * Gets the localized name for a number scale
 */
fun getLocalizedScaleName(scale: NumberScale, locale: Locale): String {
    val languageCode = locale.language
    return scaleTranslations[languageCode]?.get(scale) ?: scale.englishName
}
