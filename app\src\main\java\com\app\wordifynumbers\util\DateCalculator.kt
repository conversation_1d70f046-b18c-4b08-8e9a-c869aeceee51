package com.app.wordifynumbers.util

import android.os.Build
import androidx.annotation.RequiresApi
import java.time.LocalDate
import java.time.Period
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.Locale

/**
 * Utility class for date calculations
 * Provides methods for calculating date differences, adding/subtracting days,
 * and formatting dates according to locale standards
 */
object DateCalculator {
    /**
     * Calculates the difference between two dates
     * 
     * @param date1 The first date
     * @param date2 The second date
     * @return A Period representing the difference between the dates
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun calculateDateDifference(date1: LocalDate, date2: LocalDate): Period {
        return Period.between(date1, date2)
    }
    
    /**
     * Calculates the total days between two dates
     * 
     * @param date1 The first date
     * @param date2 The second date
     * @return The number of days between the dates (absolute value)
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun calculateDaysBetween(date1: LocalDate, date2: LocalDate): Long {
        return kotlin.math.abs(ChronoUnit.DAYS.between(date1, date2))
    }
    
    /**
     * Adds a specified number of days to a date
     * 
     * @param date The starting date
     * @param days The number of days to add (can be negative to subtract)
     * @return The resulting date
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun addDays(date: LocalDate, days: Long): LocalDate {
        return date.plusDays(days)
    }
    
    /**
     * Adds a specified number of months to a date
     * 
     * @param date The starting date
     * @param months The number of months to add (can be negative to subtract)
     * @return The resulting date
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun addMonths(date: LocalDate, months: Long): LocalDate {
        return date.plusMonths(months)
    }
    
    /**
     * Adds a specified number of years to a date
     * 
     * @param date The starting date
     * @param years The number of years to add (can be negative to subtract)
     * @return The resulting date
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun addYears(date: LocalDate, years: Long): LocalDate {
        return date.plusYears(years)
    }
    
    /**
     * Formats a date according to the specified pattern and locale
     * 
     * @param date The date to format
     * @param pattern The date pattern (e.g., "dd/MM/yyyy")
     * @param locale The locale to use for formatting
     * @return The formatted date string
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun formatDate(date: LocalDate, pattern: String = "dd/MM/yyyy", locale: Locale = Locale.getDefault()): String {
        val formatter = DateTimeFormatter.ofPattern(pattern, locale)
        return date.format(formatter)
    }
    
    /**
     * Returns a human-readable representation of a period
     * 
     * @param period The period to format
     * @return A human-readable string (e.g., "2 years, 3 months, 5 days")
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun formatPeriod(period: Period): String {
        val years = period.years
        val months = period.months
        val days = period.days
        
        val parts = mutableListOf<String>()
        
        if (years != 0) {
            parts.add("$years ${if (years == 1) "year" else "years"}")
        }
        
        if (months != 0) {
            parts.add("$months ${if (months == 1) "month" else "months"}")
        }
        
        if (days != 0 || parts.isEmpty()) {
            parts.add("$days ${if (days == 1) "day" else "days"}")
        }
        
        return parts.joinToString(", ")
    }
    
    /**
     * Calculates age from a birth date
     * 
     * @param birthDate The birth date
     * @param currentDate The reference date (defaults to today)
     * @return The age in years
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun calculateAge(birthDate: LocalDate, currentDate: LocalDate = LocalDate.now()): Int {
        return Period.between(birthDate, currentDate).years
    }
    
    /**
     * Checks if a year is a leap year
     * 
     * @param year The year to check
     * @return True if the year is a leap year, false otherwise
     */
    fun isLeapYear(year: Int): Boolean {
        return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)
    }
    
    /**
     * Gets the day of week name for a given date
     * 
     * @param date The date
     * @param locale The locale to use for the day name
     * @return The name of the day of week
     */
    @RequiresApi(Build.VERSION_CODES.O)
    fun getDayOfWeekName(date: LocalDate, locale: Locale = Locale.getDefault()): String {
        val formatter = DateTimeFormatter.ofPattern("EEEE", locale)
        return date.format(formatter)
    }
}