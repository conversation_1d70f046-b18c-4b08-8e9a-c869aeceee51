package com.app.wordifynumbers.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.theme.*

/**
 * A standardized footer component for all screens in the app.
 * 
 * @param modifier Modifier for the footer
 * @param accentColor The accent color for the footer (defaults to NeonGlow)
 * @param showAppInfo Whether to show app info (version, etc.)
 * @param appVersion The app version to display
 * @param additionalContent Optional additional content to display in the footer
 */
@Composable
fun AppFooter(
    modifier: Modifier = Modifier,
    accentColor: Color = NeonGlow,
    showAppInfo: Boolean = true,
    appVersion: String = "1.0.0",
    additionalContent: @Composable (() -> Unit)? = null
) {
    // Animated effects
    val infiniteTransition = rememberInfiniteTransition(label = "footerAnimation")
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 8.dp)
            .shadow(
                elevation = 8.dp,
                spotColor = accentColor.copy(alpha = 0.15f),
                ambientColor = accentColor.copy(alpha = 0.05f),
                shape = RoundedCornerShape(12.dp)
            )
            .clip(RoundedCornerShape(12.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        NeonCard.copy(alpha = 0.8f),
                        NeonCard.copy(alpha = 0.7f)
                    )
                )
            )
            .drawBehind {
                // Bottom glow line
                drawLine(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color.Transparent,
                            accentColor.copy(alpha = glowAlpha),
                            Color.Transparent
                        )
                    ),
                    start = Offset(size.width * 0.2f, size.height),
                    end = Offset(size.width * 0.8f, size.height),
                    strokeWidth = 1.dp.toPx()
                )
            }
            .padding(vertical = 8.dp, horizontal = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // App branding
            Row(
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "Wordify Numbers",
                    style = MaterialTheme.typography.labelLarge.copy(
                        fontWeight = FontWeight.Medium,
                        letterSpacing = 0.5.sp
                    ),
                    color = accentColor,
                    textAlign = TextAlign.Center
                )
            }
            
            // Additional content if provided
            if (additionalContent != null) {
                additionalContent()
            }
            
            // App info if enabled
            if (showAppInfo) {
                Text(
                    text = "Version $appVersion",
                    style = MaterialTheme.typography.bodySmall,
                    color = NeonText.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}
