package com.app.wordifynumbers.ui.components

import androidx.compose.foundation.layout.size
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.app.wordifynumbers.ui.theme.NeonBackground

/**
 * A header action button component for use in the ModernScreenWrapper header.
 * 
 * @param icon The icon to display in the button
 * @param contentDescription The content description for accessibility
 * @param onClick The callback to invoke when the button is clicked
 * @param accentColor The accent color for the button
 * @param modifier The modifier to apply to the button
 */
@Composable
fun HeaderActionButton(
    icon: ImageVector,
    contentDescription: String?,
    onClick: () -> Unit,
    accentColor: Color,
    modifier: Modifier = Modifier
) {
    FilledIconButton(
        onClick = onClick,
        modifier = modifier.size(40.dp),
        colors = IconButtonDefaults.filledIconButtonColors(
            containerColor = accentColor.copy(alpha = 0.15f),
            contentColor = accentColor
        )
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = accentColor,
            modifier = Modifier.size(20.dp)
        )
    }
}
